'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  BookOpen01Icon as BookOpenIcon,
  ChartIcon as BarChartIcon,
  Award01Icon as TrophyIcon,
  Award01Icon as AwardIcon,
  Calendar01Icon as CalendarIcon,
  Building02Icon as BuildingIcon,
  ArrowLeft01Icon as ArrowLeftIcon
} from 'hugeicons-react';
import Link from 'next/link';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Course, Quiz } from '@/types/lms';
import { useEnrollment } from '@/contexts/enrollment-context';
import {
  QuizModal,
  QuizIntroduction,
  CourseTab,
  ProgressTab,
  ExamTab,
  CertificateTab,
  TableOfContents
} from '@/components/lms';

const CourseLearningPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const courseId = params.courseId as string;
  const { courseData: defaultCourseData, updateCourseProgress, getCourseById } = useEnrollment();

  // Get the specific course based on courseId from URL
  const courseData = getCourseById(courseId) || defaultCourseData;

  const [expandedContents, setExpandedContents] = useState<{
    [key: string]: boolean;
  }>({});
  const [expandedModules, setExpandedModules] = useState<{
    [key: string]: boolean;
  }>({});
  const [expandedChapters, setExpandedChapters] = useState<{
    [key: string]: boolean;
  }>({});
  const [currentQuiz, setCurrentQuiz] = useState<Quiz | null>(null);
  const [showQuizIntro, setShowQuizIntro] = useState(false);
  const [quizType, setQuizType] = useState<'chapter' | 'module' | 'final'>('chapter');
  const [showCertificate, setShowCertificate] = useState(false);
  const [activeTab, setActiveTab] = useState('course');
  const [isLearningMode, setIsLearningMode] = useState(false);
  const [currentModuleId, setCurrentModuleId] = useState<string>('');
  const [currentChapterId, setCurrentChapterId] = useState<string>('');
  const [currentContentId, setCurrentContentId] = useState<string>('');

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab) {
      setActiveTab(tab);
    }
    
    // Check if we're coming from course material (learning mode)
    const fromMaterial = searchParams.get('from') === 'material';
    setIsLearningMode(fromMaterial);
  }, [searchParams]);

  const toggleContent = useCallback((contentId: string) => {
    setExpandedContents((prev) => ({ ...prev, [contentId]: !prev[contentId] }));
  }, []);

  const toggleModule = useCallback((moduleId: string) => {
    setExpandedModules((prev) => ({ ...prev, [moduleId]: !prev[moduleId] }));
  }, []);

  const toggleChapter = useCallback((chapterId: string) => {
    setExpandedChapters((prev) => ({ ...prev, [chapterId]: !prev[chapterId] }));
  }, []);

  const expandAllModules = useCallback(() => {
    const newExpandedModules: { [key: string]: boolean } = {};
    courseData.modules.forEach((module) => {
      if (module.isUnlocked) {
        newExpandedModules[module.id] = true;
      }
    });
    setExpandedModules(newExpandedModules);
  }, [courseData.modules]);

  const collapseAllModules = useCallback(() => {
    setExpandedModules({});
  }, []);

  const expandAllChaptersInModule = useCallback(
    (moduleId: string) => {
      const courseModule = courseData.modules.find((m) => m.id === moduleId);
      if (!courseModule) return;

      const newExpandedChapters = { ...expandedChapters };
      courseModule.chapters.forEach((chapter) => {
        if (chapter.isUnlocked) {
          newExpandedChapters[chapter.id] = true;
        }
      });
      setExpandedChapters(newExpandedChapters);
    },
    [courseData.modules, expandedChapters]
  );

  const collapseAllChaptersInModule = useCallback(
    (moduleId: string) => {
      const courseModule = courseData.modules.find((m) => m.id === moduleId);
      if (!courseModule) return;

      setExpandedModules((prev) => ({ ...prev, [moduleId]: false }));

      const newExpandedContents = { ...expandedContents };
      const newExpandedChapters = { ...expandedChapters };
      courseModule.chapters.forEach((chapter) => {
        delete newExpandedChapters[chapter.id];
      });
      setExpandedChapters(newExpandedChapters);
    },
    [courseData.modules, expandedChapters]
  );

  const toggleContentComplete = useCallback(
    (contentId: string) => {
      const newCourse = JSON.parse(JSON.stringify(courseData)) as Course;

      let contentFound = false;
      for (const courseModule of newCourse.modules) {
        for (const chapter of courseModule.chapters) {
          const content = chapter.contents.find((c) => c.id === contentId);
          if (content) {
            content.isCompleted = !content.isCompleted;
            contentFound = true;

            const completedContents = chapter.contents.filter(
              (c) => c.isCompleted
            ).length;
            const nextChapterIndex = chapter.order;
            const nextChapter = courseModule.chapters.find(
              (ch) => ch.order === nextChapterIndex + 1
            );

            if (
              nextChapter &&
              completedContents === chapter.contents.length &&
              chapter.quiz.isPassed
            ) {
              nextChapter.isUnlocked = true;
            }

            const allChaptersCompleted = courseModule.chapters.every(
              (ch) =>
                ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
            );

            if (allChaptersCompleted && courseModule.moduleQuiz.isPassed) {
              const nextModuleIndex = courseModule.order;
              const nextModule = newCourse.modules.find(
                (m) => m.order === nextModuleIndex + 1
              );
              if (nextModule) {
                nextModule.isUnlocked = true;
                if (nextModule.chapters.length > 0) {
                  nextModule.chapters[0].isUnlocked = true;
                }
              }
            }

            break;
          }
        }
        if (contentFound) break;
      }

      updateCourseProgress(newCourse);
    },
    [courseData, updateCourseProgress]
  );

  const showQuizIntroduction = useCallback(
    (quizId: string, type: 'chapter' | 'module' | 'final') => {
      let quiz: Quiz | undefined;

      // Check if it's the final exam
      if (courseData.finalExam.id === quizId) {
        // Navigate to dedicated exam page for final exam
        router.push(`/my-courses/${courseId}/exam?type=final&examId=${quizId}`);
        return;
      }

      // Check module and chapter quizzes
      for (const courseModule of courseData.modules) {
        for (const chapter of courseModule.chapters) {
          if (chapter.quiz.id === quizId) {
            quiz = chapter.quiz;
            setCurrentQuiz({ ...quiz });
            setQuizType('chapter');
            setShowQuizIntro(true);
            return;
          }
        }
        if (courseModule.moduleQuiz.id === quizId) {
          quiz = courseModule.moduleQuiz;
          setCurrentQuiz({ ...quiz });
          setQuizType('module');
          setShowQuizIntro(true);
          return;
        }
      }
    },
    [courseData, courseId, router]
  );

  const startQuiz = useCallback(
    (quizId: string) => {
      let quiz: Quiz | undefined;

      // Check if it's the final exam
      if (courseData.finalExam.id === quizId) {
        // Navigate to dedicated exam page for final exam
        router.push(`/my-courses/${courseId}/exam?type=final&examId=${quizId}`);
        return;
      }

      // Check module and chapter quizzes
      for (const courseModule of courseData.modules) {
        for (const chapter of courseModule.chapters) {
          if (chapter.quiz.id === quizId) {
            quiz = chapter.quiz;
            setCurrentQuiz({ ...quiz });
            setShowQuizIntro(false); // Hide intro and start quiz directly
            return;
          }
        }
        if (courseModule.moduleQuiz.id === quizId) {
          quiz = courseModule.moduleQuiz;
          setCurrentQuiz({ ...quiz });
          setShowQuizIntro(false); // Hide intro and start quiz directly
          return;
        }
      }
    },
    [courseData, courseId, router]
  );

  const handleQuizComplete = useCallback(
    (score: number) => {
      if (!currentQuiz) return;

      const newCourse = JSON.parse(JSON.stringify(courseData)) as Course;

      const updateQuiz = (quiz: Quiz) => {
        quiz.attempts += 1;
        quiz.lastScore = score;
        quiz.isPassed = score >= quiz.minimumScore;
      };

      for (const courseModule of newCourse.modules) {
        for (const chapter of courseModule.chapters) {
          if (chapter.quiz.id === currentQuiz.id) {
            updateQuiz(chapter.quiz);

            const allContentsCompleted = chapter.contents.every(
              (c) => c.isCompleted
            );
            if ((chapter.quiz.isPassed || chapter.quiz.attempts >= chapter.quiz.maxAttempts) && allContentsCompleted) {
              const nextChapter = courseModule.chapters.find(
                (ch) => ch.order === chapter.order + 1
              );
              if (nextChapter) {
                nextChapter.isUnlocked = true;
              }
            }
            break;
          }
        }

        if (courseModule.moduleQuiz.id === currentQuiz.id) {
          updateQuiz(courseModule.moduleQuiz);

          const allChaptersCompleted = courseModule.chapters.every(
            (ch) => ch.contents.every((c) => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)
          );
          if ((courseModule.moduleQuiz.isPassed || courseModule.moduleQuiz.attempts >= courseModule.moduleQuiz.maxAttempts) && allChaptersCompleted) {
            const nextModule = newCourse.modules.find(
              (m) => m.order === courseModule.order + 1
            );
            if (nextModule) {
              nextModule.isUnlocked = true;
              if (nextModule.chapters.length > 0) {
                nextModule.chapters[0].isUnlocked = true;
              }
            }
          }
        }
      }

      if (newCourse.finalExam.id === currentQuiz.id) {
        updateQuiz(newCourse.finalExam);

        const allModulesCompleted = newCourse.modules.every(
          (m) =>
            m.chapters.every(
              (ch) =>
                ch.contents.every((c) => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)
            ) && (m.moduleQuiz.isPassed || m.moduleQuiz.attempts >= m.moduleQuiz.maxAttempts)
        );

        if (newCourse.finalExam.isPassed && allModulesCompleted) {
          newCourse.certificate.isEligible = true;
          newCourse.certificate.completionDate = new Date()
            .toISOString()
            .split('T')[0];
          newCourse.status = 'completed';
        }
      }

      updateCourseProgress(newCourse);
      setCurrentQuiz(null);
    },
    [currentQuiz, courseData, updateCourseProgress]
  );

  const handleNavigateToSection = useCallback(
    (moduleId: string, chapterId?: string, contentId?: string) => {
      setActiveTab('course');

      // Set current content for single content view
      setCurrentModuleId(moduleId);
      if (chapterId) {
        setCurrentChapterId(chapterId);
      }
      if (contentId) {
        setCurrentContentId(contentId);
      }

      setExpandedModules((prev) => ({ ...prev, [moduleId]: true }));
      if (chapterId) {
        setExpandedChapters((prev) => ({ ...prev, [chapterId]: true }));
      }

      if (contentId) {
        setExpandedContents((prev) => ({ ...prev, [contentId]: true }));
      }
    },
    []
  );

  const completedChapters = courseData.modules.reduce(
    (total, module) =>
      total +
      module.chapters.filter(
        (ch) => ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
      ).length,
    0
  );

  const totalChapters = courseData.modules.reduce(
    (total, module) => total + module.chapters.length,
    0
  );
  const overallProgress =
    totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0;

  const navigationItems = [
    { id: 'course', label: 'Konten Kursus', icon: BookOpenIcon },
    { id: 'progress', label: 'Kemajuan', icon: BarChartIcon },
    { id: 'exam', label: 'Final Exam', icon: TrophyIcon },
    { id: 'certificate', label: 'Sertifikat', icon: AwardIcon }
  ];

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* Main Content Layout */}
      <div className='flex h-screen'>
        {/* Left Navigation Panel */}
        <div className='w-96 bg-white border-r flex-shrink-0'>
          <div className='p-6'>
            {/* Course Info Header */}
            <div className='mb-6'>
              <div className='flex items-center gap-2 mb-3'>
                <Link href={`/my-courses/${courseId}`}>
                  <Button variant='ghost' size='sm' className='p-1'>
                    <ArrowLeftIcon className='h-4 w-4' />
                  </Button>
                </Link>
                <BuildingIcon className='h-6 w-6 text-[var(--iai-primary)]' />
                <div>
                  <h1 className='text-lg font-bold text-gray-900'>
                    {courseData.name}
                  </h1>
                  <p className='text-sm text-gray-600'>{courseData.code}</p>
                </div>
              </div>
              <div className='space-y-2'>
                <div className='flex items-center justify-between text-sm'>
                  <span className='text-gray-500'>Kemajuan</span>
                  <span className='font-medium'>{Math.round(overallProgress)}%</span>
                </div>
                <Progress value={overallProgress} className='h-2' />
                <div className='flex items-center justify-between text-sm'>
                  <span className='text-gray-500'>Instruktur</span>
                  <span>{courseData.instructor}</span>
                </div>
                <div className='flex items-center justify-between text-sm'>
                  <span className='text-gray-500'>Deadline</span>
                  <span>{courseData.endDate}</span>
                </div>
                <Badge
                  variant={
                    courseData.status === 'completed'
                      ? 'default'
                      : 'secondary'
                  }
                  className='w-full justify-center'
                >
                  {courseData.status === 'completed'
                    ? 'Selesai'
                    : 'Sedang Belajar'}
                </Badge>
              </div>
            </div>

            {isLearningMode ? (
              <TableOfContents
                course={courseData}
                onNavigate={handleNavigateToSection}
                onStartQuiz={showQuizIntroduction}
                expandedModules={expandedModules}
                expandedChapters={expandedChapters}
                onToggleModule={toggleModule}
                onToggleChapter={toggleChapter}
                currentModuleIndex={0}
              />
            ) : (
              <>
                <h2 className='text-lg font-semibold text-gray-900 mb-4'>
                  Menu Pembelajaran
                </h2>
                <nav className='space-y-2'>
                  {navigationItems.map((item) => {
                    const Icon = item.icon;
                    return (
                      <button
                        key={item.id}
                        onClick={() => setActiveTab(item.id)}
                        className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                          activeTab === item.id
                            ? 'bg-blue-50 text-blue-700 border border-blue-200'
                            : 'text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <Icon className='h-4 w-4' />
                        <span className='text-sm font-medium'>{item.label}</span>
                      </button>
                    );
                  })}
                </nav>
              </>
            )}
          </div>
        </div>

        {/* Right Content Area */}
        <div className='flex-1 overflow-auto'>
          <div className='p-6 pb-8'>
            <div className='max-w-6xl'>
              <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
                <TabsContent value='course' className='mt-0'>
                  {showQuizIntro && currentQuiz ? (
                    <QuizIntroduction
                      quiz={currentQuiz}
                      quizType={quizType}
                      onStartQuiz={() => {
                        setShowQuizIntro(false);
                        startQuiz(currentQuiz.id);
                      }}
                      onCancel={() => {
                        setShowQuizIntro(false);
                        setCurrentQuiz(null);
                      }}
                    />
                  ) : (
                    <CourseTab
                      courseData={courseData}
                      expandedModules={expandedModules}
                      expandedChapters={expandedChapters}
                      expandedContents={expandedContents}
                      onToggleModule={toggleModule}
                      onToggleChapter={toggleChapter}
                      onToggleContent={toggleContent}
                      onToggleContentComplete={toggleContentComplete}
                      onStartQuiz={startQuiz}
                      onNavigateToSection={handleNavigateToSection}
                      onExpandAllModules={expandAllModules}
                      onCollapseAllModules={collapseAllModules}
                      onExpandAllChaptersInModule={expandAllChaptersInModule}
                      onCollapseAllChaptersInModule={collapseAllChaptersInModule}
                      onNavigateToFinalExam={() => setActiveTab('exam')}
                      isLearningMode={isLearningMode}
                      currentModuleId={currentModuleId}
                      currentChapterId={currentChapterId}
                      currentContentId={currentContentId}
                    />
                  )}
                </TabsContent>

                <TabsContent value='progress' className='mt-4'>
                  <ProgressTab
                    courseData={courseData}
                    overallProgress={overallProgress}
                  />
                </TabsContent>

                <TabsContent value='exam' className='mt-4'>
                  <ExamTab courseData={courseData} onStartQuiz={startQuiz} />
                </TabsContent>

                <TabsContent value='certificate' className='mt-4'>
                  <CertificateTab
                    courseData={courseData}
                    institution={{
                      id: 'iai-indonesia',
                      name: 'Indonesian Institute of Architects',
                      shortName: 'IAI',
                      website: 'https://iai.or.id',
                      certificateTemplate: {
                        primaryColor: '#1e40af',
                        secondaryColor: '#f59e0b',
                        signatoryName: 'Ar. Georgius Budi Yulianto, IAI, AA',
                        signatoryTitle: 'Ketua Umum IAI 2024-2027'
                      }
                    }}
                    overallProgress={overallProgress}
                    onGenerateCertificate={() => setShowCertificate(true)}
                  />
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </div>

      {/* Quiz Modal - Only for chapter and module quizzes */}
      {currentQuiz && (
        <QuizModal
          quiz={currentQuiz}
          isOpen={true}
          onComplete={handleQuizComplete}
          onClose={() => setCurrentQuiz(null)}
        />
      )}
    </div>
  );
};

export default CourseLearningPage;
