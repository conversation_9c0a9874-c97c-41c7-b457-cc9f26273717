/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
      --tw-animation-delay: 0s;
      --tw-animation-direction: normal;
      --tw-animation-duration: initial;
      --tw-animation-fill-mode: none;
      --tw-animation-iteration-count: 1;
      --tw-enter-opacity: 1;
      --tw-enter-rotate: 0;
      --tw-enter-scale: 1;
      --tw-enter-translate-x: 0;
      --tw-enter-translate-y: 0;
      --tw-exit-opacity: 1;
      --tw-exit-rotate: 0;
      --tw-exit-scale: 1;
      --tw-exit-translate-x: 0;
      --tw-exit-translate-y: 0;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-red-50: oklch(97.1% .013 17.38);
    --color-red-100: oklch(93.6% .032 17.717);
    --color-red-200: oklch(88.5% .062 18.334);
    --color-red-300: oklch(80.8% .114 19.571);
    --color-red-400: oklch(70.4% .191 22.216);
    --color-red-500: oklch(63.7% .237 25.331);
    --color-red-600: oklch(57.7% .245 27.325);
    --color-red-700: oklch(50.5% .213 27.518);
    --color-red-800: oklch(44.4% .177 26.899);
    --color-orange-50: oklch(98% .016 73.684);
    --color-orange-100: oklch(95.4% .038 75.164);
    --color-orange-400: oklch(75% .183 55.934);
    --color-orange-600: oklch(64.6% .222 41.116);
    --color-orange-700: oklch(55.3% .195 38.402);
    --color-orange-800: oklch(47% .157 37.304);
    --color-amber-50: oklch(98.7% .022 95.277);
    --color-amber-200: oklch(92.4% .12 95.746);
    --color-amber-500: oklch(76.9% .188 70.08);
    --color-amber-600: oklch(66.6% .179 58.318);
    --color-amber-700: oklch(55.5% .163 48.998);
    --color-amber-800: oklch(47.3% .137 46.201);
    --color-yellow-50: oklch(98.7% .026 102.212);
    --color-yellow-100: oklch(97.3% .071 103.193);
    --color-yellow-200: oklch(94.5% .129 101.54);
    --color-yellow-300: oklch(90.5% .182 98.111);
    --color-yellow-400: oklch(85.2% .199 91.936);
    --color-yellow-500: oklch(79.5% .184 86.047);
    --color-yellow-600: oklch(68.1% .162 75.834);
    --color-yellow-700: oklch(55.4% .135 66.442);
    --color-yellow-800: oklch(47.6% .114 61.907);
    --color-lime-50: oklch(98.6% .031 120.757);
    --color-lime-600: oklch(64.8% .2 131.684);
    --color-green-50: oklch(98.2% .018 155.826);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-200: oklch(92.5% .084 155.995);
    --color-green-300: oklch(87.1% .15 154.449);
    --color-green-400: oklch(79.2% .209 151.711);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-700: oklch(52.7% .154 150.069);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-green-900: oklch(39.3% .095 152.535);
    --color-sky-50: oklch(97.7% .013 236.62);
    --color-sky-300: oklch(82.8% .111 230.318);
    --color-sky-600: oklch(58.8% .158 241.966);
    --color-sky-900: oklch(39.1% .09 240.876);
    --color-blue-50: oklch(97% .014 254.604);
    --color-blue-100: oklch(93.2% .032 255.585);
    --color-blue-200: oklch(88.2% .059 254.128);
    --color-blue-300: oklch(80.9% .105 251.813);
    --color-blue-400: oklch(70.7% .165 254.624);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-blue-800: oklch(42.4% .199 265.638);
    --color-blue-900: oklch(37.9% .146 265.522);
    --color-indigo-50: oklch(96.2% .018 272.314);
    --color-indigo-100: oklch(93% .034 272.788);
    --color-indigo-600: oklch(51.1% .262 276.966);
    --color-purple-50: oklch(97.7% .014 308.299);
    --color-purple-100: oklch(94.6% .033 307.174);
    --color-purple-200: oklch(90.2% .063 306.703);
    --color-purple-400: oklch(71.4% .203 305.504);
    --color-purple-600: oklch(55.8% .288 302.321);
    --color-purple-700: oklch(49.6% .265 301.924);
    --color-purple-800: oklch(43.8% .218 303.724);
    --color-slate-50: oklch(98.4% .003 247.858);
    --color-slate-800: oklch(27.9% .041 260.031);
    --color-slate-900: oklch(20.8% .042 265.755);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-neutral-50: oklch(98.5% 0 0);
    --color-neutral-500: oklch(55.6% 0 0);
    --color-neutral-600: oklch(43.9% 0 0);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;
    --tracking-tight: -.025em;
    --tracking-wide: .025em;
    --tracking-widest: .1em;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-xs: .125rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --drop-shadow-lg: 0 4px 4px #00000026;
    --ease-out: cubic-bezier(0, 0, .2, 1);
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-ping: ping 1s cubic-bezier(0, 0, .2, 1) infinite;
    --animate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite;
    --animate-bounce: bounce 1s infinite;
    --blur-sm: 8px;
    --blur-md: 12px;
    --aspect-video: 16 / 9;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --color-border: var(--border);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }

  * {
    border-color: var(--border);
    outline-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    * {
      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}

@layer components;

@layer utilities {
  .\@container\/card {
    container: card / inline-size;
  }

  .\@container\/card-header {
    container: card-header / inline-size;
  }

  .pointer-events-none {
    pointer-events: none;
  }

  .invisible {
    visibility: hidden;
  }

  .visible {
    visibility: visible;
  }

  .sr-only {
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    position: absolute;
    overflow: hidden;
  }

  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .static {
    position: static;
  }

  .sticky {
    position: sticky;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .inset-x-0 {
    inset-inline: calc(var(--spacing) * 0);
  }

  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }

  .-top-0\.5 {
    top: calc(var(--spacing) * -.5);
  }

  .-top-1 {
    top: calc(var(--spacing) * -1);
  }

  .-top-4 {
    top: calc(var(--spacing) * -4);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1\.5 {
    top: calc(var(--spacing) * 1.5);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-2 {
    top: calc(var(--spacing) * 2);
  }

  .top-2\.5 {
    top: calc(var(--spacing) * 2.5);
  }

  .top-3 {
    top: calc(var(--spacing) * 3);
  }

  .top-3\.5 {
    top: calc(var(--spacing) * 3.5);
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-24 {
    top: calc(var(--spacing) * 24);
  }

  .top-\[0\.3rem\] {
    top: .3rem;
  }

  .top-\[1px\] {
    top: 1px;
  }

  .top-\[50\%\] {
    top: 50%;
  }

  .top-\[60\%\] {
    top: 60%;
  }

  .top-full {
    top: 100%;
  }

  .-right-0\.5 {
    right: calc(var(--spacing) * -.5);
  }

  .-right-1 {
    right: calc(var(--spacing) * -1);
  }

  .-right-4 {
    right: calc(var(--spacing) * -4);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-1 {
    right: calc(var(--spacing) * 1);
  }

  .right-2 {
    right: calc(var(--spacing) * 2);
  }

  .right-3 {
    right: calc(var(--spacing) * 3);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-8 {
    right: calc(var(--spacing) * 8);
  }

  .right-\[0\.3rem\] {
    right: .3rem;
  }

  .-bottom-1 {
    bottom: calc(var(--spacing) * -1);
  }

  .-bottom-4 {
    bottom: calc(var(--spacing) * -4);
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }

  .-left-4 {
    left: calc(var(--spacing) * -4);
  }

  .-left-8 {
    left: calc(var(--spacing) * -8);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-1 {
    left: calc(var(--spacing) * 1);
  }

  .left-1\/2 {
    left: 50%;
  }

  .left-2 {
    left: calc(var(--spacing) * 2);
  }

  .left-3 {
    left: calc(var(--spacing) * 3);
  }

  .left-\[50\%\] {
    left: 50%;
  }

  .isolate {
    isolation: isolate;
  }

  .z-5 {
    z-index: 5;
  }

  .z-10 {
    z-index: 10;
  }

  .z-20 {
    z-index: 20;
  }

  .z-50 {
    z-index: 50;
  }

  .z-99999 {
    z-index: 99999;
  }

  .z-\[-1\]\! {
    z-index: -1 !important;
  }

  .z-\[1\] {
    z-index: 1;
  }

  .col-span-4 {
    grid-column: span 4 / span 4;
  }

  .col-span-full {
    grid-column: 1 / -1;
  }

  .col-start-2 {
    grid-column-start: 2;
  }

  .row-span-2 {
    grid-row: span 2 / span 2;
  }

  .row-start-1 {
    grid-row-start: 1;
  }

  .container {
    width: 100%;
  }

  @media (width >= 40rem) {
    .container {
      max-width: 40rem;
    }
  }

  @media (width >= 48rem) {
    .container {
      max-width: 48rem;
    }
  }

  @media (width >= 64rem) {
    .container {
      max-width: 64rem;
    }
  }

  @media (width >= 80rem) {
    .container {
      max-width: 80rem;
    }
  }

  @media (width >= 96rem) {
    .container {
      max-width: 96rem;
    }
  }

  .-mx-1 {
    margin-inline: calc(var(--spacing) * -1);
  }

  .mx-0\.5 {
    margin-inline: calc(var(--spacing) * .5);
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-3\.5 {
    margin-inline: calc(var(--spacing) * 3.5);
  }

  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .my-0\.5 {
    margin-block: calc(var(--spacing) * .5);
  }

  .my-1 {
    margin-block: calc(var(--spacing) * 1);
  }

  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }

  .my-3 {
    margin-block: calc(var(--spacing) * 3);
  }

  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }

  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }

  .prose {
    color: var(--tw-prose-body);
    --tw-prose-body: oklch(37.3% .034 259.733);
    --tw-prose-headings: oklch(21% .034 264.665);
    --tw-prose-lead: oklch(44.6% .03 256.802);
    --tw-prose-links: oklch(21% .034 264.665);
    --tw-prose-bold: oklch(21% .034 264.665);
    --tw-prose-counters: oklch(55.1% .027 264.364);
    --tw-prose-bullets: oklch(87.2% .01 258.338);
    --tw-prose-hr: oklch(92.8% .006 264.531);
    --tw-prose-quotes: oklch(21% .034 264.665);
    --tw-prose-quote-borders: oklch(92.8% .006 264.531);
    --tw-prose-captions: oklch(55.1% .027 264.364);
    --tw-prose-kbd: oklch(21% .034 264.665);
    --tw-prose-kbd-shadows: NaN NaN NaN;
    --tw-prose-code: oklch(21% .034 264.665);
    --tw-prose-pre-code: oklch(92.8% .006 264.531);
    --tw-prose-pre-bg: oklch(27.8% .033 256.848);
    --tw-prose-th-borders: oklch(87.2% .01 258.338);
    --tw-prose-td-borders: oklch(92.8% .006 264.531);
    --tw-prose-invert-body: oklch(87.2% .01 258.338);
    --tw-prose-invert-headings: #fff;
    --tw-prose-invert-lead: oklch(70.7% .022 261.325);
    --tw-prose-invert-links: #fff;
    --tw-prose-invert-bold: #fff;
    --tw-prose-invert-counters: oklch(70.7% .022 261.325);
    --tw-prose-invert-bullets: oklch(44.6% .03 256.802);
    --tw-prose-invert-hr: oklch(37.3% .034 259.733);
    --tw-prose-invert-quotes: oklch(96.7% .003 264.542);
    --tw-prose-invert-quote-borders: oklch(37.3% .034 259.733);
    --tw-prose-invert-captions: oklch(70.7% .022 261.325);
    --tw-prose-invert-kbd: #fff;
    --tw-prose-invert-kbd-shadows: 255 255 255;
    --tw-prose-invert-code: #fff;
    --tw-prose-invert-pre-code: oklch(87.2% .01 258.338);
    --tw-prose-invert-pre-bg: #00000080;
    --tw-prose-invert-th-borders: oklch(44.6% .03 256.802);
    --tw-prose-invert-td-borders: oklch(37.3% .034 259.733);
    max-width: 65ch;
    font-size: 1rem;
    line-height: 1.75;
  }

  .prose :where(p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
  }

  .prose :where([class~="lead"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-lead);
    margin-top: 1.2em;
    margin-bottom: 1.2em;
    font-size: 1.25em;
    line-height: 1.6;
  }

  .prose :where(a):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-links);
    font-weight: 500;
    text-decoration: underline;
  }

  .prose :where(strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-bold);
    font-weight: 600;
  }

  .prose :where(a strong):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(blockquote strong):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(thead th strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
  }

  .prose :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
    padding-inline-start: 1.625em;
    list-style-type: decimal;
  }

  .prose :where(ol[type="A"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-alpha;
  }

  .prose :where(ol[type="a"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-alpha;
  }

  .prose :where(ol[type="A" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-alpha;
  }

  .prose :where(ol[type="a" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-alpha;
  }

  .prose :where(ol[type="I"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-roman;
  }

  .prose :where(ol[type="i"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-roman;
  }

  .prose :where(ol[type="I" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: upper-roman;
  }

  .prose :where(ol[type="i" s]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: lower-roman;
  }

  .prose :where(ol[type="1"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    list-style-type: decimal;
  }

  .prose :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
    padding-inline-start: 1.625em;
    list-style-type: disc;
  }

  .prose :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *))::marker {
    color: var(--tw-prose-counters);
    font-weight: 400;
  }

  .prose :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *))::marker {
    color: var(--tw-prose-bullets);
  }

  .prose :where(dt):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    margin-top: 1.25em;
    font-weight: 600;
  }

  .prose :where(hr):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-color: var(--tw-prose-hr);
    border-top-width: 1px;
    margin-top: 3em;
    margin-bottom: 3em;
  }

  .prose :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-quotes);
    border-inline-start-width: .25rem;
    border-inline-start-color: var(--tw-prose-quote-borders);
    quotes: "“""”""‘""’";
    margin-top: 1.6em;
    margin-bottom: 1.6em;
    padding-inline-start: 1em;
    font-style: italic;
    font-weight: 500;
  }

  .prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"], [class~="not-prose"] *)):before {
    content: open-quote;
  }

  .prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"], [class~="not-prose"] *)):after {
    content: close-quote;
  }

  .prose :where(h1):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    margin-top: 0;
    margin-bottom: .888889em;
    font-size: 2.25em;
    font-weight: 800;
    line-height: 1.11111;
  }

  .prose :where(h1 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-weight: 900;
  }

  .prose :where(h2):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    margin-top: 2em;
    margin-bottom: 1em;
    font-size: 1.5em;
    font-weight: 700;
    line-height: 1.33333;
  }

  .prose :where(h2 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-weight: 800;
  }

  .prose :where(h3):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    margin-top: 1.6em;
    margin-bottom: .6em;
    font-size: 1.25em;
    font-weight: 600;
    line-height: 1.6;
  }

  .prose :where(h3 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-weight: 700;
  }

  .prose :where(h4):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    margin-top: 1.5em;
    margin-bottom: .5em;
    font-weight: 600;
    line-height: 1.5;
  }

  .prose :where(h4 strong):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-weight: 700;
  }

  .prose :where(img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .prose :where(picture):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
    display: block;
  }

  .prose :where(video):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .prose :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-kbd);
    box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);
    padding-top: .1875em;
    padding-inline-end: .375em;
    padding-bottom: .1875em;
    border-radius: .3125rem;
    padding-inline-start: .375em;
    font-family: inherit;
    font-size: .875em;
    font-weight: 500;
  }

  .prose :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-code);
    font-size: .875em;
    font-weight: 600;
  }

  .prose :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)):before, .prose :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)):after {
    content: "`";
  }

  .prose :where(a code):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(h1 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
  }

  .prose :where(h2 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-size: .875em;
  }

  .prose :where(h3 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
    font-size: .9em;
  }

  .prose :where(h4 code):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(blockquote code):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(thead th code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: inherit;
  }

  .prose :where(pre):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-pre-code);
    background-color: var(--tw-prose-pre-bg);
    padding-top: .857143em;
    padding-inline-end: 1.14286em;
    padding-bottom: .857143em;
    border-radius: .375rem;
    margin-top: 1.71429em;
    margin-bottom: 1.71429em;
    padding-inline-start: 1.14286em;
    font-size: .875em;
    font-weight: 400;
    line-height: 1.71429;
    overflow-x: auto;
  }

  .prose :where(pre code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-weight: inherit;
    color: inherit;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    background-color: #0000;
    border-width: 0;
    border-radius: 0;
    padding: 0;
  }

  .prose :where(pre code):not(:where([class~="not-prose"], [class~="not-prose"] *)):before, .prose :where(pre code):not(:where([class~="not-prose"], [class~="not-prose"] *)):after {
    content: none;
  }

  .prose :where(table):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    table-layout: auto;
    width: 100%;
    margin-top: 2em;
    margin-bottom: 2em;
    font-size: .875em;
    line-height: 1.71429;
  }

  .prose :where(thead):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-bottom-width: 1px;
    border-bottom-color: var(--tw-prose-th-borders);
  }

  .prose :where(thead th):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-headings);
    vertical-align: bottom;
    padding-inline-end: .571429em;
    padding-bottom: .571429em;
    padding-inline-start: .571429em;
    font-weight: 600;
  }

  .prose :where(tbody tr):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-bottom-width: 1px;
    border-bottom-color: var(--tw-prose-td-borders);
  }

  .prose :where(tbody tr:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-bottom-width: 0;
  }

  .prose :where(tbody td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    vertical-align: baseline;
  }

  .prose :where(tfoot):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    border-top-width: 1px;
    border-top-color: var(--tw-prose-th-borders);
  }

  .prose :where(tfoot td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    vertical-align: top;
  }

  .prose :where(th, td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    text-align: start;
  }

  .prose :where(figure > *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose :where(figcaption):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    color: var(--tw-prose-captions);
    margin-top: .857143em;
    font-size: .875em;
    line-height: 1.42857;
  }

  .prose :where(picture > img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose :where(li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .5em;
    margin-bottom: .5em;
  }

  .prose :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-start: .375em;
  }

  .prose :where(.prose > ul > li p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .75em;
    margin-bottom: .75em;
  }

  .prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
  }

  .prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 1.25em;
  }

  .prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
  }

  .prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 1.25em;
  }

  .prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .75em;
    margin-bottom: .75em;
  }

  .prose :where(dl):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.25em;
    margin-bottom: 1.25em;
  }

  .prose :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .5em;
    padding-inline-start: 1.625em;
  }

  .prose :where(hr + *):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(h2 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(h3 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose :where(h4 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .prose :where(thead th:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .prose :where(thead th:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .prose :where(tbody td, tfoot td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-top: .571429em;
    padding-inline-end: .571429em;
    padding-bottom: .571429em;
    padding-inline-start: .571429em;
  }

  .prose :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .prose :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .prose :where(figure):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2em;
    margin-bottom: 2em;
  }

  .prose :where(.prose > :first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .prose :where(.prose > :last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 0;
  }

  .prose-sm {
    font-size: .875rem;
    line-height: 1.71429;
  }

  .prose-sm :where(p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.14286em;
    margin-bottom: 1.14286em;
  }

  .prose-sm :where([class~="lead"]):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .888889em;
    margin-bottom: .888889em;
    font-size: 1.28571em;
    line-height: 1.55556;
  }

  .prose-sm :where(blockquote):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.33333em;
    margin-bottom: 1.33333em;
    padding-inline-start: 1.11111em;
  }

  .prose-sm :where(h1):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: .8em;
    font-size: 2.14286em;
    line-height: 1.2;
  }

  .prose-sm :where(h2):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.6em;
    margin-bottom: .8em;
    font-size: 1.42857em;
    line-height: 1.4;
  }

  .prose-sm :where(h3):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.55556em;
    margin-bottom: .444444em;
    font-size: 1.28571em;
    line-height: 1.55556;
  }

  .prose-sm :where(h4):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.42857em;
    margin-bottom: .571429em;
    line-height: 1.42857;
  }

  .prose-sm :where(img):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose-sm :where(picture):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.71429em;
    margin-bottom: 1.71429em;
  }

  .prose-sm :where(picture > img):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose-sm :where(video):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.71429em;
    margin-bottom: 1.71429em;
  }

  .prose-sm :where(kbd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-top: .142857em;
    padding-inline-end: .357143em;
    padding-bottom: .142857em;
    border-radius: .3125rem;
    padding-inline-start: .357143em;
    font-size: .857143em;
  }

  .prose-sm :where(code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: .857143em;
  }

  .prose-sm :where(h2 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: .9em;
  }

  .prose-sm :where(h3 code):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: .888889em;
  }

  .prose-sm :where(pre):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-top: .666667em;
    padding-inline-end: 1em;
    padding-bottom: .666667em;
    border-radius: .25rem;
    margin-top: 1.66667em;
    margin-bottom: 1.66667em;
    padding-inline-start: 1em;
    font-size: .857143em;
    line-height: 1.66667;
  }

  .prose-sm :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose-sm :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.14286em;
    margin-bottom: 1.14286em;
    padding-inline-start: 1.57143em;
  }

  .prose-sm :where(li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .285714em;
    margin-bottom: .285714em;
  }

  .prose-sm :where(ol > li):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose-sm :where(ul > li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-start: .428571em;
  }

  .prose-sm :where(.prose-sm > ul > li p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .571429em;
    margin-bottom: .571429em;
  }

  .prose-sm :where(.prose-sm > ul > li > p:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.14286em;
  }

  .prose-sm :where(.prose-sm > ul > li > p:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 1.14286em;
  }

  .prose-sm :where(.prose-sm > ol > li > p:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.14286em;
  }

  .prose-sm :where(.prose-sm > ol > li > p:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 1.14286em;
  }

  .prose-sm :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .571429em;
    margin-bottom: .571429em;
  }

  .prose-sm :where(dl):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.14286em;
    margin-bottom: 1.14286em;
  }

  .prose-sm :where(dt):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.14286em;
  }

  .prose-sm :where(dd):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .285714em;
    padding-inline-start: 1.57143em;
  }

  .prose-sm :where(hr):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 2.85714em;
    margin-bottom: 2.85714em;
  }

  .prose-sm :where(hr + *):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose-sm :where(h2 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose-sm :where(h3 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)), .prose-sm :where(h4 + *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .prose-sm :where(table):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    font-size: .857143em;
    line-height: 1.5;
  }

  .prose-sm :where(thead th):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-end: 1em;
    padding-bottom: .666667em;
    padding-inline-start: 1em;
  }

  .prose-sm :where(thead th:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .prose-sm :where(thead th:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .prose-sm :where(tbody td, tfoot td):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-top: .666667em;
    padding-inline-end: 1em;
    padding-bottom: .666667em;
    padding-inline-start: 1em;
  }

  .prose-sm :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-start: 0;
  }

  .prose-sm :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    padding-inline-end: 0;
  }

  .prose-sm :where(figure):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 1.71429em;
    margin-bottom: 1.71429em;
  }

  .prose-sm :where(figure > *):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
    margin-bottom: 0;
  }

  .prose-sm :where(figcaption):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: .666667em;
    font-size: .857143em;
    line-height: 1.33333;
  }

  .prose-sm :where(.prose-sm > :first-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-top: 0;
  }

  .prose-sm :where(.prose-sm > :last-child):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
    margin-bottom: 0;
  }

  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }

  .mt-0\! {
    margin-top: calc(var(--spacing) * 0) !important;
  }

  .mt-0\.5 {
    margin-top: calc(var(--spacing) * .5);
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }

  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }

  .mt-64\! {
    margin-top: calc(var(--spacing) * 64) !important;
  }

  .mt-auto {
    margin-top: auto;
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mr-auto {
    margin-right: auto;
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .-ml-1 {
    margin-left: calc(var(--spacing) * -1);
  }

  .-ml-1\.5 {
    margin-left: calc(var(--spacing) * -1.5);
  }

  .-ml-2 {
    margin-left: calc(var(--spacing) * -2);
  }

  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }

  .ml-6 {
    margin-left: calc(var(--spacing) * 6);
  }

  .ml-auto {
    margin-left: auto;
  }

  .line-clamp-1 {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-3 {
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .block {
    display: block;
  }

  .contents {
    display: contents;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .table {
    display: table;
  }

  .table-caption {
    display: table-caption;
  }

  .table-cell {
    display: table-cell;
  }

  .table-row {
    display: table-row;
  }

  .field-sizing-content {
    field-sizing: content;
  }

  .aspect-\[4\/3\] {
    aspect-ratio: 4 / 3;
  }

  .aspect-auto {
    aspect-ratio: auto;
  }

  .aspect-square {
    aspect-ratio: 1;
  }

  .aspect-video {
    aspect-ratio: var(--aspect-video);
  }

  .size-2 {
    width: calc(var(--spacing) * 2);
    height: calc(var(--spacing) * 2);
  }

  .size-2\.5 {
    width: calc(var(--spacing) * 2.5);
    height: calc(var(--spacing) * 2.5);
  }

  .size-3 {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  .size-3\.5 {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .size-4 {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .size-7 {
    width: calc(var(--spacing) * 7);
    height: calc(var(--spacing) * 7);
  }

  .size-8 {
    width: calc(var(--spacing) * 8);
    height: calc(var(--spacing) * 8);
  }

  .size-9 {
    width: calc(var(--spacing) * 9);
    height: calc(var(--spacing) * 9);
  }

  .size-full {
    width: 100%;
    height: 100%;
  }

  .h-1 {
    height: calc(var(--spacing) * 1);
  }

  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-7 {
    height: calc(var(--spacing) * 7);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-9 {
    height: calc(var(--spacing) * 9);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-11 {
    height: calc(var(--spacing) * 11);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-24 {
    height: calc(var(--spacing) * 24);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-48 {
    height: calc(var(--spacing) * 48);
  }

  .h-52 {
    height: calc(var(--spacing) * 52);
  }

  .h-64 {
    height: calc(var(--spacing) * 64);
  }

  .h-96 {
    height: calc(var(--spacing) * 96);
  }

  .h-\[1\.15rem\] {
    height: 1.15rem;
  }

  .h-\[1px\] {
    height: 1px;
  }

  .h-\[75vh\] {
    height: 75vh;
  }

  .h-\[90vh\] {
    height: 90vh;
  }

  .h-\[250px\] {
    height: 250px;
  }

  .h-\[280px\] {
    height: 280px;
  }

  .h-\[300px\] {
    height: 300px;
  }

  .h-\[316px\] {
    height: 316px;
  }

  .h-\[400px\] {
    height: 400px;
  }

  .h-\[calc\(100dvh-52px\)\] {
    height: calc(100dvh - 52px);
  }

  .h-\[calc\(100vh-64px\)\] {
    height: calc(100vh - 64px);
  }

  .h-\[calc\(100vh-80px\)\] {
    height: calc(100vh - 80px);
  }

  .h-\[var\(--radix-navigation-menu-viewport-height\)\] {
    height: var(--radix-navigation-menu-viewport-height);
  }

  .h-\[var\(--radix-select-trigger-height\)\] {
    height: var(--radix-select-trigger-height);
  }

  .h-auto {
    height: auto;
  }

  .h-fit {
    height: fit-content;
  }

  .h-full {
    height: 100%;
  }

  .h-px {
    height: 1px;
  }

  .h-screen {
    height: 100vh;
  }

  .h-svh {
    height: 100svh;
  }

  .max-h-\(--radix-context-menu-content-available-height\) {
    max-height: var(--radix-context-menu-content-available-height);
  }

  .max-h-\(--radix-dropdown-menu-content-available-height\) {
    max-height: var(--radix-dropdown-menu-content-available-height);
  }

  .max-h-\(--radix-select-content-available-height\) {
    max-height: var(--radix-select-content-available-height);
  }

  .max-h-8 {
    max-height: calc(var(--spacing) * 8);
  }

  .max-h-16 {
    max-height: calc(var(--spacing) * 16);
  }

  .max-h-32 {
    max-height: calc(var(--spacing) * 32);
  }

  .max-h-48 {
    max-height: calc(var(--spacing) * 48);
  }

  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }

  .max-h-\[18\.75rem\] {
    max-height: 18.75rem;
  }

  .max-h-\[60vh\] {
    max-height: 60vh;
  }

  .max-h-\[70vh\] {
    max-height: 70vh;
  }

  .max-h-\[75vh\] {
    max-height: 75vh;
  }

  .max-h-\[80vh\] {
    max-height: 80vh;
  }

  .max-h-\[85vh\] {
    max-height: 85vh;
  }

  .max-h-\[90vh\] {
    max-height: 90vh;
  }

  .max-h-\[95vh\] {
    max-height: 95vh;
  }

  .max-h-\[300px\] {
    max-height: 300px;
  }

  .max-h-\[400px\] {
    max-height: 400px;
  }

  .max-h-\[calc\(100vh-16rem\)\] {
    max-height: calc(100vh - 16rem);
  }

  .max-h-full {
    max-height: 100%;
  }

  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }

  .min-h-4 {
    min-height: calc(var(--spacing) * 4);
  }

  .min-h-16 {
    min-height: calc(var(--spacing) * 16);
  }

  .min-h-\[32px\] {
    min-height: 32px;
  }

  .min-h-\[60px\] {
    min-height: 60px;
  }

  .min-h-\[200px\] {
    min-height: 200px;
  }

  .min-h-\[400px\] {
    min-height: 400px;
  }

  .min-h-\[calc\(100vh-200px\)\] {
    min-height: calc(100vh - 200px);
  }

  .min-h-full {
    min-height: 100%;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .min-h-svh {
    min-height: 100svh;
  }

  .w-\(--radix-dropdown-menu-trigger-width\) {
    width: var(--radix-dropdown-menu-trigger-width);
  }

  .w-\(--sidebar-width\) {
    width: var(--sidebar-width);
  }

  .w-0 {
    width: calc(var(--spacing) * 0);
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }

  .w-1\/2 {
    width: 50%;
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }

  .w-2\/3 {
    width: 66.6667%;
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-3\/4 {
    width: 75%;
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-7 {
    width: calc(var(--spacing) * 7);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-9 {
    width: calc(var(--spacing) * 9);
  }

  .w-10 {
    width: calc(var(--spacing) * 10);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-24 {
    width: calc(var(--spacing) * 24);
  }

  .w-28 {
    width: calc(var(--spacing) * 28);
  }

  .w-32 {
    width: calc(var(--spacing) * 32);
  }

  .w-40 {
    width: calc(var(--spacing) * 40);
  }

  .w-44 {
    width: calc(var(--spacing) * 44);
  }

  .w-48 {
    width: calc(var(--spacing) * 48);
  }

  .w-56 {
    width: calc(var(--spacing) * 56);
  }

  .w-64 {
    width: calc(var(--spacing) * 64);
  }

  .w-72 {
    width: calc(var(--spacing) * 72);
  }

  .w-80 {
    width: calc(var(--spacing) * 80);
  }

  .w-96 {
    width: calc(var(--spacing) * 96);
  }

  .w-\[--radix-dropdown-menu-trigger-width\] {
    width: --radix-dropdown-menu-trigger-width;
  }

  .w-\[1px\] {
    width: 1px;
  }

  .w-\[4\.5rem\] {
    width: 4.5rem;
  }

  .w-\[12\.5rem\] {
    width: 12.5rem;
  }

  .w-\[70px\] {
    width: 70px;
  }

  .w-\[80px\] {
    width: 80px;
  }

  .w-\[90vw\] {
    width: 90vw;
  }

  .w-\[95vw\] {
    width: 95vw;
  }

  .w-\[100px\] {
    width: 100px;
  }

  .w-\[120px\] {
    width: 120px;
  }

  .w-\[140px\] {
    width: 140px;
  }

  .w-\[150px\] {
    width: 150px;
  }

  .w-\[160px\] {
    width: 160px;
  }

  .w-\[180px\] {
    width: 180px;
  }

  .w-\[200px\] {
    width: 200px;
  }

  .w-\[250px\] {
    width: 250px;
  }

  .w-\[300px\] {
    width: 300px;
  }

  .w-\[350px\] {
    width: 350px;
  }

  .w-auto {
    width: auto;
  }

  .w-fit {
    width: fit-content;
  }

  .w-full {
    width: 100%;
  }

  .w-max {
    width: max-content;
  }

  .w-px {
    width: 1px;
  }

  .max-w-\(--skeleton-width\) {
    max-width: var(--skeleton-width);
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-6xl {
    max-width: var(--container-6xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-\[600px\] {
    max-width: 600px;
  }

  .max-w-\[calc\(100\%-2rem\)\] {
    max-width: calc(100% - 2rem);
  }

  .max-w-full {
    max-width: 100%;
  }

  .max-w-max {
    max-width: max-content;
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-none {
    max-width: none;
  }

  .max-w-sm {
    max-width: var(--container-sm);
  }

  .max-w-xl {
    max-width: var(--container-xl);
  }

  .max-w-xs {
    max-width: var(--container-xs);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .min-w-5 {
    min-width: calc(var(--spacing) * 5);
  }

  .min-w-8 {
    min-width: calc(var(--spacing) * 8);
  }

  .min-w-9 {
    min-width: calc(var(--spacing) * 9);
  }

  .min-w-10 {
    min-width: calc(var(--spacing) * 10);
  }

  .min-w-56 {
    min-width: calc(var(--spacing) * 56);
  }

  .min-w-\[8rem\] {
    min-width: 8rem;
  }

  .min-w-\[12rem\] {
    min-width: 12rem;
  }

  .min-w-\[120px\] {
    min-width: 120px;
  }

  .min-w-\[300px\] {
    min-width: 300px;
  }

  .min-w-\[320px\] {
    min-width: 320px;
  }

  .min-w-\[var\(--radix-select-trigger-width\)\] {
    min-width: var(--radix-select-trigger-width);
  }

  .min-w-full {
    min-width: 100%;
  }

  .flex-1 {
    flex: 1;
  }

  .flex-none {
    flex: none;
  }

  .flex-shrink-0, .shrink-0 {
    flex-shrink: 0;
  }

  .flex-grow, .grow {
    flex-grow: 1;
  }

  .caption-bottom {
    caption-side: bottom;
  }

  .border-collapse {
    border-collapse: collapse;
  }

  .origin-\(--radix-context-menu-content-transform-origin\) {
    transform-origin: var(--radix-context-menu-content-transform-origin);
  }

  .origin-\(--radix-dropdown-menu-content-transform-origin\) {
    transform-origin: var(--radix-dropdown-menu-content-transform-origin);
  }

  .origin-\(--radix-hover-card-content-transform-origin\) {
    transform-origin: var(--radix-hover-card-content-transform-origin);
  }

  .origin-\(--radix-menubar-content-transform-origin\) {
    transform-origin: var(--radix-menubar-content-transform-origin);
  }

  .origin-\(--radix-popover-content-transform-origin\) {
    transform-origin: var(--radix-popover-content-transform-origin);
  }

  .origin-\(--radix-select-content-transform-origin\) {
    transform-origin: var(--radix-select-content-transform-origin);
  }

  .origin-\(--radix-tooltip-content-transform-origin\) {
    transform-origin: var(--radix-tooltip-content-transform-origin);
  }

  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-x-px {
    --tw-translate-x: -1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-\[-50\%\] {
    --tw-translate-x: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-x-px {
    --tw-translate-x: 1px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-12\! {
    --tw-translate-y: calc(var(--spacing) * -12) !important;
    translate: var(--tw-translate-x) var(--tw-translate-y) !important;
  }

  .translate-y-0\.5 {
    --tw-translate-y: calc(var(--spacing) * .5);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[-50\%\] {
    --tw-translate-y: -50%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .translate-y-\[calc\(-50\%_-_2px\)\] {
    --tw-translate-y: calc(-50% - 2px);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .scale-100 {
    --tw-scale-x: 100%;
    --tw-scale-y: 100%;
    --tw-scale-z: 100%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .scale-125 {
    --tw-scale-x: 125%;
    --tw-scale-y: 125%;
    --tw-scale-z: 125%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }

  .rotate-3 {
    rotate: 3deg;
  }

  .rotate-45 {
    rotate: 45deg;
  }

  .rotate-180 {
    rotate: 180deg;
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-bounce {
    animation: var(--animate-bounce);
  }

  .animate-caret-blink {
    animation: 1.25s ease-out infinite caret-blink;
  }

  .animate-in {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .animate-ping {
    animation: var(--animate-ping);
  }

  .animate-pulse {
    animation: var(--animate-pulse);
  }

  .animate-spin {
    animation: var(--animate-spin);
  }

  .cursor-default {
    cursor: default;
  }

  .cursor-grab {
    cursor: grab;
  }

  .cursor-move {
    cursor: move;
  }

  .cursor-not-allowed {
    cursor: not-allowed;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .touch-none {
    touch-action: none;
  }

  .resize-none {
    resize: none;
  }

  .snap-none {
    scroll-snap-type: none;
  }

  .snap-center {
    scroll-snap-align: center;
  }

  .scroll-my-1 {
    scroll-margin-block: calc(var(--spacing) * 1);
  }

  .scroll-mt-4 {
    scroll-margin-top: calc(var(--spacing) * 4);
  }

  .scroll-mt-20 {
    scroll-margin-top: calc(var(--spacing) * 20);
  }

  .scroll-py-1 {
    scroll-padding-block: calc(var(--spacing) * 1);
  }

  .list-decimal {
    list-style-type: decimal;
  }

  .list-disc {
    list-style-type: disc;
  }

  .list-none {
    list-style-type: none;
  }

  .grid-flow-col {
    grid-auto-flow: column;
  }

  .auto-rows-min {
    grid-auto-rows: min-content;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .grid-cols-\[0_1fr\] {
    grid-template-columns: 0 1fr;
  }

  .grid-rows-\[auto_auto\] {
    grid-template-rows: auto auto;
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-col-reverse {
    flex-direction: column-reverse;
  }

  .flex-row {
    flex-direction: row;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .place-items-center {
    place-items: center;
  }

  .items-center {
    align-items: center;
  }

  .items-end {
    align-items: flex-end;
  }

  .items-start {
    align-items: flex-start;
  }

  .items-stretch {
    align-items: stretch;
  }

  .justify-around {
    justify-content: space-around;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-end {
    justify-content: flex-end;
  }

  .justify-start {
    justify-content: flex-start;
  }

  .justify-items-start {
    justify-items: start;
  }

  .gap-0 {
    gap: calc(var(--spacing) * 0);
  }

  .gap-0\.5 {
    gap: calc(var(--spacing) * .5);
  }

  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }

  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  .gap-12 {
    gap: calc(var(--spacing) * 12);
  }

  :where(.space-y-0 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-1 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-px > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(1px * var(--tw-space-y-reverse));
    margin-block-end: calc(1px * calc(1 - var(--tw-space-y-reverse)));
  }

  .gap-x-4 {
    column-gap: calc(var(--spacing) * 4);
  }

  :where(.space-x-1 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-2 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-3 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-12 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 12) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-x-reverse)));
  }

  .gap-y-0\.5 {
    row-gap: calc(var(--spacing) * .5);
  }

  .self-start {
    align-self: flex-start;
  }

  .justify-self-end {
    justify-self: flex-end;
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-auto {
    overflow: auto;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .overflow-x-auto {
    overflow-x: auto;
  }

  .overflow-x-hidden {
    overflow-x: hidden;
  }

  .overflow-y-auto {
    overflow-y: auto;
  }

  .rounded {
    border-radius: .25rem;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }

  .rounded-\[0\.5rem\] {
    border-radius: .5rem;
  }

  .rounded-\[2px\] {
    border-radius: 2px;
  }

  .rounded-\[4px\] {
    border-radius: 4px;
  }

  .rounded-\[inherit\] {
    border-radius: inherit;
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius);
  }

  .rounded-md {
    border-radius: calc(var(--radius)  - 2px);
  }

  .rounded-none {
    border-radius: 0;
  }

  .rounded-sm {
    border-radius: calc(var(--radius)  - 4px);
  }

  .rounded-xl {
    border-radius: calc(var(--radius)  + 4px);
  }

  .rounded-xs {
    border-radius: var(--radius-xs);
  }

  .rounded-t-lg {
    border-top-left-radius: var(--radius);
    border-top-right-radius: var(--radius);
  }

  .rounded-tl-sm {
    border-top-left-radius: calc(var(--radius)  - 4px);
  }

  .rounded-r {
    border-top-right-radius: .25rem;
    border-bottom-right-radius: .25rem;
  }

  .rounded-r-md {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }

  .border-\[1\.5px\] {
    border-style: var(--tw-border-style);
    border-width: 1.5px;
  }

  .border-y {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-t-2 {
    border-top-style: var(--tw-border-style);
    border-top-width: 2px;
  }

  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }

  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }

  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }

  .border-none {
    --tw-border-style: none;
    border-style: none;
  }

  .border-\(--color-border\) {
    border-color: var(--color-border);
  }

  .border-\[var\(--iai-primary\)\] {
    border-color: var(--iai-primary);
  }

  .border-amber-200 {
    border-color: var(--color-amber-200);
  }

  .border-amber-500 {
    border-color: var(--color-amber-500);
  }

  .border-blue-200 {
    border-color: var(--color-blue-200);
  }

  .border-blue-300 {
    border-color: var(--color-blue-300);
  }

  .border-blue-400 {
    border-color: var(--color-blue-400);
  }

  .border-blue-500 {
    border-color: var(--color-blue-500);
  }

  .border-blue-600 {
    border-color: var(--color-blue-600);
  }

  .border-border {
    border-color: var(--border);
  }

  .border-border\/50 {
    border-color: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-border\/50 {
      border-color: color-mix(in oklab, var(--border) 50%, transparent);
    }
  }

  .border-gray-100 {
    border-color: var(--color-gray-100);
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-gray-400 {
    border-color: var(--color-gray-400);
  }

  .border-green-200 {
    border-color: var(--color-green-200);
  }

  .border-green-300 {
    border-color: var(--color-green-300);
  }

  .border-green-400 {
    border-color: var(--color-green-400);
  }

  .border-green-500 {
    border-color: var(--color-green-500);
  }

  .border-green-600 {
    border-color: var(--color-green-600);
  }

  .border-input {
    border-color: var(--input);
  }

  .border-muted-foreground\/25 {
    border-color: var(--muted-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-muted-foreground\/25 {
      border-color: color-mix(in oklab, var(--muted-foreground) 25%, transparent);
    }
  }

  .border-muted-foreground\/50 {
    border-color: var(--muted-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-muted-foreground\/50 {
      border-color: color-mix(in oklab, var(--muted-foreground) 50%, transparent);
    }
  }

  .border-orange-400 {
    border-color: var(--color-orange-400);
  }

  .border-primary {
    border-color: var(--primary);
  }

  .border-primary\/30 {
    border-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .border-primary\/30 {
      border-color: color-mix(in oklab, var(--primary) 30%, transparent);
    }
  }

  .border-purple-200 {
    border-color: var(--color-purple-200);
  }

  .border-purple-400 {
    border-color: var(--color-purple-400);
  }

  .border-red-200 {
    border-color: var(--color-red-200);
  }

  .border-red-300 {
    border-color: var(--color-red-300);
  }

  .border-red-400 {
    border-color: var(--color-red-400);
  }

  .border-red-500 {
    border-color: var(--color-red-500);
  }

  .border-red-600 {
    border-color: var(--color-red-600);
  }

  .border-secondary {
    border-color: var(--secondary);
  }

  .border-sidebar-border {
    border-color: var(--sidebar-border);
  }

  .border-sky-600 {
    border-color: var(--color-sky-600);
  }

  .border-transparent {
    border-color: #0000;
  }

  .border-white {
    border-color: var(--color-white);
  }

  .border-yellow-200 {
    border-color: var(--color-yellow-200);
  }

  .border-yellow-300 {
    border-color: var(--color-yellow-300);
  }

  .border-yellow-400 {
    border-color: var(--color-yellow-400);
  }

  .border-t-transparent {
    border-top-color: #0000;
  }

  .border-l-blue-200 {
    border-left-color: var(--color-blue-200);
  }

  .border-l-gray-300 {
    border-left-color: var(--color-gray-300);
  }

  .border-l-green-400 {
    border-left-color: var(--color-green-400);
  }

  .border-l-transparent {
    border-left-color: #0000;
  }

  .bg-\(--color-bg\) {
    background-color: var(--color-bg);
  }

  .bg-\[var\(--iai-primary\)\] {
    background-color: var(--iai-primary);
  }

  .bg-accent {
    background-color: var(--accent);
  }

  .bg-accent\/50 {
    background-color: var(--accent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-accent\/50 {
      background-color: color-mix(in oklab, var(--accent) 50%, transparent);
    }
  }

  .bg-amber-50 {
    background-color: var(--color-amber-50);
  }

  .bg-amber-500 {
    background-color: var(--color-amber-500);
  }

  .bg-background {
    background-color: var(--background);
  }

  .bg-background\/80 {
    background-color: var(--background);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-background\/80 {
      background-color: color-mix(in oklab, var(--background) 80%, transparent);
    }
  }

  .bg-black {
    background-color: var(--color-black);
  }

  .bg-black\/20 {
    background-color: #0003;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/20 {
      background-color: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .bg-black\/50 {
    background-color: #00000080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-50\/50 {
    background-color: #eff6ff80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-blue-50\/50 {
      background-color: color-mix(in oklab, var(--color-blue-50) 50%, transparent);
    }
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-200 {
    background-color: var(--color-blue-200);
  }

  .bg-blue-400 {
    background-color: var(--color-blue-400);
  }

  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-border {
    background-color: var(--border);
  }

  .bg-card {
    background-color: var(--card);
  }

  .bg-current {
    background-color: currentColor;
  }

  .bg-destructive {
    background-color: var(--destructive);
  }

  .bg-foreground {
    background-color: var(--foreground);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }

  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }

  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }

  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }

  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }

  .bg-green-50 {
    background-color: var(--color-green-50);
  }

  .bg-green-50\/30 {
    background-color: #f0fdf44d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-green-50\/30 {
      background-color: color-mix(in oklab, var(--color-green-50) 30%, transparent);
    }
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-green-400 {
    background-color: var(--color-green-400);
  }

  .bg-green-500 {
    background-color: var(--color-green-500);
  }

  .bg-green-600 {
    background-color: var(--color-green-600);
  }

  .bg-muted {
    background-color: var(--muted);
  }

  .bg-muted\/30 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/30 {
      background-color: color-mix(in oklab, var(--muted) 30%, transparent);
    }
  }

  .bg-muted\/50 {
    background-color: var(--muted);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-muted\/50 {
      background-color: color-mix(in oklab, var(--muted) 50%, transparent);
    }
  }

  .bg-orange-50 {
    background-color: var(--color-orange-50);
  }

  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }

  .bg-popover {
    background-color: var(--popover);
  }

  .bg-primary {
    background-color: var(--primary);
  }

  .bg-primary\/5 {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/5 {
      background-color: color-mix(in oklab, var(--primary) 5%, transparent);
    }
  }

  .bg-primary\/20 {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-primary\/20 {
      background-color: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-purple-400 {
    background-color: var(--color-purple-400);
  }

  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }

  .bg-red-50 {
    background-color: var(--color-red-50);
  }

  .bg-red-100 {
    background-color: var(--color-red-100);
  }

  .bg-red-500 {
    background-color: var(--color-red-500);
  }

  .bg-red-600 {
    background-color: var(--color-red-600);
  }

  .bg-secondary {
    background-color: var(--secondary);
  }

  .bg-secondary\/50 {
    background-color: var(--secondary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-secondary\/50 {
      background-color: color-mix(in oklab, var(--secondary) 50%, transparent);
    }
  }

  .bg-sidebar {
    background-color: var(--sidebar);
  }

  .bg-sidebar-border {
    background-color: var(--sidebar-border);
  }

  .bg-transparent {
    background-color: #0000;
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/10 {
    background-color: #ffffff1a;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/10 {
      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }

  .bg-white\/20 {
    background-color: #fff3;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/20 {
      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }

  .bg-white\/40 {
    background-color: #fff6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/40 {
      background-color: color-mix(in oklab, var(--color-white) 40%, transparent);
    }
  }

  .bg-white\/50 {
    background-color: #ffffff80;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/50 {
      background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
    }
  }

  .bg-white\/95 {
    background-color: #fffffff2;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/95 {
      background-color: color-mix(in oklab, var(--color-white) 95%, transparent);
    }
  }

  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }

  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }

  .bg-yellow-400 {
    background-color: var(--color-yellow-400);
  }

  .bg-yellow-500 {
    background-color: var(--color-yellow-500);
  }

  .bg-yellow-600 {
    background-color: var(--color-yellow-600);
  }

  .bg-linear-to-b {
    --tw-gradient-position: to bottom;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  @supports (background-image: linear-gradient(in lab, red, red)) {
    .bg-linear-to-b {
      --tw-gradient-position: to bottom in oklab;
    }
  }

  .bg-linear-to-t {
    --tw-gradient-position: to top;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  @supports (background-image: linear-gradient(in lab, red, red)) {
    .bg-linear-to-t {
      --tw-gradient-position: to top in oklab;
    }
  }

  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-bl {
    --tw-gradient-position: to bottom left in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-blue-50 {
    --tw-gradient-from: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-100 {
    --tw-gradient-from: var(--color-blue-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-500 {
    --tw-gradient-from: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-foreground {
    --tw-gradient-from: var(--foreground);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-gray-400 {
    --tw-gradient-from: var(--color-gray-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-gray-900 {
    --tw-gradient-from: var(--color-gray-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-50 {
    --tw-gradient-from: var(--color-green-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-primary\/5 {
    --tw-gradient-from: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-primary\/5 {
      --tw-gradient-from: color-mix(in oklab, var(--primary) 5%, transparent);
    }
  }

  .from-slate-50 {
    --tw-gradient-from: var(--color-slate-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-slate-800 {
    --tw-gradient-from: var(--color-slate-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-transparent {
    --tw-gradient-from: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-white {
    --tw-gradient-from: var(--color-white);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-yellow-50 {
    --tw-gradient-from: var(--color-yellow-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-yellow-400 {
    --tw-gradient-from: var(--color-yellow-400);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-gray-800 {
    --tw-gradient-via: var(--color-gray-800);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .via-white {
    --tw-gradient-via: var(--color-white);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-black {
    --tw-gradient-to: var(--color-black);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-blue-50 {
    --tw-gradient-to: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-gray-600 {
    --tw-gradient-to: var(--color-gray-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-indigo-50 {
    --tw-gradient-to: var(--color-indigo-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-indigo-100 {
    --tw-gradient-to: var(--color-indigo-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-indigo-600 {
    --tw-gradient-to: var(--color-indigo-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-orange-50 {
    --tw-gradient-to: var(--color-orange-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-primary\/20 {
    --tw-gradient-to: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .to-primary\/20 {
      --tw-gradient-to: color-mix(in oklab, var(--primary) 20%, transparent);
    }
  }

  .to-purple-600 {
    --tw-gradient-to: var(--color-purple-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-slate-900 {
    --tw-gradient-to: var(--color-slate-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-yellow-600 {
    --tw-gradient-to: var(--color-yellow-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .bg-clip-text {
    background-clip: text;
  }

  .fill-current {
    fill: currentColor;
  }

  .fill-foreground {
    fill: var(--foreground);
  }

  .fill-muted-foreground {
    fill: var(--muted-foreground);
  }

  .fill-primary {
    fill: var(--primary);
  }

  .object-contain {
    object-fit: contain;
  }

  .object-cover {
    object-fit: cover;
  }

  .\!p-0 {
    padding: calc(var(--spacing) * 0) !important;
  }

  .p-0 {
    padding: calc(var(--spacing) * 0);
  }

  .p-0\! {
    padding: calc(var(--spacing) * 0) !important;
  }

  .p-1 {
    padding: calc(var(--spacing) * 1);
  }

  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-3 {
    padding: calc(var(--spacing) * 3);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .p-12 {
    padding: calc(var(--spacing) * 12);
  }

  .p-px {
    padding: 1px;
  }

  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }

  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }

  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }

  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .\!py-0 {
    padding-block: calc(var(--spacing) * 0) !important;
  }

  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }

  .py-0\.5 {
    padding-block: calc(var(--spacing) * .5);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }

  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .\!pt-3 {
    padding-top: calc(var(--spacing) * 3) !important;
  }

  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }

  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }

  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }

  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }

  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }

  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }

  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pt-12 {
    padding-top: calc(var(--spacing) * 12);
  }

  .pt-20 {
    padding-top: calc(var(--spacing) * 20);
  }

  .pr-1 {
    padding-right: calc(var(--spacing) * 1);
  }

  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }

  .pr-2\.5 {
    padding-right: calc(var(--spacing) * 2.5);
  }

  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }

  .pr-8 {
    padding-right: calc(var(--spacing) * 8);
  }

  .pr-10 {
    padding-right: calc(var(--spacing) * 10);
  }

  .pr-20 {
    padding-right: calc(var(--spacing) * 20);
  }

  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }

  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }

  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }

  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }

  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .pb-8 {
    padding-bottom: calc(var(--spacing) * 8);
  }

  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }

  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }

  .pl-7 {
    padding-left: calc(var(--spacing) * 7);
  }

  .pl-8 {
    padding-left: calc(var(--spacing) * 8);
  }

  .pl-9 {
    padding-left: calc(var(--spacing) * 9);
  }

  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .align-middle {
    vertical-align: middle;
  }

  .font-mono {
    font-family: var(--font-mono);
  }

  .font-sans {
    font-family: var(--font-sans);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-6xl {
    font-size: var(--text-6xl);
    line-height: var(--tw-leading, var(--text-6xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .text-\[0\.8rem\] {
    font-size: .8rem;
  }

  .text-\[10px\] {
    font-size: 10px;
  }

  .text-\[10rem\] {
    font-size: 10rem;
  }

  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .font-black {
    --tw-font-weight: var(--font-weight-black);
    font-weight: var(--font-weight-black);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-extrabold {
    --tw-font-weight: var(--font-weight-extrabold);
    font-weight: var(--font-weight-extrabold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }

  .tracking-wide {
    --tw-tracking: var(--tracking-wide);
    letter-spacing: var(--tracking-wide);
  }

  .tracking-widest {
    --tw-tracking: var(--tracking-widest);
    letter-spacing: var(--tracking-widest);
  }

  .text-balance {
    text-wrap: balance;
  }

  .break-words {
    overflow-wrap: break-word;
  }

  .whitespace-nowrap {
    white-space: nowrap;
  }

  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }

  .text-\[var\(--iai-primary\)\] {
    color: var(--iai-primary);
  }

  .text-accent-foreground {
    color: var(--accent-foreground);
  }

  .text-amber-600 {
    color: var(--color-amber-600);
  }

  .text-amber-700 {
    color: var(--color-amber-700);
  }

  .text-amber-800 {
    color: var(--color-amber-800);
  }

  .text-blue-200 {
    color: var(--color-blue-200);
  }

  .text-blue-500 {
    color: var(--color-blue-500);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-700 {
    color: var(--color-blue-700);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-blue-900 {
    color: var(--color-blue-900);
  }

  .text-card-foreground {
    color: var(--card-foreground);
  }

  .text-current {
    color: currentColor;
  }

  .text-destructive {
    color: var(--destructive);
  }

  .text-foreground {
    color: var(--foreground);
  }

  .text-foreground\/80 {
    color: var(--foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-foreground\/80 {
      color: color-mix(in oklab, var(--foreground) 80%, transparent);
    }
  }

  .text-gray-100 {
    color: var(--color-gray-100);
  }

  .text-gray-200 {
    color: var(--color-gray-200);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-700 {
    color: var(--color-gray-700);
  }

  .text-gray-800 {
    color: var(--color-gray-800);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-400 {
    color: var(--color-green-400);
  }

  .text-green-500 {
    color: var(--color-green-500);
  }

  .text-green-600 {
    color: var(--color-green-600);
  }

  .text-green-700 {
    color: var(--color-green-700);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-green-900 {
    color: var(--color-green-900);
  }

  .text-muted-foreground {
    color: var(--muted-foreground);
  }

  .text-muted-foreground\/70 {
    color: var(--muted-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-muted-foreground\/70 {
      color: color-mix(in oklab, var(--muted-foreground) 70%, transparent);
    }
  }

  .text-orange-600 {
    color: var(--color-orange-600);
  }

  .text-orange-700 {
    color: var(--color-orange-700);
  }

  .text-orange-800 {
    color: var(--color-orange-800);
  }

  .text-popover-foreground {
    color: var(--popover-foreground);
  }

  .text-primary {
    color: var(--primary);
  }

  .text-primary-foreground {
    color: var(--primary-foreground);
  }

  .text-primary\/50 {
    color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-primary\/50 {
      color: color-mix(in oklab, var(--primary) 50%, transparent);
    }
  }

  .text-purple-600 {
    color: var(--color-purple-600);
  }

  .text-purple-700 {
    color: var(--color-purple-700);
  }

  .text-purple-800 {
    color: var(--color-purple-800);
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-red-600 {
    color: var(--color-red-600);
  }

  .text-red-700 {
    color: var(--color-red-700);
  }

  .text-red-800 {
    color: var(--color-red-800);
  }

  .text-secondary-foreground {
    color: var(--secondary-foreground);
  }

  .text-secondary-foreground\/50 {
    color: var(--secondary-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-secondary-foreground\/50 {
      color: color-mix(in oklab, var(--secondary-foreground) 50%, transparent);
    }
  }

  .text-sidebar-foreground {
    color: var(--sidebar-foreground);
  }

  .text-sidebar-foreground\/70 {
    color: var(--sidebar-foreground);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-sidebar-foreground\/70 {
      color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);
    }
  }

  .text-sidebar-primary-foreground {
    color: var(--sidebar-primary-foreground);
  }

  .text-sky-600 {
    color: var(--color-sky-600);
  }

  .text-sky-900 {
    color: var(--color-sky-900);
  }

  .text-transparent {
    color: #0000;
  }

  .text-white {
    color: var(--color-white);
  }

  .text-white\/90 {
    color: #ffffffe6;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .text-white\/90 {
      color: color-mix(in oklab, var(--color-white) 90%, transparent);
    }
  }

  .text-yellow-300 {
    color: var(--color-yellow-300);
  }

  .text-yellow-500 {
    color: var(--color-yellow-500);
  }

  .text-yellow-600 {
    color: var(--color-yellow-600);
  }

  .text-yellow-700 {
    color: var(--color-yellow-700);
  }

  .text-yellow-800 {
    color: var(--color-yellow-800);
  }

  .capitalize {
    text-transform: capitalize;
  }

  .uppercase {
    text-transform: uppercase;
  }

  .italic {
    font-style: italic;
  }

  .tabular-nums {
    --tw-numeric-spacing: tabular-nums;
    font-variant-numeric: var(--tw-ordinal, ) var(--tw-slashed-zero, ) var(--tw-numeric-figure, ) var(--tw-numeric-spacing, ) var(--tw-numeric-fraction, );
  }

  .no-underline\! {
    text-decoration-line: none !important;
  }

  .underline {
    text-decoration-line: underline;
  }

  .underline-offset-4 {
    text-underline-offset: 4px;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .opacity-0 {
    opacity: 0;
  }

  .opacity-10 {
    opacity: .1;
  }

  .opacity-30 {
    opacity: .3;
  }

  .opacity-50 {
    opacity: .5;
  }

  .opacity-60 {
    opacity: .6;
  }

  .opacity-70 {
    opacity: .7;
  }

  .opacity-75 {
    opacity: .75;
  }

  .opacity-80 {
    opacity: .8;
  }

  .opacity-90 {
    opacity: .9;
  }

  .opacity-100 {
    opacity: 1;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a), 0 2px 4px -2px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-0 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-1 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .ring-blue-400 {
    --tw-ring-color: var(--color-blue-400);
  }

  .ring-green-200 {
    --tw-ring-color: var(--color-green-200);
  }

  .ring-primary {
    --tw-ring-color: var(--primary);
  }

  .ring-red-200 {
    --tw-ring-color: var(--color-red-200);
  }

  .ring-ring\/50 {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .ring-ring\/50 {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  .ring-sidebar-ring {
    --tw-ring-color: var(--sidebar-ring);
  }

  .ring-sky-300 {
    --tw-ring-color: var(--color-sky-300);
  }

  .ring-yellow-400 {
    --tw-ring-color: var(--color-yellow-400);
  }

  .ring-offset-background {
    --tw-ring-offset-color: var(--background);
  }

  .outline-hidden {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .outline-hidden {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .blur-md {
    --tw-blur: blur(var(--blur-md));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .drop-shadow-lg {
    --tw-drop-shadow-size: drop-shadow(0 4px 4px var(--tw-drop-shadow-color, #00000026));
    --tw-drop-shadow: drop-shadow(var(--drop-shadow-lg));
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .filter {
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[color\,box-shadow\] {
    transition-property: color, box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[left\,right\,width\] {
    transition-property: left, right, width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[margin\,opacity\] {
    transition-property: margin, opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\,height\,padding\] {
    transition-property: width, height, padding;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\,height\] {
    transition-property: width, height;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-\[width\] {
    transition-property: width;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-shadow {
    transition-property: box-shadow;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-none {
    transition-property: none;
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .duration-500 {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .duration-1000 {
    --tw-duration: 1s;
    transition-duration: 1s;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }

  .ease-linear {
    --tw-ease: linear;
    transition-timing-function: linear;
  }

  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }

  .fade-in-0 {
    --tw-enter-opacity: 0;
  }

  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }

  .select-none {
    -webkit-user-select: none;
    user-select: none;
  }

  .zoom-in-95 {
    --tw-enter-scale: .95;
  }

  .fade-in {
    --tw-enter-opacity: 0;
  }

  .ring-inset {
    --tw-ring-inset: inset;
  }

  .slide-in-from-bottom-2 {
    --tw-enter-translate-y: calc(2 * var(--spacing));
  }

  .slide-in-from-top-2 {
    --tw-enter-translate-y: calc(2 * var(--spacing) * -1);
  }

  .group-focus-within\/menu-item\:opacity-100:is(:where(.group\/menu-item):focus-within *) {
    opacity: 1;
  }

  @media (hover: hover) {
    .group-hover\:bg-blue-200:is(:where(.group):hover *) {
      background-color: var(--color-blue-200);
    }
  }

  @media (hover: hover) {
    .group-hover\:text-blue-600:is(:where(.group):hover *) {
      color: var(--color-blue-600);
    }
  }

  @media (hover: hover) {
    .group-hover\/menu-item\:opacity-100:is(:where(.group\/menu-item):hover *) {
      opacity: 1;
    }
  }

  .group-has-data-\[collapsible\=icon\]\/sidebar-wrapper\:h-12:is(:where(.group\/sidebar-wrapper):has([data-collapsible="icon"]) *) {
    height: calc(var(--spacing) * 12);
  }

  .group-has-data-\[sidebar\=menu-action\]\/menu-item\:pr-8:is(:where(.group\/menu-item):has([data-sidebar="menu-action"]) *) {
    padding-right: calc(var(--spacing) * 8);
  }

  .group-data-\[collapsible\=icon\]\:-mt-8:is(:where(.group)[data-collapsible="icon"] *) {
    margin-top: calc(var(--spacing) * -8);
  }

  .group-data-\[collapsible\=icon\]\:hidden:is(:where(.group)[data-collapsible="icon"] *) {
    display: none;
  }

  .group-data-\[collapsible\=icon\]\:size-8\!:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--spacing) * 8) !important;
    height: calc(var(--spacing) * 8) !important;
  }

  .group-data-\[collapsible\=icon\]\:w-\(--sidebar-width-icon\):is(:where(.group)[data-collapsible="icon"] *) {
    width: var(--sidebar-width-icon);
  }

  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\)\]:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4)));
  }

  .group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)\+\(--spacing\(4\)\)\+2px\)\]:is(:where(.group)[data-collapsible="icon"] *) {
    width: calc(var(--sidebar-width-icon)  + (calc(var(--spacing) * 4))  + 2px);
  }

  .group-data-\[collapsible\=icon\]\:overflow-hidden:is(:where(.group)[data-collapsible="icon"] *) {
    overflow: hidden;
  }

  .group-data-\[collapsible\=icon\]\:p-0\!:is(:where(.group)[data-collapsible="icon"] *) {
    padding: calc(var(--spacing) * 0) !important;
  }

  .group-data-\[collapsible\=icon\]\:p-2\!:is(:where(.group)[data-collapsible="icon"] *) {
    padding: calc(var(--spacing) * 2) !important;
  }

  .group-data-\[collapsible\=icon\]\:opacity-0:is(:where(.group)[data-collapsible="icon"] *) {
    opacity: 0;
  }

  .group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
    right: calc(var(--sidebar-width) * -1);
  }

  .group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\]:is(:where(.group)[data-collapsible="offcanvas"] *) {
    left: calc(var(--sidebar-width) * -1);
  }

  .group-data-\[collapsible\=offcanvas\]\:w-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
    width: calc(var(--spacing) * 0);
  }

  .group-data-\[collapsible\=offcanvas\]\:translate-x-0:is(:where(.group)[data-collapsible="offcanvas"] *) {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled="true"] *) {
    pointer-events: none;
  }

  .group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled="true"] *) {
    opacity: .5;
  }

  .group-data-\[side\=left\]\:-right-4:is(:where(.group)[data-side="left"] *) {
    right: calc(var(--spacing) * -4);
  }

  .group-data-\[side\=left\]\:border-r:is(:where(.group)[data-side="left"] *) {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .group-data-\[side\=right\]\:left-0:is(:where(.group)[data-side="right"] *) {
    left: calc(var(--spacing) * 0);
  }

  .group-data-\[side\=right\]\:rotate-180:is(:where(.group)[data-side="right"] *) {
    rotate: 180deg;
  }

  .group-data-\[side\=right\]\:border-l:is(:where(.group)[data-side="right"] *) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .group-data-\[state\=open\]\:rotate-180:is(:where(.group)[data-state="open"] *) {
    rotate: 180deg;
  }

  .group-data-\[state\=open\]\/collapsible\:rotate-90:is(:where(.group\/collapsible)[data-state="open"] *) {
    rotate: 90deg;
  }

  .group-data-\[variant\=floating\]\:rounded-lg:is(:where(.group)[data-variant="floating"] *) {
    border-radius: var(--radius);
  }

  .group-data-\[variant\=floating\]\:border:is(:where(.group)[data-variant="floating"] *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .group-data-\[variant\=floating\]\:border-sidebar-border:is(:where(.group)[data-variant="floating"] *) {
    border-color: var(--sidebar-border);
  }

  .group-data-\[variant\=floating\]\:shadow-sm:is(:where(.group)[data-variant="floating"] *) {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .group-data-\[vaul-drawer-direction\=bottom\]\/drawer-content\:block:is(:where(.group\/drawer-content)[data-vaul-drawer-direction="bottom"] *) {
    display: block;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:top-full:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    top: 100%;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:mt-1\.5:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    margin-top: calc(var(--spacing) * 1.5);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:overflow-hidden:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    overflow: hidden;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:rounded-md:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    border-radius: calc(var(--radius)  - 2px);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:border:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:bg-popover:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    background-color: var(--popover);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:text-popover-foreground:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    color: var(--popover-foreground);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:shadow:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:duration-200:is(:where(.group\/navigation-menu)[data-viewport="false"] *) {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  @media (hover: hover) {
    .peer-hover\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button):hover ~ *) {
      color: var(--sidebar-accent-foreground);
    }
  }

  .peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled ~ *) {
    cursor: not-allowed;
  }

  .peer-disabled\:opacity-50:is(:where(.peer):disabled ~ *) {
    opacity: .5;
  }

  .peer-disabled\:opacity-70:is(:where(.peer):disabled ~ *) {
    opacity: .7;
  }

  .peer-data-\[active\=true\]\/menu-button\:text-sidebar-accent-foreground:is(:where(.peer\/menu-button)[data-active="true"] ~ *) {
    color: var(--sidebar-accent-foreground);
  }

  .peer-data-\[size\=default\]\/menu-button\:top-1\.5:is(:where(.peer\/menu-button)[data-size="default"] ~ *) {
    top: calc(var(--spacing) * 1.5);
  }

  .peer-data-\[size\=lg\]\/menu-button\:top-2\.5:is(:where(.peer\/menu-button)[data-size="lg"] ~ *) {
    top: calc(var(--spacing) * 2.5);
  }

  .peer-data-\[size\=sm\]\/menu-button\:top-1:is(:where(.peer\/menu-button)[data-size="sm"] ~ *) {
    top: calc(var(--spacing) * 1);
  }

  .selection\:bg-primary ::selection, .selection\:bg-primary::selection {
    background-color: var(--primary);
  }

  .selection\:text-primary-foreground ::selection, .selection\:text-primary-foreground::selection {
    color: var(--primary-foreground);
  }

  .file\:inline-flex::file-selector-button {
    display: inline-flex;
  }

  .file\:h-7::file-selector-button {
    height: calc(var(--spacing) * 7);
  }

  .file\:border-0::file-selector-button {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .file\:bg-transparent::file-selector-button {
    background-color: #0000;
  }

  .file\:text-sm::file-selector-button {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .file\:font-medium::file-selector-button {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .file\:text-foreground::file-selector-button {
    color: var(--foreground);
  }

  .placeholder\:text-muted-foreground::placeholder {
    color: var(--muted-foreground);
  }

  .after\:absolute:after {
    content: var(--tw-content);
    position: absolute;
  }

  .after\:-inset-2:after {
    content: var(--tw-content);
    inset: calc(var(--spacing) * -2);
  }

  .after\:inset-y-0:after {
    content: var(--tw-content);
    inset-block: calc(var(--spacing) * 0);
  }

  .after\:left-1\/2:after {
    content: var(--tw-content);
    left: 50%;
  }

  .after\:w-1:after {
    content: var(--tw-content);
    width: calc(var(--spacing) * 1);
  }

  .after\:w-\[2px\]:after {
    content: var(--tw-content);
    width: 2px;
  }

  .after\:-translate-x-1\/2:after {
    content: var(--tw-content);
    --tw-translate-x: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .group-data-\[collapsible\=offcanvas\]\:after\:left-full:is(:where(.group)[data-collapsible="offcanvas"] *):after {
    content: var(--tw-content);
    left: 100%;
  }

  .first\:rounded-l-md:first-child {
    border-top-left-radius: calc(var(--radius)  - 2px);
    border-bottom-left-radius: calc(var(--radius)  - 2px);
  }

  .first\:border-l:first-child {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .last\:rounded-r-md:last-child {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .last\:border-b-0:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .even\:border-l:nth-child(2n) {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .focus-within\:relative:focus-within {
    position: relative;
  }

  .focus-within\:z-20:focus-within {
    z-index: 20;
  }

  @media (hover: hover) {
    .hover\:scale-\[1\.02\]:hover {
      scale: 1.02;
    }
  }

  @media (hover: hover) {
    .hover\:rotate-0:hover {
      rotate: none;
    }
  }

  @media (hover: hover) {
    .hover\:border-gray-300:hover {
      border-color: var(--color-gray-300);
    }
  }

  @media (hover: hover) {
    .hover\:border-gray-400:hover {
      border-color: var(--color-gray-400);
    }
  }

  @media (hover: hover) {
    .hover\:border-muted-foreground\/50:hover {
      border-color: var(--muted-foreground);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:border-muted-foreground\/50:hover {
        border-color: color-mix(in oklab, var(--muted-foreground) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[var\(--iai-primary\)\]:hover {
      background-color: var(--iai-primary);
    }
  }

  @media (hover: hover) {
    .hover\:bg-\[var\(--iai-secondary\)\]:hover {
      background-color: var(--iai-secondary);
    }
  }

  @media (hover: hover) {
    .hover\:bg-accent:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-black:hover {
      background-color: var(--color-black);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-50:hover {
      background-color: var(--color-blue-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-700:hover {
      background-color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-100:hover {
      background-color: var(--color-gray-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-800:hover {
      background-color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-50:hover {
      background-color: var(--color-green-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-100:hover {
      background-color: var(--color-green-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-200:hover {
      background-color: var(--color-green-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-600:hover {
      background-color: var(--color-green-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-green-700:hover {
      background-color: var(--color-green-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted:hover {
      background-color: var(--muted);
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/25:hover {
      background-color: var(--muted);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/25:hover {
        background-color: color-mix(in oklab, var(--muted) 25%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/50:hover {
      background-color: var(--muted);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/50:hover {
        background-color: color-mix(in oklab, var(--muted) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-muted\/80:hover {
      background-color: var(--muted);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-muted\/80:hover {
        background-color: color-mix(in oklab, var(--muted) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary:hover {
      background-color: var(--primary);
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/5:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/5:hover {
        background-color: color-mix(in oklab, var(--primary) 5%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/10:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/10:hover {
        background-color: color-mix(in oklab, var(--primary) 10%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-100:hover {
      background-color: var(--color-red-100);
    }
  }

  @media (hover: hover) {
    .hover\:bg-red-200:hover {
      background-color: var(--color-red-200);
    }
  }

  @media (hover: hover) {
    .hover\:bg-secondary\/80:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-secondary\/80:hover {
        background-color: color-mix(in oklab, var(--secondary) 80%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-sidebar-accent:hover {
      background-color: var(--sidebar-accent);
    }
  }

  @media (hover: hover) {
    .hover\:bg-sky-50:hover {
      background-color: var(--color-sky-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-transparent:hover {
      background-color: #0000;
    }
  }

  @media (hover: hover) {
    .hover\:bg-white:hover {
      background-color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:bg-white\/20:hover {
      background-color: #fff3;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .hover\:bg-white\/20:hover {
        background-color: color-mix(in oklab, var(--color-white) 20%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .hover\:bg-yellow-500:hover {
      background-color: var(--color-yellow-500);
    }
  }

  @media (hover: hover) {
    .hover\:bg-yellow-700:hover {
      background-color: var(--color-yellow-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-\[var\(--iai-secondary\)\]:hover {
      color: var(--iai-secondary);
    }
  }

  @media (hover: hover) {
    .hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-600:hover {
      color: var(--color-blue-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-destructive:hover {
      color: var(--destructive);
    }
  }

  @media (hover: hover) {
    .hover\:text-foreground:hover {
      color: var(--foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-600:hover {
      color: var(--color-gray-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-700:hover {
      color: var(--color-gray-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-900:hover {
      color: var(--color-gray-900);
    }
  }

  @media (hover: hover) {
    .hover\:text-muted-foreground:hover {
      color: var(--muted-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-primary-foreground:hover {
      color: var(--primary-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-red-700:hover {
      color: var(--color-red-700);
    }
  }

  @media (hover: hover) {
    .hover\:text-sidebar-accent-foreground:hover {
      color: var(--sidebar-accent-foreground);
    }
  }

  @media (hover: hover) {
    .hover\:text-white:hover {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-80:hover {
      opacity: .8;
    }
  }

  @media (hover: hover) {
    .hover\:opacity-100:hover {
      opacity: 1;
    }
  }

  @media (hover: hover) {
    .hover\:shadow-2xl:hover {
      --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover {
      --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-xl:hover {
      --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:ring-4:hover {
      --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (hover: hover) {
    .hover\:group-data-\[collapsible\=offcanvas\]\:bg-sidebar:hover:is(:where(.group)[data-collapsible="offcanvas"] *) {
      background-color: var(--sidebar);
    }
  }

  @media (hover: hover) {
    .hover\:after\:bg-sidebar-border:hover:after {
      content: var(--tw-content);
      background-color: var(--sidebar-border);
    }
  }

  .focus\:z-10:focus {
    z-index: 10;
  }

  .focus\:border-\[var\(--iai-primary\)\]:focus {
    border-color: var(--iai-primary);
  }

  .focus\:border-red-500:focus {
    border-color: var(--color-red-500);
  }

  .focus\:border-transparent:focus {
    border-color: #0000;
  }

  .focus\:bg-accent:focus {
    background-color: var(--accent);
  }

  .focus\:bg-primary:focus {
    background-color: var(--primary);
  }

  .focus\:text-accent-foreground:focus {
    color: var(--accent-foreground);
  }

  .focus\:text-primary-foreground:focus {
    color: var(--primary-foreground);
  }

  .focus\:ring-0:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-1:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus\:ring-\[var\(--iai-primary\)\]:focus {
    --tw-ring-color: var(--iai-primary);
  }

  .focus\:ring-blue-500:focus {
    --tw-ring-color: var(--color-blue-500);
  }

  .focus\:ring-red-500:focus {
    --tw-ring-color: var(--color-red-500);
  }

  .focus\:ring-ring:focus {
    --tw-ring-color: var(--ring);
  }

  .focus\:ring-offset-0:focus {
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:ring-offset-2:focus {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus\:outline-hidden:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus\:outline-hidden:focus {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .focus\:outline-none:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  .focus-visible\:z-10:focus-visible {
    z-index: 10;
  }

  .focus-visible\:border-ring:focus-visible {
    border-color: var(--ring);
  }

  .focus-visible\:ring-1:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-2:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-4:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-\[3px\]:focus-visible {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .focus-visible\:ring-\[var\(--iai-primary\)\]\/20:focus-visible {
    --tw-ring-color: var(--iai-primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-\[var\(--iai-primary\)\]\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--iai-primary) 20%, transparent);
    }
  }

  .focus-visible\:ring-destructive\/20:focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-destructive\/20:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .focus-visible\:ring-ring:focus-visible {
    --tw-ring-color: var(--ring);
  }

  .focus-visible\:ring-ring\/50:focus-visible {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .focus-visible\:ring-ring\/50:focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  .focus-visible\:ring-offset-1:focus-visible {
    --tw-ring-offset-width: 1px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus-visible\:ring-offset-2:focus-visible {
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset, ) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  }

  .focus-visible\:outline-hidden:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .focus-visible\:outline-hidden:focus-visible {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .focus-visible\:outline-1:focus-visible {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }

  .focus-visible\:outline-none:focus-visible {
    --tw-outline-style: none;
    outline-style: none;
  }

  .active\:bg-primary\/90:active {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .active\:bg-primary\/90:active {
      background-color: color-mix(in oklab, var(--primary) 90%, transparent);
    }
  }

  .active\:bg-sidebar-accent:active {
    background-color: var(--sidebar-accent);
  }

  .active\:text-primary-foreground:active {
    color: var(--primary-foreground);
  }

  .active\:text-sidebar-accent-foreground:active {
    color: var(--sidebar-accent-foreground);
  }

  .disabled\:pointer-events-none:disabled {
    pointer-events: none;
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:cursor-pointer:disabled {
    cursor: pointer;
  }

  .disabled\:border-none:disabled {
    --tw-border-style: none;
    border-style: none;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  .disabled\:opacity-100:disabled {
    opacity: 1;
  }

  :where([data-side="left"]) .in-data-\[side\=left\]\:cursor-w-resize {
    cursor: w-resize;
  }

  :where([data-side="right"]) .in-data-\[side\=right\]\:cursor-e-resize {
    cursor: e-resize;
  }

  .has-disabled\:opacity-50:has(:disabled) {
    opacity: .5;
  }

  .has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot="card-action"]) {
    grid-template-columns: 1fr auto;
  }

  .has-data-\[variant\=inset\]\:bg-sidebar:has([data-variant="inset"]) {
    background-color: var(--sidebar);
  }

  .has-\[\>svg\]\:grid-cols-\[calc\(var\(--spacing\)\*4\)_1fr\]:has( > svg) {
    grid-template-columns: calc(var(--spacing) * 4) 1fr;
  }

  .has-\[\>svg\]\:gap-x-3:has( > svg) {
    column-gap: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-2\.5:has( > svg) {
    padding-inline: calc(var(--spacing) * 2.5);
  }

  .has-\[\>svg\]\:px-3:has( > svg) {
    padding-inline: calc(var(--spacing) * 3);
  }

  .has-\[\>svg\]\:px-4:has( > svg) {
    padding-inline: calc(var(--spacing) * 4);
  }

  .aria-disabled\:pointer-events-none[aria-disabled="true"] {
    pointer-events: none;
  }

  .aria-disabled\:opacity-50[aria-disabled="true"] {
    opacity: .5;
  }

  .aria-invalid\:border-destructive[aria-invalid="true"] {
    border-color: var(--destructive);
  }

  .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .aria-invalid\:ring-destructive\/20[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .aria-selected\:bg-accent[aria-selected="true"] {
    background-color: var(--accent);
  }

  .aria-selected\:bg-primary[aria-selected="true"] {
    background-color: var(--primary);
  }

  .aria-selected\:text-accent-foreground[aria-selected="true"] {
    color: var(--accent-foreground);
  }

  .aria-selected\:text-muted-foreground[aria-selected="true"] {
    color: var(--muted-foreground);
  }

  .aria-selected\:text-primary-foreground[aria-selected="true"] {
    color: var(--primary-foreground);
  }

  .aria-selected\:opacity-100[aria-selected="true"] {
    opacity: 1;
  }

  .data-\[active\=true\]\:z-10[data-active="true"] {
    z-index: 10;
  }

  .data-\[active\=true\]\:border-ring[data-active="true"] {
    border-color: var(--ring);
  }

  .data-\[active\=true\]\:bg-accent\/50[data-active="true"] {
    background-color: var(--accent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:bg-accent\/50[data-active="true"] {
      background-color: color-mix(in oklab, var(--accent) 50%, transparent);
    }
  }

  .data-\[active\=true\]\:bg-primary\/5[data-active="true"] {
    background-color: var(--primary);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:bg-primary\/5[data-active="true"] {
      background-color: color-mix(in oklab, var(--primary) 5%, transparent);
    }
  }

  .data-\[active\=true\]\:bg-sidebar-accent[data-active="true"] {
    background-color: var(--sidebar-accent);
  }

  .data-\[active\=true\]\:font-medium[data-active="true"] {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .data-\[active\=true\]\:text-accent-foreground[data-active="true"] {
    color: var(--accent-foreground);
  }

  .data-\[active\=true\]\:text-sidebar-accent-foreground[data-active="true"] {
    color: var(--sidebar-accent-foreground);
  }

  .data-\[active\=true\]\:ring-\[3px\][data-active="true"] {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[active\=true\]\:ring-ring\/50[data-active="true"] {
    --tw-ring-color: var(--ring);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:ring-ring\/50[data-active="true"] {
      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);
    }
  }

  @media (hover: hover) {
    .data-\[active\=true\]\:hover\:bg-accent[data-active="true"]:hover {
      background-color: var(--accent);
    }
  }

  .data-\[active\=true\]\:focus\:bg-accent[data-active="true"]:focus {
    background-color: var(--accent);
  }

  .data-\[active\=true\]\:aria-invalid\:border-destructive[data-active="true"][aria-invalid="true"] {
    border-color: var(--destructive);
  }

  .data-\[active\=true\]\:aria-invalid\:ring-destructive\/20[data-active="true"][aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[active\=true\]\:aria-invalid\:ring-destructive\/20[data-active="true"][aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .data-\[disabled\]\:pointer-events-none[data-disabled] {
    pointer-events: none;
  }

  .data-\[disabled\]\:opacity-50[data-disabled] {
    opacity: .5;
  }

  .data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"] {
    pointer-events: none;
  }

  .data-\[disabled\=true\]\:opacity-50[data-disabled="true"] {
    opacity: .5;
  }

  .data-\[error\=true\]\:text-destructive[data-error="true"] {
    color: var(--destructive);
  }

  .data-\[inset\]\:pl-8[data-inset] {
    padding-left: calc(var(--spacing) * 8);
  }

  .data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion="from-end"] {
    --tw-enter-translate-x: calc(52 * var(--spacing));
  }

  .data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion="from-start"] {
    --tw-enter-translate-x: calc(52 * var(--spacing) * -1);
  }

  .data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion="to-end"] {
    --tw-exit-translate-x: calc(52 * var(--spacing));
  }

  .data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion="to-start"] {
    --tw-exit-translate-x: calc(52 * var(--spacing) * -1);
  }

  .data-\[motion\^\=from-\]\:animate-in[data-motion^="from-"] {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[motion\^\=from-\]\:fade-in[data-motion^="from-"] {
    --tw-enter-opacity: 0;
  }

  .data-\[motion\^\=to-\]\:animate-out[data-motion^="to-"] {
    animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[motion\^\=to-\]\:fade-out[data-motion^="to-"] {
    --tw-exit-opacity: 0;
  }

  .data-\[orientation\=horizontal\]\:h-1\.5[data-orientation="horizontal"] {
    height: calc(var(--spacing) * 1.5);
  }

  .data-\[orientation\=horizontal\]\:h-full[data-orientation="horizontal"] {
    height: 100%;
  }

  .data-\[orientation\=horizontal\]\:w-full[data-orientation="horizontal"] {
    width: 100%;
  }

  .data-\[orientation\=vertical\]\:h-4[data-orientation="vertical"] {
    height: calc(var(--spacing) * 4);
  }

  .data-\[orientation\=vertical\]\:h-full[data-orientation="vertical"] {
    height: 100%;
  }

  .data-\[orientation\=vertical\]\:min-h-44[data-orientation="vertical"] {
    min-height: calc(var(--spacing) * 44);
  }

  .data-\[orientation\=vertical\]\:w-1\.5[data-orientation="vertical"] {
    width: calc(var(--spacing) * 1.5);
  }

  .data-\[orientation\=vertical\]\:w-auto[data-orientation="vertical"] {
    width: auto;
  }

  .data-\[orientation\=vertical\]\:w-full[data-orientation="vertical"] {
    width: 100%;
  }

  .data-\[orientation\=vertical\]\:flex-col[data-orientation="vertical"] {
    flex-direction: column;
  }

  .data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction="vertical"] {
    height: 1px;
  }

  .data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction="vertical"] {
    width: 100%;
  }

  .data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction="vertical"] {
    flex-direction: column;
  }

  .data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction="vertical"]:after {
    content: var(--tw-content);
    left: calc(var(--spacing) * 0);
  }

  .data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction="vertical"]:after {
    content: var(--tw-content);
    height: calc(var(--spacing) * 1);
  }

  .data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction="vertical"]:after {
    content: var(--tw-content);
    width: 100%;
  }

  .data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction="vertical"]:after {
    content: var(--tw-content);
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction="vertical"]:after {
    content: var(--tw-content);
    --tw-translate-y: calc(calc(1 / 2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[placeholder\]\:text-muted-foreground[data-placeholder] {
    color: var(--muted-foreground);
  }

  .data-\[selected\=true\]\:bg-accent[data-selected="true"] {
    background-color: var(--accent);
  }

  .data-\[selected\=true\]\:text-accent-foreground[data-selected="true"] {
    color: var(--accent-foreground);
  }

  .data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
    --tw-translate-y: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
    --tw-enter-translate-y: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=left\]\:-translate-x-1[data-side="left"] {
    --tw-translate-x: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
    --tw-enter-translate-x: calc(2 * var(--spacing));
  }

  .data-\[side\=right\]\:translate-x-1[data-side="right"] {
    --tw-translate-x: calc(var(--spacing) * 1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
    --tw-enter-translate-x: calc(2 * var(--spacing) * -1);
  }

  .data-\[side\=top\]\:-translate-y-1[data-side="top"] {
    --tw-translate-y: calc(var(--spacing) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
    --tw-enter-translate-y: calc(2 * var(--spacing));
  }

  .data-\[size\=default\]\:h-9[data-size="default"] {
    height: calc(var(--spacing) * 9);
  }

  .data-\[size\=sm\]\:h-8[data-size="sm"] {
    height: calc(var(--spacing) * 8);
  }

  :is(.\*\:data-\[slot\=alert-description\]\:text-destructive\/90 > *)[data-slot="alert-description"] {
    color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    :is(.\*\:data-\[slot\=alert-description\]\:text-destructive\/90 > *)[data-slot="alert-description"] {
      color: color-mix(in oklab, var(--destructive) 90%, transparent);
    }
  }

  :is(.\*\:data-\[slot\=card\]\:bg-gradient-to-t > *)[data-slot="card"] {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  :is(.\*\:data-\[slot\=card\]\:from-primary\/5 > *)[data-slot="card"] {
    --tw-gradient-from: var(--primary);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    :is(.\*\:data-\[slot\=card\]\:from-primary\/5 > *)[data-slot="card"] {
      --tw-gradient-from: color-mix(in oklab, var(--primary) 5%, transparent);
    }
  }

  :is(.\*\:data-\[slot\=card\]\:to-card > *)[data-slot="card"] {
    --tw-gradient-to: var(--card);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  :is(.\*\:data-\[slot\=card\]\:shadow-xs > *)[data-slot="card"] {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  :is(.\*\*\:data-\[slot\=command-input-wrapper\]\:h-12 *)[data-slot="command-input-wrapper"] {
    height: calc(var(--spacing) * 12);
  }

  :is(.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:ring-0 *)[data-slot="navigation-menu-link"]:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  :is(.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:outline-none *)[data-slot="navigation-menu-link"]:focus {
    --tw-outline-style: none;
    outline-style: none;
  }

  :is(.\*\:data-\[slot\=select-value\]\:line-clamp-1 > *)[data-slot="select-value"] {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  :is(.\*\:data-\[slot\=select-value\]\:flex > *)[data-slot="select-value"] {
    display: flex;
  }

  :is(.\*\:data-\[slot\=select-value\]\:w-12 > *)[data-slot="select-value"] {
    width: calc(var(--spacing) * 12);
  }

  :is(.\*\:data-\[slot\=select-value\]\:items-center > *)[data-slot="select-value"] {
    align-items: center;
  }

  :is(.\*\:data-\[slot\=select-value\]\:gap-2 > *)[data-slot="select-value"] {
    gap: calc(var(--spacing) * 2);
  }

  .data-\[state\=active\]\:bg-background[data-state="active"] {
    background-color: var(--background);
  }

  .data-\[state\=active\]\:text-foreground[data-state="active"] {
    color: var(--foreground);
  }

  .data-\[state\=active\]\:shadow-sm[data-state="active"] {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[state\=checked\]\:translate-x-\[calc\(100\%-2px\)\][data-state="checked"] {
    --tw-translate-x: calc(100% - 2px);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[state\=checked\]\:border-\[var\(--iai-primary\)\][data-state="checked"] {
    border-color: var(--iai-primary);
  }

  .data-\[state\=checked\]\:border-primary[data-state="checked"] {
    border-color: var(--primary);
  }

  .data-\[state\=checked\]\:bg-\[var\(--iai-primary\)\][data-state="checked"] {
    background-color: var(--iai-primary);
  }

  .data-\[state\=checked\]\:bg-primary[data-state="checked"] {
    background-color: var(--primary);
  }

  .data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
    color: var(--primary-foreground);
  }

  .data-\[state\=closed\]\:animate-accordion-up[data-state="closed"] {
    animation: accordion-up var(--tw-animation-duration, var(--tw-duration, .2s)) var(--tw-ease, ease-out) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=closed\]\:animate-out[data-state="closed"] {
    animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=closed\]\:duration-300[data-state="closed"] {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
    --tw-exit-translate-y: 100%;
  }

  .data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
    --tw-exit-translate-x: -100%;
  }

  .data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
    --tw-exit-translate-x: 100%;
  }

  .data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
    --tw-exit-translate-y: -100%;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:animate-out:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
    animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:fade-out-0:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
    --tw-exit-opacity: 0;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:zoom-out-95:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="closed"] {
    --tw-exit-scale: .95;
  }

  .data-\[state\=hidden\]\:animate-out[data-state="hidden"] {
    animation: exit var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=hidden\]\:fade-out[data-state="hidden"] {
    --tw-exit-opacity: 0;
  }

  .data-\[state\=on\]\:bg-accent[data-state="on"] {
    background-color: var(--accent);
  }

  .data-\[state\=on\]\:text-accent-foreground[data-state="on"] {
    color: var(--accent-foreground);
  }

  .data-\[state\=open\]\:animate-accordion-down[data-state="open"] {
    animation: accordion-down var(--tw-animation-duration, var(--tw-duration, .2s)) var(--tw-ease, ease-out) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=open\]\:animate-in[data-state="open"] {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=open\]\:bg-accent[data-state="open"] {
    background-color: var(--accent);
  }

  .data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
    background-color: var(--accent);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
      background-color: color-mix(in oklab, var(--accent) 50%, transparent);
    }
  }

  .data-\[state\=open\]\:bg-secondary[data-state="open"] {
    background-color: var(--secondary);
  }

  .data-\[state\=open\]\:bg-sidebar-accent[data-state="open"] {
    background-color: var(--sidebar-accent);
  }

  .data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
    color: var(--accent-foreground);
  }

  .data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
    color: var(--muted-foreground);
  }

  .data-\[state\=open\]\:text-sidebar-accent-foreground[data-state="open"] {
    color: var(--sidebar-accent-foreground);
  }

  .data-\[state\=open\]\:opacity-100[data-state="open"] {
    opacity: 1;
  }

  .data-\[state\=open\]\:duration-500[data-state="open"] {
    --tw-duration: .5s;
    transition-duration: .5s;
  }

  .data-\[state\=open\]\:fade-in-0[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .data-\[state\=open\]\:zoom-in-90[data-state="open"] {
    --tw-enter-scale: .9;
  }

  .data-\[state\=open\]\:zoom-in-95[data-state="open"] {
    --tw-enter-scale: .95;
  }

  .data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
    --tw-enter-translate-y: 100%;
  }

  .data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
    --tw-enter-translate-x: -100%;
  }

  .data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
    --tw-enter-translate-x: 100%;
  }

  .data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
    --tw-enter-translate-y: -100%;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:animate-in:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:fade-in-0:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
    --tw-enter-opacity: 0;
  }

  .group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:zoom-in-95:is(:where(.group\/navigation-menu)[data-viewport="false"] *)[data-state="open"] {
    --tw-enter-scale: .95;
  }

  @media (hover: hover) {
    .data-\[state\=open\]\:hover\:bg-accent[data-state="open"]:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    .data-\[state\=open\]\:hover\:bg-sidebar-accent[data-state="open"]:hover {
      background-color: var(--sidebar-accent);
    }
  }

  @media (hover: hover) {
    .data-\[state\=open\]\:hover\:text-sidebar-accent-foreground[data-state="open"]:hover {
      color: var(--sidebar-accent-foreground);
    }
  }

  .data-\[state\=open\]\:focus\:bg-accent[data-state="open"]:focus {
    background-color: var(--accent);
  }

  .data-\[state\=selected\]\:bg-muted[data-state="selected"] {
    background-color: var(--muted);
  }

  .data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"] {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .data-\[state\=unchecked\]\:bg-input[data-state="unchecked"] {
    background-color: var(--input);
  }

  .data-\[state\=visible\]\:animate-in[data-state="visible"] {
    animation: enter var(--tw-animation-duration, var(--tw-duration, .15s)) var(--tw-ease, ease) var(--tw-animation-delay, 0s) var(--tw-animation-iteration-count, 1) var(--tw-animation-direction, normal) var(--tw-animation-fill-mode, none);
  }

  .data-\[state\=visible\]\:fade-in[data-state="visible"] {
    --tw-enter-opacity: 0;
  }

  .data-\[variant\=destructive\]\:text-destructive[data-variant="destructive"] {
    color: var(--destructive);
  }

  .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .data-\[variant\=destructive\]\:focus\:bg-destructive\/10[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, var(--destructive) 10%, transparent);
    }
  }

  .data-\[variant\=destructive\]\:focus\:text-destructive[data-variant="destructive"]:focus {
    color: var(--destructive);
  }

  .data-\[variant\=outline\]\:border-l-0[data-variant="outline"] {
    border-left-style: var(--tw-border-style);
    border-left-width: 0;
  }

  .data-\[variant\=outline\]\:shadow-xs[data-variant="outline"] {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, #0000000d);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .data-\[variant\=outline\]\:first\:border-l[data-variant="outline"]:first-child {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .data-\[vaul-drawer-direction\=bottom\]\:inset-x-0[data-vaul-drawer-direction="bottom"] {
    inset-inline: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=bottom\]\:bottom-0[data-vaul-drawer-direction="bottom"] {
    bottom: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=bottom\]\:mt-24[data-vaul-drawer-direction="bottom"] {
    margin-top: calc(var(--spacing) * 24);
  }

  .data-\[vaul-drawer-direction\=bottom\]\:max-h-\[80vh\][data-vaul-drawer-direction="bottom"] {
    max-height: 80vh;
  }

  .data-\[vaul-drawer-direction\=bottom\]\:rounded-t-lg[data-vaul-drawer-direction="bottom"] {
    border-top-left-radius: var(--radius);
    border-top-right-radius: var(--radius);
  }

  .data-\[vaul-drawer-direction\=bottom\]\:border-t[data-vaul-drawer-direction="bottom"] {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .data-\[vaul-drawer-direction\=left\]\:inset-y-0[data-vaul-drawer-direction="left"] {
    inset-block: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=left\]\:left-0[data-vaul-drawer-direction="left"] {
    left: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=left\]\:w-3\/4[data-vaul-drawer-direction="left"] {
    width: 75%;
  }

  .data-\[vaul-drawer-direction\=left\]\:border-r[data-vaul-drawer-direction="left"] {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }

  .data-\[vaul-drawer-direction\=right\]\:inset-y-0[data-vaul-drawer-direction="right"] {
    inset-block: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=right\]\:right-0[data-vaul-drawer-direction="right"] {
    right: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=right\]\:w-3\/4[data-vaul-drawer-direction="right"] {
    width: 75%;
  }

  .data-\[vaul-drawer-direction\=right\]\:border-l[data-vaul-drawer-direction="right"] {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }

  .data-\[vaul-drawer-direction\=top\]\:inset-x-0[data-vaul-drawer-direction="top"] {
    inset-inline: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=top\]\:top-0[data-vaul-drawer-direction="top"] {
    top: calc(var(--spacing) * 0);
  }

  .data-\[vaul-drawer-direction\=top\]\:mb-24[data-vaul-drawer-direction="top"] {
    margin-bottom: calc(var(--spacing) * 24);
  }

  .data-\[vaul-drawer-direction\=top\]\:max-h-\[80vh\][data-vaul-drawer-direction="top"] {
    max-height: 80vh;
  }

  .data-\[vaul-drawer-direction\=top\]\:rounded-b-lg[data-vaul-drawer-direction="top"] {
    border-bottom-right-radius: var(--radius);
    border-bottom-left-radius: var(--radius);
  }

  .data-\[vaul-drawer-direction\=top\]\:border-b[data-vaul-drawer-direction="top"] {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  @media (width >= 40rem) {
    .sm\:mb-4 {
      margin-bottom: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 40rem) {
    .sm\:mb-8 {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:block {
      display: block;
    }
  }

  @media (width >= 40rem) {
    .sm\:flex {
      display: flex;
    }
  }

  @media (width >= 40rem) {
    .sm\:hidden {
      display: none;
    }
  }

  @media (width >= 40rem) {
    .sm\:h-10 {
      height: calc(var(--spacing) * 10);
    }
  }

  @media (width >= 40rem) {
    .sm\:h-\[200px\] {
      height: 200px;
    }
  }

  @media (width >= 40rem) {
    .sm\:w-\[200px\] {
      width: 200px;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-2xl {
      max-width: var(--container-2xl);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-4xl {
      max-width: var(--container-4xl);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-\[425px\] {
      max-width: 425px;
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-lg {
      max-width: var(--container-lg);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-md {
      max-width: var(--container-md);
    }
  }

  @media (width >= 40rem) {
    .sm\:max-w-sm {
      max-width: var(--container-sm);
    }
  }

  @media (width >= 40rem) {
    .sm\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 40rem) {
    .sm\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-2\.5 {
      gap: calc(var(--spacing) * 2.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-6 {
      gap: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    :where(.sm\:space-y-6 > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }

  @media (width >= 40rem) {
    .sm\:border-t-0 {
      border-top-style: var(--tw-border-style);
      border-top-width: 0;
    }
  }

  @media (width >= 40rem) {
    .sm\:border-l {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }

  @media (width >= 40rem) {
    .sm\:p-6 {
      padding: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:p-8 {
      padding: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-5 {
      padding-inline: calc(var(--spacing) * 5);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-6 {
      padding-block: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:pt-6 {
      padding-top: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:pr-2\.5 {
      padding-right: calc(var(--spacing) * 2.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:pr-12 {
      padding-right: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 40rem) {
    .sm\:pb-6 {
      padding-bottom: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:pl-2\.5 {
      padding-left: calc(var(--spacing) * 2.5);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-left {
      text-align: left;
    }
  }

  @media (width >= 40rem) {
    .sm\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-4xl {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .data-\[vaul-drawer-direction\=left\]\:sm\:max-w-sm[data-vaul-drawer-direction="left"] {
      max-width: var(--container-sm);
    }
  }

  @media (width >= 40rem) {
    .data-\[vaul-drawer-direction\=right\]\:sm\:max-w-sm[data-vaul-drawer-direction="right"] {
      max-width: var(--container-sm);
    }
  }

  @media (width >= 48rem) {
    .md\:absolute {
      position: absolute;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 48rem) {
    .md\:col-span-3 {
      grid-column: span 3 / span 3;
    }
  }

  @media (width >= 48rem) {
    .md\:block {
      display: block;
    }
  }

  @media (width >= 48rem) {
    .md\:flex {
      display: flex;
    }
  }

  @media (width >= 48rem) {
    .md\:grid {
      display: grid;
    }
  }

  @media (width >= 48rem) {
    .md\:hidden {
      display: none;
    }
  }

  @media (width >= 48rem) {
    .md\:inline-block {
      display: inline-block;
    }
  }

  @media (width >= 48rem) {
    .md\:w-40 {
      width: calc(var(--spacing) * 40);
    }
  }

  @media (width >= 48rem) {
    .md\:w-96 {
      width: calc(var(--spacing) * 96);
    }
  }

  @media (width >= 48rem) {
    .md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] {
      width: var(--radix-navigation-menu-viewport-width);
    }
  }

  @media (width >= 48rem) {
    .md\:w-auto {
      width: auto;
    }
  }

  @media (width >= 48rem) {
    .md\:max-w-lg {
      max-width: var(--container-lg);
    }
  }

  @media (width >= 48rem) {
    .md\:flex-1 {
      flex: 1;
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:border-t-4 {
      border-top-style: var(--tw-border-style);
      border-top-width: 4px;
    }
  }

  @media (width >= 48rem) {
    .md\:border-l-0 {
      border-left-style: var(--tw-border-style);
      border-left-width: 0;
    }
  }

  @media (width >= 48rem) {
    .md\:px-0 {
      padding-inline: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 48rem) {
    .md\:pt-4 {
      padding-top: calc(var(--spacing) * 4);
    }
  }

  @media (width >= 48rem) {
    .md\:pb-0 {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:pl-0 {
      padding-left: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-sm {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:opacity-0 {
      opacity: 0;
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:m-2:is(:where(.peer)[data-variant="inset"] ~ *) {
      margin: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:ml-0:is(:where(.peer)[data-variant="inset"] ~ *) {
      margin-left: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:rounded-xl:is(:where(.peer)[data-variant="inset"] ~ *) {
      border-radius: calc(var(--radius)  + 4px);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:shadow-sm:is(:where(.peer)[data-variant="inset"] ~ *) {
      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  @media (width >= 48rem) {
    .md\:peer-data-\[variant\=inset\]\:peer-data-\[state\=collapsed\]\:ml-2:is(:where(.peer)[data-variant="inset"] ~ *):is(:where(.peer)[data-state="collapsed"] ~ *) {
      margin-left: calc(var(--spacing) * 2);
    }
  }

  @media (width >= 48rem) {
    .md\:after\:hidden:after {
      content: var(--tw-content);
      display: none;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-3 {
      grid-column: span 3 / span 3;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-5 {
      grid-column: span 5 / span 5;
    }
  }

  @media (width >= 64rem) {
    .lg\:mx-0 {
      margin-inline: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 64rem) {
    .lg\:mb-16 {
      margin-bottom: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 64rem) {
    .lg\:block {
      display: block;
    }
  }

  @media (width >= 64rem) {
    .lg\:flex {
      display: flex;
    }
  }

  @media (width >= 64rem) {
    .lg\:hidden {
      display: none;
    }
  }

  @media (width >= 64rem) {
    .lg\:h-full {
      height: 100%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-1\/2 {
      width: 50%;
    }
  }

  @media (width >= 64rem) {
    .lg\:w-56 {
      width: calc(var(--spacing) * 56);
    }
  }

  @media (width >= 64rem) {
    .lg\:w-64 {
      width: calc(var(--spacing) * 64);
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-2xl {
      max-width: var(--container-2xl);
    }
  }

  @media (width >= 64rem) {
    .lg\:max-w-xl {
      max-width: var(--container-xl);
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-5 {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-6 {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-7 {
      grid-template-columns: repeat(7, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:justify-end {
      justify-content: flex-end;
    }
  }

  @media (width >= 64rem) {
    .lg\:justify-start {
      justify-content: flex-start;
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-8 {
      gap: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:p-8 {
      padding: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:p-12 {
      padding: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 64rem) {
    .lg\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:py-24 {
      padding-block: calc(var(--spacing) * 24);
    }
  }

  @media (width >= 64rem) {
    .lg\:text-left {
      text-align: left;
    }
  }

  @media (width >= 64rem) {
    .lg\:text-2xl {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (width >= 64rem) {
    .lg\:text-xl {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }

  @media (width >= 80rem) {
    .xl\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (width >= 80rem) {
    .xl\:col-span-3 {
      grid-column: span 3 / span 3;
    }
  }

  @media (width >= 80rem) {
    .xl\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @container card (width >= 250px) {
    .\@\[250px\]\/card\:text-3xl {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }

  @container card (width >= 540px) {
    .\@\[540px\]\/card\:block {
      display: block;
    }
  }

  @container card (width >= 540px) {
    .\@\[540px\]\/card\:hidden {
      display: none;
    }
  }

  @container main (width >= 36rem) {
    .\@xl\/main\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @container main (width >= 64rem) {
    .\@5xl\/main\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  .dark\:border-input:is(.dark *) {
    border-color: var(--input);
  }

  .dark\:bg-destructive\/60:is(.dark *) {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-destructive\/60:is(.dark *) {
      background-color: color-mix(in oklab, var(--destructive) 60%, transparent);
    }
  }

  .dark\:bg-input\/30:is(.dark *) {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:bg-input\/30:is(.dark *) {
      background-color: color-mix(in oklab, var(--input) 30%, transparent);
    }
  }

  .dark\:text-foreground:is(.dark *) {
    color: var(--foreground);
  }

  @media (hover: hover) {
    .dark\:hover\:bg-accent\/50:is(.dark *):hover {
      background-color: var(--accent);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-accent\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--accent) 50%, transparent);
      }
    }
  }

  @media (hover: hover) {
    .dark\:hover\:bg-input\/50:is(.dark *):hover {
      background-color: var(--input);
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:hover\:bg-input\/50:is(.dark *):hover {
        background-color: color-mix(in oklab, var(--input) 50%, transparent);
      }
    }
  }

  .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  .dark\:data-\[active\=true\]\:aria-invalid\:ring-destructive\/40:is(.dark *)[data-active="true"][aria-invalid="true"] {
    --tw-ring-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[active\=true\]\:aria-invalid\:ring-destructive\/40:is(.dark *)[data-active="true"][aria-invalid="true"] {
      --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);
    }
  }

  :is(.dark\:\*\:data-\[slot\=card\]\:bg-card:is(.dark *) > *)[data-slot="card"] {
    background-color: var(--card);
  }

  .dark\:data-\[state\=checked\]\:bg-primary:is(.dark *)[data-state="checked"] {
    background-color: var(--primary);
  }

  .dark\:data-\[state\=checked\]\:bg-primary-foreground:is(.dark *)[data-state="checked"] {
    background-color: var(--primary-foreground);
  }

  .dark\:data-\[state\=unchecked\]\:bg-foreground:is(.dark *)[data-state="unchecked"] {
    background-color: var(--foreground);
  }

  .dark\:data-\[state\=unchecked\]\:bg-input\/80:is(.dark *)[data-state="unchecked"] {
    background-color: var(--input);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[state\=unchecked\]\:bg-input\/80:is(.dark *)[data-state="unchecked"] {
      background-color: color-mix(in oklab, var(--input) 80%, transparent);
    }
  }

  .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
    background-color: var(--destructive);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .dark\:data-\[variant\=destructive\]\:focus\:bg-destructive\/20:is(.dark *)[data-variant="destructive"]:focus {
      background-color: color-mix(in oklab, var(--destructive) 20%, transparent);
    }
  }

  .\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground .recharts-cartesian-axis-tick text {
    fill: var(--muted-foreground);
  }

  .\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
    stroke: var(--border);
  }

  @supports (color: color-mix(in lab, red, red)) {
    .\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50 .recharts-cartesian-grid line[stroke="#ccc"] {
      stroke: color-mix(in oklab, var(--border) 50%, transparent);
    }
  }

  .\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border .recharts-curve.recharts-tooltip-cursor {
    stroke: var(--border);
  }

  .\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-dot[stroke="#fff"] {
    stroke: #0000;
  }

  .\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-layer\]\:outline-hidden .recharts-layer {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-polar-grid [stroke="#ccc"] {
    stroke: var(--border);
  }

  .\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted .recharts-radial-bar-background-sector {
    fill: var(--muted);
  }

  .\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {
    fill: var(--muted);
  }

  .\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border .recharts-reference-line [stroke="#ccc"] {
    stroke: var(--border);
  }

  .\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-sector\]\:outline-hidden .recharts-sector {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent .recharts-sector[stroke="#fff"] {
    stroke: #0000;
  }

  .\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface {
    --tw-outline-style: none;
    outline-style: none;
  }

  @media (forced-colors: active) {
    .\[\&_\.recharts-surface\]\:outline-hidden .recharts-surface {
      outline-offset: 2px;
      outline: 2px solid #0000;
    }
  }

  .\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
    padding-block: calc(var(--spacing) * 1.5);
  }

  .\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
    color: var(--muted-foreground);
  }

  .\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0 [cmdk-group]:not([hidden]) ~ [cmdk-group] {
    padding-top: calc(var(--spacing) * 0);
  }

  .\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
    height: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
    width: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
    height: calc(var(--spacing) * 12);
  }

  .\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
    padding-inline: calc(var(--spacing) * 2);
  }

  .\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
    padding-block: calc(var(--spacing) * 3);
  }

  .\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
    height: calc(var(--spacing) * 5);
  }

  .\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
    width: calc(var(--spacing) * 5);
  }

  .\[\&_p\]\:leading-relaxed p {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .\[\&_svg\]\:pointer-events-none svg {
    pointer-events: none;
  }

  .\[\&_svg\]\:invisible svg {
    visibility: hidden;
  }

  .\[\&_svg\]\:size-4 svg {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&_svg\]\:shrink-0 svg {
    flex-shrink: 0;
  }

  .\[\&_svg\]\:text-muted-foreground svg {
    color: var(--muted-foreground);
  }

  .\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*="size-"]) {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*="text-"]) {
    color: var(--muted-foreground);
  }

  .focus\:\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-accent-foreground:focus svg:not([class*="text-"]) {
    color: var(--accent-foreground);
  }

  .\[\&_tr\]\:border-b tr {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .\[\&_tr\:last-child\]\:border-0 tr:last-child {
    border-style: var(--tw-border-style);
    border-width: 0;
  }

  .\[\&\:has\(\>\.day-range-end\)\]\:rounded-r-md:has( > .day-range-end) {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\>\.day-range-start\)\]\:rounded-l-md:has( > .day-range-start) {
    border-top-left-radius: calc(var(--radius)  - 2px);
    border-bottom-left-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\[aria-selected\]\)\]\:rounded-md:has([aria-selected]) {
    border-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]) {
    background-color: var(--accent);
  }

  .first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:first-child:has([aria-selected]) {
    border-top-left-radius: calc(var(--radius)  - 2px);
    border-bottom-left-radius: calc(var(--radius)  - 2px);
  }

  .last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:last-child:has([aria-selected]) {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has([aria-selected].day-range-end) {
    border-top-right-radius: calc(var(--radius)  - 2px);
    border-bottom-right-radius: calc(var(--radius)  - 2px);
  }

  .\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
    padding-right: calc(var(--spacing) * 0);
  }

  .\[\.border-b\]\:pb-6.border-b {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .\[\.border-t\]\:pt-6.border-t {
    padding-top: calc(var(--spacing) * 6);
  }

  :is(.\*\:\[span\]\:last\:flex > *):is(span):last-child {
    display: flex;
  }

  :is(.\*\:\[span\]\:last\:items-center > *):is(span):last-child {
    align-items: center;
  }

  :is(.\*\:\[span\]\:last\:gap-2 > *):is(span):last-child {
    gap: calc(var(--spacing) * 2);
  }

  :is(.data-\[variant\=destructive\]\:\*\:\[svg\]\:\!text-destructive[data-variant="destructive"] > *):is(svg) {
    color: var(--destructive) !important;
  }

  .\[\&\>\[role\=checkbox\]\]\:translate-y-\[2px\] > [role="checkbox"] {
    --tw-translate-y: 2px;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .\[\&\>button\]\:hidden > button {
    display: none;
  }

  .\[\&\>span\:first-child\]\:right-2 > span:first-child {
    right: calc(var(--spacing) * 2);
  }

  .\[\&\>span\:first-child\]\:left-auto > span:first-child {
    left: auto;
  }

  .\[\&\>span\:last-child\]\:truncate > span:last-child {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .\[\&\>svg\]\:pointer-events-none > svg {
    pointer-events: none;
  }

  .\[\&\>svg\]\:size-3 > svg {
    width: calc(var(--spacing) * 3);
    height: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:size-3\.5 > svg {
    width: calc(var(--spacing) * 3.5);
    height: calc(var(--spacing) * 3.5);
  }

  .\[\&\>svg\]\:size-4 > svg {
    width: calc(var(--spacing) * 4);
    height: calc(var(--spacing) * 4);
  }

  .\[\&\>svg\]\:h-2\.5 > svg {
    height: calc(var(--spacing) * 2.5);
  }

  .\[\&\>svg\]\:h-3 > svg {
    height: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:w-2\.5 > svg {
    width: calc(var(--spacing) * 2.5);
  }

  .\[\&\>svg\]\:w-3 > svg {
    width: calc(var(--spacing) * 3);
  }

  .\[\&\>svg\]\:shrink-0 > svg {
    flex-shrink: 0;
  }

  .\[\&\>svg\]\:translate-y-0\.5 > svg {
    --tw-translate-y: calc(var(--spacing) * .5);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .\[\&\>svg\]\:text-current > svg {
    color: currentColor;
  }

  .\[\&\>svg\]\:text-muted-foreground > svg {
    color: var(--muted-foreground);
  }

  .\[\&\>svg\]\:text-sidebar-accent-foreground > svg {
    color: var(--sidebar-accent-foreground);
  }

  .\[\&\>tr\]\:last\:border-b-0 > tr:last-child {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 0;
  }

  .\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction="vertical"] > div {
    rotate: 90deg;
  }

  .\[\&\[data-size\]\]\:h-8[data-size] {
    height: calc(var(--spacing) * 8);
  }

  .\[\&\[data-state\=closed\]\>button\]\:hidden[data-state="closed"] > button {
    display: none;
  }

  .\[\&\[data-state\=open\]\>\.alert\]\:hidden[data-state="open"] > .alert {
    display: none;
  }

  .\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state="open"] > svg {
    rotate: 180deg;
  }

  [data-side="left"][data-collapsible="offcanvas"] .\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
    right: calc(var(--spacing) * -2);
  }

  [data-side="left"][data-state="collapsed"] .\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
    cursor: e-resize;
  }

  [data-side="right"][data-collapsible="offcanvas"] .\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
    left: calc(var(--spacing) * -2);
  }

  [data-side="right"][data-state="collapsed"] .\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
    cursor: w-resize;
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-accent:hover {
      background-color: var(--accent);
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-destructive\/90:hover {
      background-color: var(--destructive);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-destructive\/90:hover {
        background-color: color-mix(in oklab, var(--destructive) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-primary\/90:hover {
      background-color: var(--primary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-primary\/90:hover {
        background-color: color-mix(in oklab, var(--primary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:bg-secondary\/90:hover {
      background-color: var(--secondary);
    }

    @supports (color: color-mix(in lab, red, red)) {
      a.\[a\&\]\:hover\:bg-secondary\/90:hover {
        background-color: color-mix(in oklab, var(--secondary) 90%, transparent);
      }
    }
  }

  @media (hover: hover) {
    a.\[a\&\]\:hover\:text-accent-foreground:hover {
      color: var(--accent-foreground);
    }
  }
}

@property --tw-animation-delay {
  syntax: "*";
  inherits: false;
  initial-value: 0s;
}

@property --tw-animation-direction {
  syntax: "*";
  inherits: false;
  initial-value: normal;
}

@property --tw-animation-duration {
  syntax: "*";
  inherits: false
}

@property --tw-animation-fill-mode {
  syntax: "*";
  inherits: false;
  initial-value: none;
}

@property --tw-animation-iteration-count {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-enter-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-enter-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-opacity {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-rotate {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-scale {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-exit-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-exit-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

body {
  overscroll-behavior: none;
  background-color: #0000;
}

:root {
  --font-sans: var(--font-inter);
  --header-height: calc(var(--spacing) * 12 + 1px);
}

@media (width >= 1024px) {
  .theme-scaled {
    --radius: .6rem;
    --text-lg: 1.05rem;
    --text-base: .85rem;
    --text-sm: .8rem;
    --spacing: .222222rem;
  }
}

.theme-scaled [data-slot="card"] {
  --spacing: .16rem;
}

.theme-scaled [data-slot="select-trigger"], .theme-scaled [data-slot="toggle-group-item"] {
  --spacing: .222222rem;
}

.theme-default, .theme-default-scaled {
  --primary: var(--color-neutral-600);
  --primary-foreground: var(--color-neutral-50);
}

:is(.theme-default, .theme-default-scaled):is(.dark *) {
  --primary: var(--color-neutral-500);
  --primary-foreground: var(--color-neutral-50);
}

.theme-blue, .theme-blue-scaled {
  --primary: var(--color-blue-600);
  --primary-foreground: var(--color-blue-50);
}

:is(.theme-blue, .theme-blue-scaled):is(.dark *) {
  --primary: var(--color-blue-500);
  --primary-foreground: var(--color-blue-50);
}

.theme-green, .theme-green-scaled {
  --primary: var(--color-lime-600);
  --primary-foreground: var(--color-lime-50);
}

:is(.theme-green, .theme-green-scaled):is(.dark *) {
  --primary: var(--color-lime-600);
  --primary-foreground: var(--color-lime-50);
}

.theme-amber, .theme-amber-scaled {
  --primary: var(--color-amber-600);
  --primary-foreground: var(--color-amber-50);
}

:is(.theme-amber, .theme-amber-scaled):is(.dark *) {
  --primary: var(--color-amber-500);
  --primary-foreground: var(--color-amber-50);
}

.theme-mono, .theme-mono-scaled {
  --font-sans: var(--font-mono);
  --primary: var(--color-neutral-600);
  --primary-foreground: var(--color-neutral-50);
}

:is(.theme-mono, .theme-mono-scaled):is(.dark *) {
  --primary: var(--color-neutral-500);
  --primary-foreground: var(--color-neutral-50);
}

:is(.theme-mono, .theme-mono-scaled) .rounded-xs, :is(.theme-mono, .theme-mono-scaled) .rounded-sm, :is(.theme-mono, .theme-mono-scaled) .rounded-md, :is(.theme-mono, .theme-mono-scaled) .rounded-lg, :is(.theme-mono, .theme-mono-scaled) .rounded-xl {
  border-radius: 0;
  border-radius: 0 !important;
}

:is(.theme-mono, .theme-mono-scaled) .shadow-xs, :is(.theme-mono, .theme-mono-scaled) .shadow-sm, :is(.theme-mono, .theme-mono-scaled) .shadow-md, :is(.theme-mono, .theme-mono-scaled) .shadow-lg, :is(.theme-mono, .theme-mono-scaled) .shadow-xl {
  --tw-shadow: 0 0 #0000 !important;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
}

:is(.theme-mono, .theme-mono-scaled) [data-slot="toggle-group"], :is(.theme-mono, .theme-mono-scaled) [data-slot="toggle-group-item"] {
  --tw-shadow: 0 0 #0000 !important;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
  border-radius: 0 !important;
}

:root, .light, .light-theme {
  --blue-1: #fbfdff;
  --blue-2: #f4faff;
  --blue-3: #e6f4fe;
  --blue-4: #d5efff;
  --blue-5: #c2e5ff;
  --blue-6: #acd8fc;
  --blue-7: #8ec8f6;
  --blue-8: #5eb1ef;
  --blue-9: #0090ff;
  --blue-10: #0588f0;
  --blue-11: #0d74ce;
  --blue-12: #113264;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, .light, .light-theme {
      --blue-1: color(display-p3 .986 .992 .999);
      --blue-2: color(display-p3 .96 .979 .998);
      --blue-3: color(display-p3 .912 .956 .991);
      --blue-4: color(display-p3 .853 .932 1);
      --blue-5: color(display-p3 .788 .894 .998);
      --blue-6: color(display-p3 .709 .843 .976);
      --blue-7: color(display-p3 .606 .777 .947);
      --blue-8: color(display-p3 .451 .688 .917);
      --blue-9: color(display-p3 .247 .556 .969);
      --blue-10: color(display-p3 .234 .523 .912);
      --blue-11: color(display-p3 .15 .44 .84);
      --blue-12: color(display-p3 .102 .193 .379);
    }
  }
}

.dark, .dark-theme {
  --blue-1: #0d1520;
  --blue-2: #111927;
  --blue-3: #0d2847;
  --blue-4: #003362;
  --blue-5: #004074;
  --blue-6: #104d87;
  --blue-7: #205d9e;
  --blue-8: #2870bd;
  --blue-9: #0090ff;
  --blue-10: #3b9eff;
  --blue-11: #70b8ff;
  --blue-12: #c2e6ff;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --blue-1: color(display-p3 .057 .081 .122);
      --blue-2: color(display-p3 .072 .098 .147);
      --blue-3: color(display-p3 .078 .154 .27);
      --blue-4: color(display-p3 .033 .197 .37);
      --blue-5: color(display-p3 .08 .245 .441);
      --blue-6: color(display-p3 .14 .298 .511);
      --blue-7: color(display-p3 .195 .361 .6);
      --blue-8: color(display-p3 .239 .434 .72);
      --blue-9: color(display-p3 .247 .556 .969);
      --blue-10: color(display-p3 .344 .612 .973);
      --blue-11: color(display-p3 .49 .72 1);
      --blue-12: color(display-p3 .788 .898 .99);
    }
  }
}

:root, .light, .light-theme {
  --slate-1: #fcfcfd;
  --slate-2: #f9f9fb;
  --slate-3: #f0f0f3;
  --slate-4: #e8e8ec;
  --slate-5: #e0e1e6;
  --slate-6: #d9d9e0;
  --slate-7: #cdced6;
  --slate-8: #b9bbc6;
  --slate-9: #8b8d98;
  --slate-10: #80838d;
  --slate-11: #60646c;
  --slate-12: #1c2024;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, .light, .light-theme {
      --slate-1: color(display-p3 .988 .988 .992);
      --slate-2: color(display-p3 .976 .976 .984);
      --slate-3: color(display-p3 .94 .941 .953);
      --slate-4: color(display-p3 .908 .909 .925);
      --slate-5: color(display-p3 .88 .881 .901);
      --slate-6: color(display-p3 .85 .852 .876);
      --slate-7: color(display-p3 .805 .808 .838);
      --slate-8: color(display-p3 .727 .733 .773);
      --slate-9: color(display-p3 .547 .553 .592);
      --slate-10: color(display-p3 .503 .512 .549);
      --slate-11: color(display-p3 .379 .392 .421);
      --slate-12: color(display-p3 .113 .125 .14);
    }
  }
}

.dark, .dark-theme {
  --slate-1: #111113;
  --slate-2: #18191b;
  --slate-3: #212225;
  --slate-4: #272a2d;
  --slate-5: #2e3135;
  --slate-6: #363a3f;
  --slate-7: #43484e;
  --slate-8: #5a6169;
  --slate-9: #696e77;
  --slate-10: #777b84;
  --slate-11: #b0b4ba;
  --slate-12: #edeef0;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --slate-1: color(display-p3 .067 .067 .074);
      --slate-2: color(display-p3 .095 .098 .105);
      --slate-3: color(display-p3 .13 .135 .145);
      --slate-4: color(display-p3 .156 .163 .176);
      --slate-5: color(display-p3 .183 .191 .206);
      --slate-6: color(display-p3 .215 .226 .244);
      --slate-7: color(display-p3 .265 .28 .302);
      --slate-8: color(display-p3 .357 .381 .409);
      --slate-9: color(display-p3 .415 .431 .463);
      --slate-10: color(display-p3 .469 .483 .514);
      --slate-11: color(display-p3 .692 .704 .728);
      --slate-12: color(display-p3 .93 .933 .94);
    }
  }
}

:root, .light, .light-theme {
  --grass-1: #fbfefb;
  --grass-2: #f5fbf5;
  --grass-3: #e9f6e9;
  --grass-4: #daf1db;
  --grass-5: #c9e8ca;
  --grass-6: #b2ddb5;
  --grass-7: #94ce9a;
  --grass-8: #65ba74;
  --grass-9: #46a758;
  --grass-10: #3e9b4f;
  --grass-11: #2a7e3b;
  --grass-12: #203c25;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, .light, .light-theme {
      --grass-1: color(display-p3 .986 .996 .985);
      --grass-2: color(display-p3 .966 .983 .964);
      --grass-3: color(display-p3 .923 .965 .917);
      --grass-4: color(display-p3 .872 .94 .865);
      --grass-5: color(display-p3 .811 .908 .802);
      --grass-6: color(display-p3 .733 .864 .724);
      --grass-7: color(display-p3 .628 .803 .622);
      --grass-8: color(display-p3 .477 .72 .482);
      --grass-9: color(display-p3 .38 .647 .378);
      --grass-10: color(display-p3 .344 .598 .342);
      --grass-11: color(display-p3 .263 .488 .261);
      --grass-12: color(display-p3 .151 .233 .153);
    }
  }
}

:root, .light, .light-theme {
  --cyan-1: #fafdfe;
  --cyan-2: #f2fafb;
  --cyan-3: #def7f9;
  --cyan-4: #caf1f6;
  --cyan-5: #b5e9f0;
  --cyan-6: #9ddde7;
  --cyan-7: #7dcedc;
  --cyan-8: #3db9cf;
  --cyan-9: #00a2c7;
  --cyan-10: #0797b9;
  --cyan-11: #107d98;
  --cyan-12: #0d3c48;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, .light, .light-theme {
      --cyan-1: color(display-p3 .982 .992 .996);
      --cyan-2: color(display-p3 .955 .981 .984);
      --cyan-3: color(display-p3 .888 .965 .975);
      --cyan-4: color(display-p3 .821 .941 .959);
      --cyan-5: color(display-p3 .751 .907 .935);
      --cyan-6: color(display-p3 .671 .862 .9);
      --cyan-7: color(display-p3 .564 .8 .854);
      --cyan-8: color(display-p3 .388 .715 .798);
      --cyan-9: color(display-p3 .282 .627 .765);
      --cyan-10: color(display-p3 .264 .583 .71);
      --cyan-11: color(display-p3 .08 .48 .63);
      --cyan-12: color(display-p3 .108 .232 .277);
    }
  }
}

:root, .light, .light-theme {
  --amber-1: #fefdfb;
  --amber-2: #fefbe9;
  --amber-3: #fff7c2;
  --amber-4: #ffee9c;
  --amber-5: #fbe577;
  --amber-6: #f3d673;
  --amber-7: #e9c162;
  --amber-8: #e2a336;
  --amber-9: #ffc53d;
  --amber-10: #ffba18;
  --amber-11: #ab6400;
  --amber-12: #4f3422;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, .light, .light-theme {
      --amber-1: color(display-p3 .995 .992 .985);
      --amber-2: color(display-p3 .994 .986 .921);
      --amber-3: color(display-p3 .994 .969 .782);
      --amber-4: color(display-p3 .989 .937 .65);
      --amber-5: color(display-p3 .97 .902 .527);
      --amber-6: color(display-p3 .936 .844 .506);
      --amber-7: color(display-p3 .89 .762 .443);
      --amber-8: color(display-p3 .85 .65 .3);
      --amber-9: color(display-p3 1 .77 .26);
      --amber-10: color(display-p3 .959 .741 .274);
      --amber-11: color(display-p3 .64 .4 0);
      --amber-12: color(display-p3 .294 .208 .145);
    }
  }
}

:root, .light, .light-theme {
  --red-1: #fffcfc;
  --red-2: #fff7f7;
  --red-3: #feebec;
  --red-4: #ffdbdc;
  --red-5: #ffcdce;
  --red-6: #fdbdbe;
  --red-7: #f4a9aa;
  --red-8: #eb8e90;
  --red-9: #e5484d;
  --red-10: #dc3e42;
  --red-11: #ce2c31;
  --red-12: #641723;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, .light, .light-theme {
      --red-1: color(display-p3 .998 .989 .988);
      --red-2: color(display-p3 .995 .971 .971);
      --red-3: color(display-p3 .985 .925 .925);
      --red-4: color(display-p3 .999 .866 .866);
      --red-5: color(display-p3 .984 .812 .811);
      --red-6: color(display-p3 .955 .751 .749);
      --red-7: color(display-p3 .915 .675 .672);
      --red-8: color(display-p3 .872 .575 .572);
      --red-9: color(display-p3 .83 .329 .324);
      --red-10: color(display-p3 .798 .294 .285);
      --red-11: color(display-p3 .744 .234 .222);
      --red-12: color(display-p3 .36 .115 .143);
    }
  }
}

.mdxeditor .cm-editor {
  --sp-font-mono: var(--font-mono);
  --sp-font-body: var(--font-body);
  padding: var(--sp-space-4) 0;
}

.mdxeditor .sp-editor .cm-editor {
  padding-bottom: 0;
}

.mdxeditor .cm-scroller {
  padding: 0 !important;
}

.mdxeditor .cm-focused {
  outline: none;
}

.mdxeditor .sp-wrapper {
  overflow: hidden;
}

.mdxeditor .sp-layout {
  border: none;
}

.mdxeditor .sp-cm pre {
  white-space: break-spaces;
  word-break: break-word;
  overflow-wrap: anywhere;
  flex-shrink: 1;
}

.mdxeditor .cm-mergeView .cm-scroller, .mdxeditor .cm-sourceView .cm-scroller {
  font-family: var(--font-mono);
  line-height: 1.3rem;
  font-size: var(--text-xs);
}

.mdxeditor .cm-gutters {
  font-size: var(--text-xxs);
  background: none;
}

.mdxeditor .cm-activeLine {
  background: none;
}

.mdxeditor .cm-tooltip-autocomplete {
  background: var(--baseBgSubtle);
}

.mdxeditor hr.selected[data-lexical-decorator="true"] {
  outline: 2px solid highlight;
}

._editorRoot_1e2ox_53 {
  --accentBase: var(--blue-1);
  --accentBgSubtle: var(--blue-2);
  --accentBg: var(--blue-3);
  --accentBgHover: var(--blue-4);
  --accentBgActive: var(--blue-5);
  --accentLine: var(--blue-6);
  --accentBorder: var(--blue-7);
  --accentBorderHover: var(--blue-8);
  --accentSolid: var(--blue-9);
  --accentSolidHover: var(--blue-10);
  --accentText: var(--blue-11);
  --accentTextContrast: var(--blue-12);
  --basePageBg: white;
  --baseBase: var(--slate-1);
  --baseBgSubtle: var(--slate-2);
  --baseBg: var(--slate-3);
  --baseBgHover: var(--slate-4);
  --baseBgActive: var(--slate-5);
  --baseLine: var(--slate-6);
  --baseBorder: var(--slate-7);
  --baseBorderHover: var(--slate-8);
  --baseSolid: var(--slate-9);
  --baseSolidHover: var(--slate-10);
  --baseText: var(--slate-11);
  --baseTextContrast: var(--slate-12);
  --admonitionTipBg: var(--cyan-4);
  --admonitionTipBorder: var(--cyan-8);
  --admonitionInfoBg: var(--grass-4);
  --admonitionInfoBorder: var(--grass-8);
  --admonitionCautionBg: var(--amber-4);
  --admonitionCautionBorder: var(--amber-8);
  --admonitionDangerBg: var(--red-4);
  --admonitionDangerBorder: var(--red-8);
  --admonitionNoteBg: var(--slate-4);
  --admonitionNoteBorder: var(--slate-8);
  --error-color: var(--red-10);
  --spacing-0: 0px;
  --spacing-px: 1px;
  --spacing-0_5: .125rem;
  --spacing-1: .25rem;
  --spacing-1_5: .375rem;
  --spacing-2: .5rem;
  --spacing-2_5: .625rem;
  --spacing-3: .75rem;
  --spacing-3_5: .875rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-9: 2.25rem;
  --spacing-10: 2.5rem;
  --spacing-11: 2.75rem;
  --spacing-12: 3rem;
  --spacing-14: 3.5rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-28: 7rem;
  --spacing-32: 8rem;
  --spacing-36: 9rem;
  --spacing-40: 10rem;
  --spacing-44: 11rem;
  --spacing-48: 12rem;
  --spacing-52: 13rem;
  --spacing-56: 14rem;
  --spacing-60: 15rem;
  --spacing-64: 16rem;
  --spacing-72: 18rem;
  --spacing-80: 20rem;
  --spacing-96: 24rem;
  --radius-none: 0px;
  --radius-small: var(--spacing-0_5);
  --radius-base: var(--spacing-1);
  --radius-medium: var(--spacing-1_5);
  --radius-large: var(--spacing-2);
  --radius-extra-large: var(--spacing-3);
  --radius-full: 9999px;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --font-body: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  --text-base: 1rem;
  --text-sm: .875rem;
  --text-xs: .75rem;
  --text-xxs: .6rem;
  font-family: var(--font-body);
  color: var(--baseText);
}

._nestedListItem_1e2ox_158 {
  list-style: none;
}

._toolbarRoot_1e2ox_162 {
  z-index: 2;
  gap: var(--spacing-1);
  border-radius: var(--radius-medium);
  padding: var(--spacing-1_5);
  background-color: var(--baseBg);
  width: inherit;
  flex-direction: row;
  align-items: center;
  display: flex;
  position: sticky;
  top: 0;
  overflow-x: auto;
}

._toolbarRoot_1e2ox_162 div[role="separator"] {
  margin: var(--spacing-2) var(--spacing-1);
  border-left: 1px solid var(--baseBorder);
  border-right: 1px solid var(--baseBase);
  height: var(--spacing-4);
}

._toolbarRoot_1e2ox_162 svg {
  color: var(--baseTextContrast);
  display: block;
}

._readOnlyToolbarRoot_1e2ox_189 {
  pointer-events: none;
  background: var(--baseBase);
}

._readOnlyToolbarRoot_1e2ox_189 > div {
  opacity: .5;
}

._toolbarModeSwitch_1e2ox_198 {
  border: 1px solid var(--baseBg);
  border-radius: var(--radius-medium);
  font-size: var(--text-xs);
  align-self: stretch;
  align-items: stretch;
  margin-left: auto;
  display: flex;
  opacity: 1 !important;
}

._toolbarModeSwitch_1e2ox_198 ._toolbarToggleItem_1e2ox_208 {
  padding-inline-start: var(--spacing-4);
  padding-inline-end: var(--spacing-4);
}

._toolbarModeSwitch_1e2ox_198 ._toolbarToggleItem_1e2ox_208:active, ._toolbarModeSwitch_1e2ox_198 ._toolbarToggleItem_1e2ox_208[data-state="on"] {
  background-color: var(--baseBorder);
}

._toolbarGroupOfGroups_1e2ox_219 {
  margin: 0 var(--spacing-1);
  display: flex;
}

._toolbarToggleSingleGroup_1e2ox_224:first-of-type ._toolbarToggleItem_1e2ox_208:only-child, ._toolbarToggleSingleGroup_1e2ox_224:only-child ._toolbarToggleItem_1e2ox_208:first-child, ._toolbarModeSwitch_1e2ox_198 ._toolbarToggleItem_1e2ox_208:first-child {
  border-top-left-radius: var(--radius-base);
  border-bottom-left-radius: var(--radius-base);
}

._toolbarToggleSingleGroup_1e2ox_224:last-of-type ._toolbarToggleItem_1e2ox_208:only-child, ._toolbarToggleSingleGroup_1e2ox_224:only-child ._toolbarToggleItem_1e2ox_208:last-child, ._toolbarModeSwitch_1e2ox_198 ._toolbarToggleItem_1e2ox_208:last-child {
  border-top-right-radius: var(--radius-base);
  border-bottom-right-radius: var(--radius-base);
}

._toolbarToggleItem_1e2ox_208, ._toolbarButton_1e2ox_239 {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding: var(--spacing-0_5);
}

@media (hover: hover) {
  ._toolbarToggleItem_1e2ox_208:hover, ._toolbarButton_1e2ox_239:hover {
    background-color: var(--baseBgActive);
  }
}

._toolbarToggleItem_1e2ox_208:active svg, ._toolbarButton_1e2ox_239:active svg {
  transform: translate(1px, 1px);
}

._toolbarToggleItem_1e2ox_208[data-state="on"], ._toolbarButton_1e2ox_239[data-state="on"], ._toolbarToggleItem_1e2ox_208:active, ._toolbarButton_1e2ox_239:active {
  color: var(--baseTextContrast);
  background-color: var(--baseBgActive);
}

._toolbarToggleItem_1e2ox_208[data-disabled], ._toolbarButton_1e2ox_239[data-disabled] {
  pointer-events: none;
}

._toolbarToggleItem_1e2ox_208[data-disabled] svg, ._toolbarButton_1e2ox_239[data-disabled] svg {
  color: var(--baseBorderHover);
}

._toolbarButton_1e2ox_239 {
  border-radius: var(--radius-base);
}

._toolbarButton_1e2ox_239 + ._toolbarButton_1e2ox_239 {
  margin-left: var(--spacing-1);
}

._activeToolbarButton_1e2ox_275 {
  color: var(--accentText);
}

._toolbarToggleSingleGroup_1e2ox_224 {
  white-space: nowrap;
  align-items: center;
  display: flex;
}

._toolbarNodeKindSelectContainer_1e2ox_285, ._toolbarButtonDropdownContainer_1e2ox_286, ._toolbarCodeBlockLanguageSelectContent_1e2ox_287, ._selectContainer_1e2ox_288 {
  filter: drop-shadow(0 2px 2px #0003);
  z-index: 3;
  width: var(--spacing-36);
  border-bottom-left-radius: var(--radius-base);
  border-bottom-right-radius: var(--radius-base);
  background-color: var(--basePageBg);
  font-size: var(--text-sm);
}

._toolbarButtonDropdownContainer_1e2ox_286, ._toolbarButtonDropdownContainer_1e2ox_286 ._selectItem_1e2ox_301:first-child {
  border-top-right-radius: var(--radius-base);
}

._toolbarNodeKindSelectTrigger_1e2ox_306, ._toolbarButtonSelectTrigger_1e2ox_307, ._selectTrigger_1e2ox_308 {
  color: inherit;
  width: var(--spacing-36);
  padding: var(--spacing-0_5) var(--spacing-1);
  border-radius: var(--radius-medium);
  white-space: nowrap;
  font-size: var(--text-sm);
  background-color: #0000;
  background-color: var(--basePageBg);
  margin: 0 var(--spacing-1);
  border: 0;
  flex-wrap: nowrap;
  align-items: center;
  padding-inline-start: var(--spacing-2);
  display: flex;
}

._toolbarNodeKindSelectTrigger_1e2ox_306[data-state="open"], ._toolbarButtonSelectTrigger_1e2ox_307[data-state="open"], ._selectTrigger_1e2ox_308[data-state="open"] {
  filter: drop-shadow(0 2px 2px #0003);
  border-bottom-right-radius: var(--radius-none);
  border-bottom-left-radius: var(--radius-none);
}

._selectTrigger_1e2ox_308[data-placeholder] > span:first-child {
  color: var(--baseBorderHover);
}

._toolbarButtonSelectTrigger_1e2ox_307 {
  width: auto;
  padding-inline-start: var(--spacing-2);
  padding-inline-end: var(--spacing-1);
  padding-block: var(--spacing-0_5);
}

._toolbarCodeBlockLanguageSelectTrigger_1e2ox_343, ._toolbarCodeBlockLanguageSelectContent_1e2ox_287 {
  width: var(--spacing-48);
}

._toolbarNodeKindSelectItem_1e2ox_348, ._selectItem_1e2ox_301 {
  cursor: default;
  padding: var(--spacing-2);
  display: flex;
}

._toolbarNodeKindSelectItem_1e2ox_348[data-highlighted], ._selectItem_1e2ox_301[data-highlighted] {
  background-color: var(--baseBg);
}

._toolbarNodeKindSelectItem_1e2ox_348[data-state="checked"], ._selectItem_1e2ox_301[data-state="checked"] {
  color: var(--baseTextContrast);
  background-color: var(--baseBg);
}

._toolbarNodeKindSelectItem_1e2ox_348[data-highlighted], ._selectItem_1e2ox_301[data-highlighted] {
  outline: none;
}

._toolbarNodeKindSelectItem_1e2ox_348:last-child, ._selectItem_1e2ox_301:last-child {
  border-bottom-left-radius: var(--radius-base);
  border-bottom-right-radius: var(--radius-base);
}

._toolbarNodeKindSelectDropdownArrow_1e2ox_373, ._selectDropdownArrow_1e2ox_374 {
  align-items: center;
  margin-left: auto;
  display: flex;
}

._contentEditable_1e2ox_380 {
  box-sizing: border-box;
  width: 100%;
  color: var(--baseTextContrast);
  padding: var(--spacing-3);
}

._contentEditable_1e2ox_380:focus {
  outline: none;
}

._codeMirrorWrapper_1e2ox_392 {
  margin-bottom: var(--spacing-5);
  border: 1px solid var(--baseLine);
  border-radius: var(--radius-medium);
  padding: .8rem;
  position: relative;
  overflow: hidden;
}

._sandPackWrapper_1e2ox_401 {
  margin-bottom: var(--spacing-5);
  border: 1px solid var(--baseLine);
  border-radius: var(--radius-medium);
  position: relative;
  overflow: hidden;
}

._codeMirrorToolbar_1e2ox_409 {
  gap: var(--spacing-1);
  padding: var(--spacing-1);
  z-index: 1;
  background-color: var(--baseBase);
  border-bottom-left-radius: var(--radius-base);
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
}

._frontmatterWrapper_1e2ox_413 {
  border-radius: var(--radius-medium);
  padding: var(--spacing-3);
  background-color: var(--baseBgSubtle);
}

._frontmatterWrapper_1e2ox_413[data-expanded="true"] {
  margin-bottom: var(--spacing-10);
}

._frontmatterToggleButton_1e2ox_423 {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--text-sm);
  display: flex;
}

._propertyPanelTitle_1e2ox_431 {
  font-size: var(--text-xs);
  padding-top: var(--spacing-2);
  padding-left: var(--spacing-2);
  margin: 0;
  font-weight: 400;
}

._propertyEditorTable_1e2ox_439 {
  table-layout: fixed;
  border-spacing: var(--spacing-2);
}

._propertyEditorTable_1e2ox_439 th {
  text-align: left;
  font-size: var(--text-sm);
  padding: var(--spacing-2) var(--spacing-3);
}

._propertyEditorTable_1e2ox_439 col:first-child {
  width: 30%;
}

._propertyEditorTable_1e2ox_439 col:nth-child(2) {
  width: 70%;
}

._propertyEditorTable_1e2ox_439 td:last-child ._iconButton_1e2ox_457 {
  margin-left: var(--spacing-4);
  margin-right: var(--spacing-4);
}

._propertyEditorTable_1e2ox_439 ._readOnlyColumnCell_1e2ox_462 {
  padding-left: 0;
}

._propertyEditorLabelCell_1e2ox_467 {
  font-weight: 400;
}

._readOnlyColumnCell_1e2ox_462 {
  padding-left: 0;
}

._buttonsFooter_1e2ox_475 {
  justify-content: flex-end;
  gap: var(--spacing-2);
  display: flex;
}

._propertyEditorInput_1e2ox_481 {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-base);
  border: 1px solid var(--baseBorder);
  background-color: var(--baseBase);
  font-size: var(--text-sm);
}

._iconButton_1e2ox_457 {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  color: var(--baseText);
}

@media (hover: hover) {
  ._iconButton_1e2ox_457:hover {
    color: var(--baseTextContrast);
  }
}

._iconButton_1e2ox_457:disabled, ._iconButton_1e2ox_457:disabled:hover {
  color: var(--baseLine);
}

._primaryButton_1e2ox_507, ._secondaryButton_1e2ox_508 {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--accentBorder);
  background-color: var(--accentSolidHover);
  color: var(--baseBase);
  font-size: var(--text-xs);
  border-radius: var(--radius-medium);
}

._primaryButton_1e2ox_507:disabled, ._secondaryButton_1e2ox_508:disabled {
  background: var(--accentLine);
  border-color: var(--accentBg);
}

._smallButton_1e2ox_523 {
  font-size: var(--text-xs);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-base);
}

._secondaryButton_1e2ox_508 {
  border: 1px solid var(--baseBorder);
  background-color: var(--baseSolidHover);
  color: var(--baseBase);
}

._dialogForm_1e2ox_535 {
  gap: var(--spacing-2);
  flex-direction: row;
  display: flex;
}

._linkDialogEditForm_1e2ox_541 {
  align-items: stretch;
  gap: var(--spacing-2);
  flex-direction: column;
  padding: 0;
  display: flex;
}

._linkDialogInputContainer_1e2ox_549 {
  flex-direction: column;
  align-items: stretch;
  display: flex;
}

._linkDialogInputWrapper_1e2ox_555 {
  background-color: var(--baseBase);
  border-radius: var(--radius-base);
  border: 1px solid var(--baseBorder);
  align-items: center;
  display: flex;
}

._linkDialogInputWrapper_1e2ox_555[data-visible-dropdown="true"] {
  border-bottom-left-radius: var(--radius-none);
  border-bottom-right-radius: var(--radius-none);
  border-bottom-width: 0;
}

._linkDialogInputWrapper_1e2ox_555 > button {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding-right: var(--spacing-2);
}

._linkDialogInput_1e2ox_549, ._dialogInput_1e2ox_576 {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  width: 20rem;
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--text-sm);
}

._linkDialogInput_1e2ox_549::-moz-placeholder, ._dialogInput_1e2ox_576::-moz-placeholder {
  color: var(--baseBorder);
}

._linkDialogInput_1e2ox_549::placeholder, ._dialogInput_1e2ox_576::placeholder {
  color: var(--baseBorder);
}

._linkDialogAnchor_1e2ox_587 {
  z-index: -1;
  background-color: highlight;
  position: fixed;
}

._linkDialogAnchor_1e2ox_587[data-visible="true"] {
  visibility: visible;
}

._linkDialogAnchor_1e2ox_587[data-visible="false"] {
  visibility: hidden;
}

._linkDialogPopoverContent_1e2ox_601, ._tableColumnEditorPopoverContent_1e2ox_602, ._dialogContent_1e2ox_603 {
  filter: drop-shadow(0 2px 2px #0003);
  align-items: center;
  gap: var(--spacing-0_5);
  border-radius: var(--radius-medium);
  border: 1px solid var(--baseBg);
  background-color: var(--basePageBg);
  padding: var(--spacing-1) var(--spacing-1);
  font-size: var(--text-sm);
  display: flex;
}

._largeDialogContent_1e2ox_615 {
  filter: drop-shadow(0 2px 2px #0003);
  gap: var(--spacing-0_5);
  border-radius: var(--radius-medium);
  border: 1px solid var(--baseBorder);
  background-color: var(--baseBgSubtle);
  padding: var(--spacing-4);
  font-size: var(--text-sm);
}

._dialogTitle_1e2ox_625 {
  font-size: var(--text-base);
  padding-left: var(--spacing-2);
  font-weight: 600;
}

._dialogCloseButton_1e2ox_631 {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  position: absolute;
  top: 10px;
  right: 10px;
}

._popoverContent_1e2ox_638 {
  filter: drop-shadow(0 2px 2px #0003);
  align-items: center;
  gap: var(--spacing-0_5);
  border-radius: var(--radius-medium);
  background-color: var(--baseBgSubtle);
  padding: var(--spacing-2) var(--spacing-2);
  font-size: var(--text-sm);
  z-index: 1;
  display: flex;
}

._popoverArrow_1e2ox_650 {
  fill: var(--basePageBg);
}

._linkDialogPreviewAnchor_1e2ox_654 {
  margin-right: var(--spacing-1);
  color: var(--accentText);
  border: 1px solid #0000;
  align-items: center;
  text-decoration: none;
  display: flex;
}

@media (hover: hover) {
  ._linkDialogPreviewAnchor_1e2ox_654:hover {
    color: var(--accentSolidHover);
  }
}

._linkDialogPreviewAnchor_1e2ox_654 span {
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 14rem;
  overflow-x: hidden;
}

._tooltipTrigger_1e2ox_677 {
  align-self: center;
}

._tooltipContent_1e2ox_681 {
  z-index: 2;
  border-radius: var(--radius-medium);
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--text-xs);
  background-color: var(--baseText);
  color: var(--baseBase);
  position: relative;
}

._tooltipContent_1e2ox_681 svg {
  fill: var(--baseText);
}

._actionButton_1e2ox_695 {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding: var(--spacing-1);
  color: var(--baseTextContrast);
  padding: var(--spacing-1) var(--spacing-1);
  border-radius: var(--radius-medium);
}

._actionButton_1e2ox_695 svg {
  display: block;
}

@media (hover: hover) {
  ._actionButton_1e2ox_695:hover {
    background-color: var(--baseBg);
  }
}

._actionButton_1e2ox_695:active svg {
  transform: translate(1px, 1px);
}

._actionButton_1e2ox_695[data-state="on"], ._actionButton_1e2ox_695:active {
  background-color: var(--baseBg);
  color: var(--baseTextContrast);
}

._primaryActionButton_1e2ox_702 {
  background-color: var(--accentSolid);
  color: var(--baseBase);
}

@media (hover: hover) {
  ._primaryActionButton_1e2ox_702:hover {
    background-color: var(--accentSolidHover);
    color: var(--baseBase);
  }
}

._tableEditor_1e2ox_714 {
  table-layout: fixed;
  border-spacing: 0;
  border-collapse: collapse;
  width: 100%;
  height: 100%;
}

._tableEditor_1e2ox_714 thead > tr > th {
  text-align: right;
}

._tableEditor_1e2ox_714 > tbody > tr > td:not(._toolCell_1e2ox_725), ._tableEditor_1e2ox_714 > tbody > tr > th:not(._toolCell_1e2ox_725):not([data-tool-cell="true"]) {
  border: 1px solid var(--baseBgActive);
  padding: var(--spacing-1) var(--spacing-2);
  white-space: normal;
}

:is(._tableEditor_1e2ox_714 > tbody > tr > td:not(._toolCell_1e2ox_725), ._tableEditor_1e2ox_714 > tbody > tr > th:not(._toolCell_1e2ox_725):not([data-tool-cell="true"])) > div {
  outline: none;
}

:is(._tableEditor_1e2ox_714 > tbody > tr > td:not(._toolCell_1e2ox_725), ._tableEditor_1e2ox_714 > tbody > tr > th:not(._toolCell_1e2ox_725):not([data-tool-cell="true"])) > div > p {
  margin: 0;
}

[data-active="true"]:is(._tableEditor_1e2ox_714 > tbody > tr > td:not(._toolCell_1e2ox_725), ._tableEditor_1e2ox_714 > tbody > tr > th:not(._toolCell_1e2ox_725):not([data-tool-cell="true"])) {
  outline: solid 1px var(--baseSolid);
}

._tableEditor_1e2ox_714 ._tableColumnEditorTrigger_1e2ox_744, ._tableEditor_1e2ox_714 ._tableRowEditorTrigger_1e2ox_745, ._tableEditor_1e2ox_714 ._addRowButton_1e2ox_746, ._tableEditor_1e2ox_714 ._addColumnButton_1e2ox_747, ._tableEditor_1e2ox_714 ._iconButton_1e2ox_457 {
  opacity: .15;
}

@media (hover: hover) {
  ._tableEditor_1e2ox_714:hover ._tableColumnEditorTrigger_1e2ox_744, ._tableEditor_1e2ox_714:hover ._tableRowEditorTrigger_1e2ox_745, ._tableEditor_1e2ox_714:hover ._addRowButton_1e2ox_746, ._tableEditor_1e2ox_714:hover ._addColumnButton_1e2ox_747, ._tableEditor_1e2ox_714:hover ._iconButton_1e2ox_457 {
    opacity: .3;
  }

  ._tableEditor_1e2ox_714:hover ._tableColumnEditorTrigger_1e2ox_744:hover, ._tableEditor_1e2ox_714:hover ._tableRowEditorTrigger_1e2ox_745:hover, ._tableEditor_1e2ox_714:hover ._addRowButton_1e2ox_746:hover, ._tableEditor_1e2ox_714:hover ._addColumnButton_1e2ox_747:hover, ._tableEditor_1e2ox_714:hover ._iconButton_1e2ox_457:hover {
    opacity: 1;
  }
}

._toolCell_1e2ox_725 {
  text-align: right;
}

._toolCell_1e2ox_725 button {
  margin: auto;
  display: block;
}

._tableColumnEditorTrigger_1e2ox_744 {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding: var(--spacing-1);
  color: var(--baseTextContrast);
  padding: var(--spacing-1);
  border-radius: var(--radius-full);
  opacity: .2;
}

._tableColumnEditorTrigger_1e2ox_744 svg {
  display: block;
}

@media (hover: hover) {
  ._tableColumnEditorTrigger_1e2ox_744:hover {
    background-color: var(--baseBg);
  }
}

._tableColumnEditorTrigger_1e2ox_744:active svg {
  transform: translate(1px, 1px);
}

._tableColumnEditorTrigger_1e2ox_744[data-state="on"], ._tableColumnEditorTrigger_1e2ox_744:active {
  background-color: var(--baseBg);
  color: var(--baseTextContrast);
}

._tableColumnEditorTrigger_1e2ox_744[data-active="true"] {
  opacity: 1 !important;
}

._tableColumnEditorToolbar_1e2ox_789 {
  display: flex;
}

._tableColumnEditorToolbar_1e2ox_789 > button {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding: var(--spacing-1);
  color: var(--baseTextContrast);
}

._tableColumnEditorToolbar_1e2ox_789 > button svg {
  display: block;
}

@media (hover: hover) {
  ._tableColumnEditorToolbar_1e2ox_789 > button:hover {
    background-color: var(--baseBg);
  }
}

._tableColumnEditorToolbar_1e2ox_789 > button:active svg {
  transform: translate(1px, 1px);
}

._tableColumnEditorToolbar_1e2ox_789 > button[data-state="on"], ._tableColumnEditorToolbar_1e2ox_789 > button:active {
  background-color: var(--baseBg);
  color: var(--baseTextContrast);
}

._tableColumnEditorToolbar_1e2ox_789 [role="separator"] {
  margin-left: var(--spacing-1);
  margin-right: var(--spacing-1);
}

._toggleGroupRoot_1e2ox_802 {
  display: inline-flex;
}

._toggleGroupRoot_1e2ox_802 button {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding: var(--spacing-1);
  color: var(--baseTextContrast);
}

._toggleGroupRoot_1e2ox_802 button svg {
  display: block;
}

@media (hover: hover) {
  ._toggleGroupRoot_1e2ox_802 button:hover {
    background-color: var(--baseBg);
  }
}

._toggleGroupRoot_1e2ox_802 button:active svg {
  transform: translate(1px, 1px);
}

._toggleGroupRoot_1e2ox_802 button[data-state="on"], ._toggleGroupRoot_1e2ox_802 button:active {
  background-color: var(--baseBg);
  color: var(--baseTextContrast);
}

._toggleGroupRoot_1e2ox_802 button:first-child {
  border-top-left-radius: var(--radius-base);
  border-bottom-left-radius: var(--radius-base);
}

._toggleGroupRoot_1e2ox_802 button:last-child {
  border-top-right-radius: var(--radius-base);
  border-bottom-right-radius: var(--radius-base);
}

._tableToolsColumn_1e2ox_820 {
  width: 2rem;
}

._tableToolsColumn_1e2ox_820 button {
  margin: auto;
  display: block;
}

._leftAlignedCell_1e2ox_829 {
  text-align: left;
}

._rightAlignedCell_1e2ox_833 {
  text-align: right;
}

._centeredCell_1e2ox_837 {
  text-align: center;
}

._addColumnButton_1e2ox_747, ._addRowButton_1e2ox_746 {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding: var(--spacing-1);
  color: var(--baseTextContrast);
  background-color: var(--baseBase);
  align-items: center;
  display: flex;
}

._addColumnButton_1e2ox_747 svg, ._addRowButton_1e2ox_746 svg {
  display: block;
}

@media (hover: hover) {
  ._addColumnButton_1e2ox_747:hover, ._addRowButton_1e2ox_746:hover {
    background-color: var(--baseBg);
  }
}

._addColumnButton_1e2ox_747:active svg, ._addRowButton_1e2ox_746:active svg {
  transform: translate(1px, 1px);
}

._addColumnButton_1e2ox_747[data-state="on"], ._addRowButton_1e2ox_746[data-state="on"], ._addColumnButton_1e2ox_747:active, ._addRowButton_1e2ox_746:active {
  background-color: var(--baseBg);
  color: var(--baseTextContrast);
}

._addColumnButton_1e2ox_747 svg, ._addRowButton_1e2ox_746 svg {
  margin: auto;
}

._addRowButton_1e2ox_746 {
  width: 100%;
  margin-top: var(--spacing-px);
  box-sizing: border-box;
  border-bottom-right-radius: var(--radius-medium);
  border-bottom-left-radius: var(--radius-medium);
}

._addColumnButton_1e2ox_747 {
  margin-left: var(--spacing-px);
  border-top-right-radius: var(--radius-medium);
  border-bottom-right-radius: var(--radius-medium);
  height: 100%;
}

._dialogOverlay_1e2ox_870 {
  background-color: var(--baseBase);
  z-index: 51;
  opacity: .5;
  animation: .15s cubic-bezier(.16, 1, .3, 1) _overlayShow_1e2ox_1;
  position: fixed;
  inset: 0;
}

._dialogContent_1e2ox_603, ._largeDialogContent_1e2ox_615 {
  z-index: 52;
  animation: .15s cubic-bezier(.16, 1, .3, 1) _contentShow_1e2ox_1;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

._dialogContent_1e2ox_603:focus, ._largeDialogContent_1e2ox_615:focus {
  outline: none;
}

@keyframes _overlayShow_1e2ox_1 {
  from {
    opacity: 0;
  }

  to {
    opacity: .5;
  }
}

@keyframes _contentShow_1e2ox_1 {
  from {
    opacity: 0;
    transform: translate(-50%, -48%)scale(.96);
  }

  to {
    opacity: 1;
    transform: translate(-50%, -50%)scale(1);
  }
}

._focusedImage_1e2ox_916 {
  outline: 2px solid highlight;
}

._imageWrapper_1e2ox_920 {
  display: inline-block;
  position: relative;
}

._imageWrapper_1e2ox_920[draggable="true"] {
  cursor: move;
  cursor: grab;
  cursor: -webkit-grab;
}

._editImageToolbar_1e2ox_933 {
  gap: var(--spacing-1);
  padding: var(--spacing-1);
  z-index: 1;
  background-color: var(--baseBase);
  border-bottom-left-radius: var(--radius-base);
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
}

._editImageButton_1e2ox_937 svg {
  display: block;
}

._inlineEditor_1e2ox_943 {
  border-radius: var(--radius-medium);
  padding: var(--spacing-1);
  gap: var(--spacing-2);
  background: var(--baseBg);
  align-items: center;
  display: inline-flex;
}

._blockEditor_1e2ox_952 {
  border-radius: var(--radius-medium);
  padding: var(--spacing-2);
  justify-content: stretch;
  gap: var(--spacing-2);
  background: var(--baseBg);
  align-items: center;
  display: flex;
}

._blockEditor_1e2ox_952 ._nestedEditor_1e2ox_961 {
  flex-grow: 1;
}

._nestedEditor_1e2ox_961 {
  background: var(--basePageBg);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-medium);
}

._nestedEditor_1e2ox_961 > p {
  margin: 0;
}

._nestedEditor_1e2ox_961:focus {
  outline: none;
}

._genericComponentName_1e2ox_980 {
  font-size: var(--text-sm);
  color: var(--baseText);
  padding-right: var(--spacing-2);
}

._diffSourceToggleWrapper_1e2ox_986 {
  pointer-events: auto;
  opacity: 1;
  margin-left: auto;
  position: sticky;
  right: 0;
}

._diffSourceToggle_1e2ox_986 {
  border-radius: var(--radius-medium);
  background-color: var(--baseBase);
  display: flex;
}

._diffSourceToggle_1e2ox_986 ._toolbarToggleItem_1e2ox_208 {
  padding: 0;
}

._diffSourceToggle_1e2ox_986 ._toolbarToggleItem_1e2ox_208 > span {
  padding: var(--spacing-1) var(--spacing-1);
  display: block;
}

._selectWithLabel_1e2ox_1012 {
  align-items: center;
  gap: var(--spacing-2);
  margin-left: var(--spacing-2);
  display: flex;
}

._selectWithLabel_1e2ox_1012 > label {
  font-size: var(--text-sm);
}

._selectWithLabel_1e2ox_1012 ._selectTrigger_1e2ox_308 {
  border: 1px solid var(--baseBorder);
}

._toolbarTitleMode_1e2ox_1027 {
  font-size: var(--text-sm);
  margin-left: var(--spacing-2);
}

._imageControlWrapperResizing_1e2ox_1032 {
  touch-action: none;
}

._imageResizer_1e2ox_1036 {
  background-color: var(--accentText);
  border: 1px solid var(--baseBg);
  width: 7px;
  height: 7px;
  display: block;
  position: absolute;
}

._imageResizer_1e2ox_1036._imageResizerN_1e2ox_1045 {
  cursor: n-resize;
  top: -6px;
  left: 48%;
}

._imageResizer_1e2ox_1036._imageResizerNe_1e2ox_1051 {
  cursor: ne-resize;
  top: -6px;
  right: -6px;
}

._imageResizer_1e2ox_1036._imageResizerE_1e2ox_1057 {
  cursor: e-resize;
  bottom: 48%;
  right: -6px;
}

._imageResizer_1e2ox_1036._imageResizerSe_1e2ox_1063 {
  cursor: nwse-resize;
  bottom: -2px;
  right: -6px;
}

._imageResizer_1e2ox_1036._imageResizerS_1e2ox_1063 {
  cursor: s-resize;
  bottom: -2px;
  left: 48%;
}

._imageResizer_1e2ox_1036._imageResizerSw_1e2ox_1075 {
  cursor: sw-resize;
  bottom: -2px;
  left: -6px;
}

._imageResizer_1e2ox_1036._imageResizerW_1e2ox_1081 {
  cursor: w-resize;
  bottom: 48%;
  left: -6px;
}

._imageResizer_1e2ox_1036._imageResizerNw_1e2ox_1087 {
  cursor: nw-resize;
  top: -6px;
  left: -6px;
}

._imagePlaceholder_1e2ox_1093 {
  border: 2px dashed;
  width: fit-content;
  height: fit-content;
  margin: 12px;
  padding: 48px;
}

._imageDimensionsContainer_1e2ox_1101 {
  gap: var(--spacing-4);
  display: flex;
}

._placeholder_1e2ox_1106 {
  color: var(--baseSolid);
  padding: var(--spacing-3);
  text-overflow: ellipsis;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  white-space: nowrap;
  pointer-events: none;
  display: inline-block;
  position: absolute;
  top: 0;
  overflow: hidden;
}

._rootContentEditableWrapper_1e2ox_1119 {
  position: relative;
}

._downshiftContainer_1e2ox_1123 {
  flex-direction: column;
  align-items: stretch;
  display: flex;
}

._downshiftInputWrapper_1e2ox_1129 {
  background-color: var(--baseBase);
  border-radius: var(--radius-base);
  border: 1px solid var(--baseBorder);
  align-items: center;
  display: flex;
}

._downshiftInputWrapper_1e2ox_1129[data-visible-dropdown="true"] {
  border-bottom-left-radius: var(--radius-none);
  border-bottom-right-radius: var(--radius-none);
  border-bottom-width: 0;
}

._downshiftInputWrapper_1e2ox_1129 > button {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  padding-right: var(--spacing-2);
}

._downshiftInput_1e2ox_1129 {
  all: unset;
  box-sizing: border-box;
  cursor: default;
  width: 20rem;
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--text-sm);
}

._downshiftInput_1e2ox_1129::-moz-placeholder {
  color: var(--baseBorder);
}

._downshiftInput_1e2ox_1129::placeholder {
  color: var(--baseBorder);
}

._downshiftAutocompleteContainer_1e2ox_1160 {
  position: relative;
}

._downshiftAutocompleteContainer_1e2ox_1160 ul {
  all: unset;
  box-sizing: border-box;
  font-size: var(--text-sm);
  border-bottom-left-radius: var(--radius-medium);
  border-bottom-right-radius: var(--radius-medium);
  width: 100%;
  max-height: var(--spacing-48);
  border: 1px solid var(--baseBorder);
  background-color: var(--baseBase);
  border-top-width: 0;
  display: none;
  position: absolute;
  overflow: hidden auto;
}

._downshiftAutocompleteContainer_1e2ox_1160 ul[data-visible="true"] {
  display: block;
}

._downshiftAutocompleteContainer_1e2ox_1160 ul li {
  padding: var(--spacing-2) var(--spacing-3);
  white-space: nowrap;
  margin-bottom: var(--spacing-1);
  text-overflow: ellipsis;
  overflow-x: hidden;
}

._downshiftAutocompleteContainer_1e2ox_1160 ul li[data-selected="true"] {
  background-color: var(--baseBgSubtle);
}

._downshiftAutocompleteContainer_1e2ox_1160 ul li[data-highlighted="true"] {
  background-color: var(--baseBgHover);
}

._downshiftAutocompleteContainer_1e2ox_1160 ul li:last-of-type {
  border-bottom-left-radius: var(--radius-medium);
  border-bottom-right-radius: var(--radius-medium);
}

._textInput_1e2ox_1206 {
  all: unset;
  border-radius: var(--radius-base);
  border: 1px solid var(--baseBorder);
  background-color: var(--baseBase);
  padding: var(--spacing-2) var(--spacing-3);
}

form._multiFieldForm_1e2ox_1214 {
  padding: var(--spacing-2);
  gap: var(--spacing-2);
  flex-direction: column;
  display: flex;
}

form._multiFieldForm_1e2ox_1214 ._formField_1e2ox_1220 {
  gap: var(--spacing-2);
  flex-direction: column;
  display: flex;
}

form._multiFieldForm_1e2ox_1214 ._formField_1e2ox_1220 label {
  font-size: var(--text-xs);
}

._markdownParseError_1e2ox_1231 {
  border-radius: var(--radius-base);
  border: 1px solid var(--error-color);
  padding: var(--spacing-2);
  margin-block: var(--spacing-2);
  color: var(--error-color);
  font-size: var(--text-xs);
}

._popupContainer_1e2ox_1240 {
  z-index: 2;
  position: relative;
}

._inputSizer_1e2ox_1245 {
  vertical-align: baseline;
  align-items: center;
  display: inline-grid;
  position: relative;
}

._inputSizer_1e2ox_1245:after, ._inputSizer_1e2ox_1245 input {
  width: auto;
  min-width: 1rem;
  font: inherit;
  resize: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  color: inherit;
  background: none;
  border: none;
  grid-area: 1 / 2;
  margin: 0;
  padding: 0 2px;
}

._inputSizer_1e2ox_1245 span {
  padding: .25em;
}

._inputSizer_1e2ox_1245:after {
  content: attr(data-value);
  white-space: pre-wrap;
}

:root, ._light_1tncs_1, ._light-theme_1tncs_1 {
  --blue-1: #fbfdff;
  --blue-2: #f4faff;
  --blue-3: #e6f4fe;
  --blue-4: #d5efff;
  --blue-5: #c2e5ff;
  --blue-6: #acd8fc;
  --blue-7: #8ec8f6;
  --blue-8: #5eb1ef;
  --blue-9: #0090ff;
  --blue-10: #0588f0;
  --blue-11: #0d74ce;
  --blue-12: #113264;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, ._light_1tncs_1, ._light-theme_1tncs_1 {
      --blue-1: color(display-p3 .986 .992 .999);
      --blue-2: color(display-p3 .96 .979 .998);
      --blue-3: color(display-p3 .912 .956 .991);
      --blue-4: color(display-p3 .853 .932 1);
      --blue-5: color(display-p3 .788 .894 .998);
      --blue-6: color(display-p3 .709 .843 .976);
      --blue-7: color(display-p3 .606 .777 .947);
      --blue-8: color(display-p3 .451 .688 .917);
      --blue-9: color(display-p3 .247 .556 .969);
      --blue-10: color(display-p3 .234 .523 .912);
      --blue-11: color(display-p3 .15 .44 .84);
      --blue-12: color(display-p3 .102 .193 .379);
    }
  }
}

._dark_1tncs_1, ._dark-theme_1tncs_1 {
  --blue-1: #0d1520;
  --blue-2: #111927;
  --blue-3: #0d2847;
  --blue-4: #003362;
  --blue-5: #004074;
  --blue-6: #104d87;
  --blue-7: #205d9e;
  --blue-8: #2870bd;
  --blue-9: #0090ff;
  --blue-10: #3b9eff;
  --blue-11: #70b8ff;
  --blue-12: #c2e6ff;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    ._dark_1tncs_1, ._dark-theme_1tncs_1 {
      --blue-1: color(display-p3 .057 .081 .122);
      --blue-2: color(display-p3 .072 .098 .147);
      --blue-3: color(display-p3 .078 .154 .27);
      --blue-4: color(display-p3 .033 .197 .37);
      --blue-5: color(display-p3 .08 .245 .441);
      --blue-6: color(display-p3 .14 .298 .511);
      --blue-7: color(display-p3 .195 .361 .6);
      --blue-8: color(display-p3 .239 .434 .72);
      --blue-9: color(display-p3 .247 .556 .969);
      --blue-10: color(display-p3 .344 .612 .973);
      --blue-11: color(display-p3 .49 .72 1);
      --blue-12: color(display-p3 .788 .898 .99);
    }
  }
}

:root, ._light_1tncs_1, ._light-theme_1tncs_1 {
  --slate-1: #fcfcfd;
  --slate-2: #f9f9fb;
  --slate-3: #f0f0f3;
  --slate-4: #e8e8ec;
  --slate-5: #e0e1e6;
  --slate-6: #d9d9e0;
  --slate-7: #cdced6;
  --slate-8: #b9bbc6;
  --slate-9: #8b8d98;
  --slate-10: #80838d;
  --slate-11: #60646c;
  --slate-12: #1c2024;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, ._light_1tncs_1, ._light-theme_1tncs_1 {
      --slate-1: color(display-p3 .988 .988 .992);
      --slate-2: color(display-p3 .976 .976 .984);
      --slate-3: color(display-p3 .94 .941 .953);
      --slate-4: color(display-p3 .908 .909 .925);
      --slate-5: color(display-p3 .88 .881 .901);
      --slate-6: color(display-p3 .85 .852 .876);
      --slate-7: color(display-p3 .805 .808 .838);
      --slate-8: color(display-p3 .727 .733 .773);
      --slate-9: color(display-p3 .547 .553 .592);
      --slate-10: color(display-p3 .503 .512 .549);
      --slate-11: color(display-p3 .379 .392 .421);
      --slate-12: color(display-p3 .113 .125 .14);
    }
  }
}

._dark_1tncs_1, ._dark-theme_1tncs_1 {
  --slate-1: #111113;
  --slate-2: #18191b;
  --slate-3: #212225;
  --slate-4: #272a2d;
  --slate-5: #2e3135;
  --slate-6: #363a3f;
  --slate-7: #43484e;
  --slate-8: #5a6169;
  --slate-9: #696e77;
  --slate-10: #777b84;
  --slate-11: #b0b4ba;
  --slate-12: #edeef0;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    ._dark_1tncs_1, ._dark-theme_1tncs_1 {
      --slate-1: color(display-p3 .067 .067 .074);
      --slate-2: color(display-p3 .095 .098 .105);
      --slate-3: color(display-p3 .13 .135 .145);
      --slate-4: color(display-p3 .156 .163 .176);
      --slate-5: color(display-p3 .183 .191 .206);
      --slate-6: color(display-p3 .215 .226 .244);
      --slate-7: color(display-p3 .265 .28 .302);
      --slate-8: color(display-p3 .357 .381 .409);
      --slate-9: color(display-p3 .415 .431 .463);
      --slate-10: color(display-p3 .469 .483 .514);
      --slate-11: color(display-p3 .692 .704 .728);
      --slate-12: color(display-p3 .93 .933 .94);
    }
  }
}

:root, ._light_1tncs_1, ._light-theme_1tncs_1 {
  --grass-1: #fbfefb;
  --grass-2: #f5fbf5;
  --grass-3: #e9f6e9;
  --grass-4: #daf1db;
  --grass-5: #c9e8ca;
  --grass-6: #b2ddb5;
  --grass-7: #94ce9a;
  --grass-8: #65ba74;
  --grass-9: #46a758;
  --grass-10: #3e9b4f;
  --grass-11: #2a7e3b;
  --grass-12: #203c25;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, ._light_1tncs_1, ._light-theme_1tncs_1 {
      --grass-1: color(display-p3 .986 .996 .985);
      --grass-2: color(display-p3 .966 .983 .964);
      --grass-3: color(display-p3 .923 .965 .917);
      --grass-4: color(display-p3 .872 .94 .865);
      --grass-5: color(display-p3 .811 .908 .802);
      --grass-6: color(display-p3 .733 .864 .724);
      --grass-7: color(display-p3 .628 .803 .622);
      --grass-8: color(display-p3 .477 .72 .482);
      --grass-9: color(display-p3 .38 .647 .378);
      --grass-10: color(display-p3 .344 .598 .342);
      --grass-11: color(display-p3 .263 .488 .261);
      --grass-12: color(display-p3 .151 .233 .153);
    }
  }
}

:root, ._light_1tncs_1, ._light-theme_1tncs_1 {
  --cyan-1: #fafdfe;
  --cyan-2: #f2fafb;
  --cyan-3: #def7f9;
  --cyan-4: #caf1f6;
  --cyan-5: #b5e9f0;
  --cyan-6: #9ddde7;
  --cyan-7: #7dcedc;
  --cyan-8: #3db9cf;
  --cyan-9: #00a2c7;
  --cyan-10: #0797b9;
  --cyan-11: #107d98;
  --cyan-12: #0d3c48;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, ._light_1tncs_1, ._light-theme_1tncs_1 {
      --cyan-1: color(display-p3 .982 .992 .996);
      --cyan-2: color(display-p3 .955 .981 .984);
      --cyan-3: color(display-p3 .888 .965 .975);
      --cyan-4: color(display-p3 .821 .941 .959);
      --cyan-5: color(display-p3 .751 .907 .935);
      --cyan-6: color(display-p3 .671 .862 .9);
      --cyan-7: color(display-p3 .564 .8 .854);
      --cyan-8: color(display-p3 .388 .715 .798);
      --cyan-9: color(display-p3 .282 .627 .765);
      --cyan-10: color(display-p3 .264 .583 .71);
      --cyan-11: color(display-p3 .08 .48 .63);
      --cyan-12: color(display-p3 .108 .232 .277);
    }
  }
}

:root, ._light_1tncs_1, ._light-theme_1tncs_1 {
  --amber-1: #fefdfb;
  --amber-2: #fefbe9;
  --amber-3: #fff7c2;
  --amber-4: #ffee9c;
  --amber-5: #fbe577;
  --amber-6: #f3d673;
  --amber-7: #e9c162;
  --amber-8: #e2a336;
  --amber-9: #ffc53d;
  --amber-10: #ffba18;
  --amber-11: #ab6400;
  --amber-12: #4f3422;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, ._light_1tncs_1, ._light-theme_1tncs_1 {
      --amber-1: color(display-p3 .995 .992 .985);
      --amber-2: color(display-p3 .994 .986 .921);
      --amber-3: color(display-p3 .994 .969 .782);
      --amber-4: color(display-p3 .989 .937 .65);
      --amber-5: color(display-p3 .97 .902 .527);
      --amber-6: color(display-p3 .936 .844 .506);
      --amber-7: color(display-p3 .89 .762 .443);
      --amber-8: color(display-p3 .85 .65 .3);
      --amber-9: color(display-p3 1 .77 .26);
      --amber-10: color(display-p3 .959 .741 .274);
      --amber-11: color(display-p3 .64 .4 0);
      --amber-12: color(display-p3 .294 .208 .145);
    }
  }
}

:root, ._light_1tncs_1, ._light-theme_1tncs_1 {
  --red-1: #fffcfc;
  --red-2: #fff7f7;
  --red-3: #feebec;
  --red-4: #ffdbdc;
  --red-5: #ffcdce;
  --red-6: #fdbdbe;
  --red-7: #f4a9aa;
  --red-8: #eb8e90;
  --red-9: #e5484d;
  --red-10: #dc3e42;
  --red-11: #ce2c31;
  --red-12: #641723;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, ._light_1tncs_1, ._light-theme_1tncs_1 {
      --red-1: color(display-p3 .998 .989 .988);
      --red-2: color(display-p3 .995 .971 .971);
      --red-3: color(display-p3 .985 .925 .925);
      --red-4: color(display-p3 .999 .866 .866);
      --red-5: color(display-p3 .984 .812 .811);
      --red-6: color(display-p3 .955 .751 .749);
      --red-7: color(display-p3 .915 .675 .672);
      --red-8: color(display-p3 .872 .575 .572);
      --red-9: color(display-p3 .83 .329 .324);
      --red-10: color(display-p3 .798 .294 .285);
      --red-11: color(display-p3 .744 .234 .222);
      --red-12: color(display-p3 .36 .115 .143);
    }
  }
}

._bold_1tncs_10 {
  font-weight: 700;
}

._italic_1tncs_14 {
  font-style: italic;
}

._underline_1tncs_18 {
  text-decoration: underline;
}

._strikethrough_1tncs_34 {
  text-decoration: line-through;
}

._underlineStrikethrough_1tncs_38 {
  text-decoration: underline line-through;
}

._subscript_1tncs_42 {
  font-size: .8em;
  vertical-align: sub !important;
}

._superscript_1tncs_47 {
  vertical-align: super;
  font-size: .8em;
}

._code_1tncs_52 {
  background-color: var(--baseBg);
  font-family: var(--font-mono);
  padding: 1px .25rem;
  font-size: 94%;
}

._nestedListItem_1tncs_59 {
  list-style: none;
}

._listitem_1tncs_69 {
  margin: var(--spacing-2) 0;
}

._listItemChecked_1tncs_73, ._listItemUnchecked_1tncs_74 {
  margin-left: 0;
  margin-right: 0;
  padding-left: var(--spacing-6);
  padding-right: var(--spacing-6);
  outline: none;
  margin-inline-start: -1rem;
  list-style-type: none;
  position: relative;
}

._listItemChecked_1tncs_73 {
  text-decoration: line-through;
}

._listItemUnchecked_1tncs_74:before, ._listItemChecked_1tncs_73:before {
  content: "";
  width: var(--spacing-4);
  height: var(--spacing-4);
  cursor: pointer;
  background-size: cover;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

._listItemUnchecked_1tncs_74[dir="rtl"]:before, ._listItemChecked_1tncs_73[dir="rtl"]:before {
  left: auto;
  right: 0;
}

._listItemUnchecked_1tncs_74:focus:before, ._listItemChecked_1tncs_73:focus:before {
  box-shadow: 0 0 0 2px var(--accentBgActive);
  border-radius: var(--radius-small);
}

._listItemUnchecked_1tncs_74:before {
  border: 1px solid var(--baseBorder);
  border-radius: var(--radius-small);
}

._listItemChecked_1tncs_73:before {
  border: 1px solid var(--accentBorder);
  border-radius: var(--radius-small);
  background-color: var(--accentSolid);
  background-repeat: no-repeat;
}

._listItemChecked_1tncs_73:after {
  content: "";
  cursor: pointer;
  border-color: var(--baseBase);
  top: var(--spacing-0_5);
  width: var(--spacing-1);
  left: var(--spacing-1_5);
  right: var(--spacing-1_5);
  height: var(--spacing-2);
  border-style: solid;
  border-width: 0 var(--spacing-0_5) var(--spacing-0_5) 0;
  display: block;
  position: absolute;
  transform: rotate(45deg);
}

._nestedListItem_1tncs_59 {
  list-style-type: none;
}

._nestedListItem_1tncs_59:before, ._nestedListItem_1tncs_59:after {
  display: none;
}

._admonitionDanger_1tncs_151, ._admonitionInfo_1tncs_152, ._admonitionNote_1tncs_153, ._admonitionTip_1tncs_154, ._admonitionCaution_1tncs_155 {
  padding: var(--spacing-2);
  margin-top: var(--spacing-2);
  margin-bottom: var(--spacing-2);
  border-left: 3px solid var(--admonitionBorder);
  background-color: var(--admonitionBg);
}

._admonitionInfo_1tncs_152 {
  --admonitionBorder: var(--admonitionInfoBorder);
  --admonitionBg: var(--admonitionInfoBg);
}

._admonitionTip_1tncs_154 {
  --admonitionBorder: var(--admonitionTipBorder);
  --admonitionBg: var(--admonitionTipBg);
}

._admonitionCaution_1tncs_155 {
  --admonitionBorder: var(--admonitionCautionBorder);
  --admonitionBg: var(--admonitionCautionBg);
}

._admonitionDanger_1tncs_151 {
  --admonitionBorder: var(--admonitionDangerBorder);
  --admonitionBg: var(--admonitionDangerBg);
}

._admonitionNote_1tncs_153 {
  --admonitionBorder: var(--admonitionNoteBorder);
  --admonitionBg: var(--admonitionNoteBg);
}

._mdxExpression_1tncs_188 {
  font-family: var(--font-mono);
  color: var(--accentText);
  font-size: 84%;
}

._mdxExpression_1tncs_188 input:focus-visible {
  outline: none;
}

:root {
  --radius: .625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(.145 0 0);
  --primary: oklch(.35 .15 350);
  --primary-foreground: oklch(.985 0 0);
  --secondary: oklch(.97 0 0);
  --secondary-foreground: oklch(.205 0 0);
  --muted: oklch(.97 0 0);
  --muted-foreground: oklch(.556 0 0);
  --accent: oklch(.32 .15 350);
  --accent-foreground: oklch(.985 0 0);
  --destructive: oklch(.577 .245 27.325);
  --border: oklch(.922 0 0);
  --input: oklch(.922 0 0);
  --ring: oklch(.35 .15 350);
  --chart-1: oklch(.646 .222 41.116);
  --chart-2: oklch(.6 .118 184.704);
  --chart-3: oklch(.398 .07 227.392);
  --chart-4: oklch(.828 .189 84.429);
  --chart-5: oklch(.769 .188 70.08);
  --sidebar: oklch(.985 0 0);
  --sidebar-foreground: oklch(.145 0 0);
  --sidebar-primary: oklch(.35 .15 350);
  --sidebar-primary-foreground: oklch(.985 0 0);
  --sidebar-accent: oklch(.97 0 0);
  --sidebar-accent-foreground: oklch(.205 0 0);
  --sidebar-border: oklch(.922 0 0);
  --sidebar-ring: oklch(.35 .15 350);
  --iai-primary: #8b1538;
  --iai-secondary: #a91b3c;
  --iai-accent: #6b1227;
}

.dark {
  --background: oklch(.145 0 0);
  --foreground: oklch(.985 0 0);
  --card: oklch(.205 0 0);
  --card-foreground: oklch(.985 0 0);
  --popover: oklch(.269 0 0);
  --popover-foreground: oklch(.985 0 0);
  --primary: oklch(.45 .18 350);
  --primary-foreground: oklch(.985 0 0);
  --secondary: oklch(.269 0 0);
  --secondary-foreground: oklch(.985 0 0);
  --muted: oklch(.269 0 0);
  --muted-foreground: oklch(.708 0 0);
  --accent: oklch(.42 .18 350);
  --accent-foreground: oklch(.985 0 0);
  --destructive: oklch(.704 .191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(.45 .18 350);
  --chart-1: oklch(.488 .243 264.376);
  --chart-2: oklch(.696 .17 162.48);
  --chart-3: oklch(.769 .188 70.08);
  --chart-4: oklch(.627 .265 303.9);
  --chart-5: oklch(.645 .246 16.439);
  --sidebar: oklch(.205 0 0);
  --sidebar-foreground: oklch(.985 0 0);
  --sidebar-primary: oklch(.45 .18 350);
  --sidebar-primary-foreground: oklch(.985 0 0);
  --sidebar-accent: oklch(.269 0 0);
  --sidebar-accent-foreground: oklch(.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(.45 .18 350);
}

::view-transition-old(root), ::view-transition-new(root) {
  mix-blend-mode: normal;
  animation: none;
}

::view-transition-old(root) {
  z-index: 0;
}

::view-transition-new(root) {
  z-index: 1;
}

@keyframes reveal {
  from {
    clip-path: circle(0% at var(--x, 50%) var(--y, 50%));
    opacity: .7;
  }

  to {
    clip-path: circle(150% at var(--x, 50%) var(--y, 50%));
    opacity: 1;
  }
}

::view-transition-new(root) {
  animation: .4s ease-in-out forwards reveal;
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-ordinal {
  syntax: "*";
  inherits: false
}

@property --tw-slashed-zero {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-figure {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false
}

@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-content {
  syntax: "*";
  inherits: false;
  initial-value: "";
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes ping {
  75%, 100% {
    opacity: 0;
    transform: scale(2);
  }
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

@keyframes bounce {
  0%, 100% {
    animation-timing-function: cubic-bezier(.8, 0, 1, 1);
    transform: translateY(-25%);
  }

  50% {
    animation-timing-function: cubic-bezier(0, 0, .2, 1);
    transform: none;
  }
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

@keyframes accordion-down {
  from {
    height: 0;
  }

  to {
    height: var(--radix-accordion-content-height, var(--bits-accordion-content-height, var(--reka-accordion-content-height, var(--kb-accordion-content-height, auto))));
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height, var(--bits-accordion-content-height, var(--reka-accordion-content-height, var(--kb-accordion-content-height, auto))));
  }

  to {
    height: 0;
  }
}

@keyframes caret-blink {
  0%, 70%, 100% {
    opacity: 1;
  }

  20%, 50% {
    opacity: 0;
  }
}

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/