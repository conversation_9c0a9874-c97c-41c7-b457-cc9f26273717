//content-viewer.tsx
import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Play, 
  FileText, 
  BookMarked, 
  CheckCircle, 
  Lock, 
  ChevronLeft, 
  ChevronRight,
  Clock,
  Target,
  Award,
  Download,
  Eye,
  ExternalLink,
  Image
} from 'lucide-react';
import { Course, Content, Quiz } from '@/types/lms';

interface ContentViewerProps {
  course: Course;
  currentModuleId: string;
  currentChapterId: string;
  currentContentId: string;
  onNavigate: (moduleId: string, chapterId: string, contentId: string) => void;
  onStartQuiz: (quizId: string, quizType: 'chapter' | 'module' | 'final') => void;
  onContentComplete: (contentId: string) => void;
}

export const ContentViewer: React.FC<ContentViewerProps> = ({
  course,
  currentModuleId,
  currentChapterId,
  currentContentId,
  onNavigate,
  onStartQuiz,
  onContentComplete
}) => {
  const [isCompleted, setIsCompleted] = useState(false);

  // Find current content
  const currentModule = course.modules.find(m => m.id === currentModuleId);
  const currentChapter = currentModule?.chapters.find(c => c.id === currentChapterId);
  const currentContent = currentChapter?.contents.find(c => c.id === currentContentId);

  // Find navigation info
  const moduleIndex = course.modules.findIndex(m => m.id === currentModuleId);
  const chapterIndex = currentModule?.chapters.findIndex(c => c.id === currentChapterId) || 0;
  const contentIndex = currentChapter?.contents.findIndex(c => c.id === currentContentId) || 0;

  // Navigation helpers
  const getNextContent = () => {
    if (!currentModule || !currentChapter || !currentContent) return null;
    
    const nextContentIndex = contentIndex + 1;
    if (nextContentIndex < currentChapter.contents.length) {
      return {
        moduleId: currentModuleId,
        chapterId: currentChapterId,
        contentId: currentChapter.contents[nextContentIndex].id
      };
    }
    
    // Check if there's a chapter quiz
    if (currentChapter.quiz && currentChapter.contents.every(c => c.isCompleted)) {
      return {
        moduleId: currentModuleId,
        chapterId: currentChapterId,
        contentId: 'chapter-quiz'
      };
    }
    
    // Check next chapter
    const nextChapterIndex = chapterIndex + 1;
    if (nextChapterIndex < currentModule.chapters.length) {
      const nextChapter = currentModule.chapters[nextChapterIndex];
      if (nextChapter.isUnlocked && nextChapter.contents.length > 0) {
        return {
          moduleId: currentModuleId,
          chapterId: nextChapter.id,
          contentId: nextChapter.contents[0].id
        };
      }
    }
    
    // Check module quiz
    if (currentModule.moduleQuiz && currentModule.chapters.every(ch => 
      ch.contents.every(c => c.isCompleted) && ch.quiz.isPassed
    )) {
      return {
        moduleId: currentModuleId,
        chapterId: 'module-quiz',
        contentId: 'module-quiz'
      };
    }
    
    return null;
  };

  const getPreviousContent = () => {
    if (!currentModule || !currentChapter || !currentContent) return null;
    
    const prevContentIndex = contentIndex - 1;
    if (prevContentIndex >= 0) {
      return {
        moduleId: currentModuleId,
        chapterId: currentChapterId,
        contentId: currentChapter.contents[prevContentIndex].id
      };
    }
    
    // Check previous chapter
    const prevChapterIndex = chapterIndex - 1;
    if (prevChapterIndex >= 0) {
      const prevChapter = currentModule.chapters[prevChapterIndex];
      if (prevChapter.contents.length > 0) {
        return {
          moduleId: currentModuleId,
          chapterId: prevChapter.id,
          contentId: prevChapter.contents[prevChapter.contents.length - 1].id
        };
      }
    }
    
    return null;
  };

  const nextContent = getNextContent();
  const prevContent = getPreviousContent();

  useEffect(() => {
    if (currentContent) {
      setIsCompleted(currentContent.isCompleted);
    }
  }, [currentContent]);

  const handleContentComplete = () => {
    if (currentContent && !currentContent.isCompleted) {
      onContentComplete(currentContent.id);
      setIsCompleted(true);
    }
  };

  const handleNext = () => {
    if (nextContent) {
      if (nextContent.contentId === 'chapter-quiz') {
        onStartQuiz(currentChapter?.quiz?.id || '', 'chapter');
      } else if (nextContent.contentId === 'module-quiz') {
        onStartQuiz(currentModule?.moduleQuiz?.id || '', 'module');
      } else {
        onNavigate(nextContent.moduleId, nextContent.chapterId, nextContent.contentId);
      }
    }
  };

  const handlePrevious = () => {
    if (prevContent) {
      onNavigate(prevContent.moduleId, prevContent.chapterId, prevContent.contentId);
    }
  };

  // Download markdown as PDF function
  const handleDownloadMarkdownAsPDF = () => {
    if (currentContent?.type !== 'text') return;
    
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${currentContent.title}</title>
          <style>
            @media print {
              @page {
                size: A4;
                margin: 2cm;
              }
              body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
                line-height: 1.6;
                color: #333;
              }
            }
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            h1 { color: #2d3748; font-size: 2em; margin-bottom: 1em; }
            h2 { color: #4a5568; font-size: 1.5em; margin: 1.5em 0 0.5em; }
            h3 { color: #4a5568; font-size: 1.25em; margin: 1.2em 0 0.5em; }
            h4 { color: #718096; font-size: 1.1em; margin: 1em 0 0.5em; }
            p { margin-bottom: 1em; }
            ul, ol { margin-bottom: 1em; padding-left: 2em; }
            li { margin-bottom: 0.25em; }
            blockquote {
              border-left: 4px solid #3182ce;
              background: #ebf8ff;
              padding: 1em;
              margin: 1em 0;
              font-style: italic;
            }
            code {
              background: #f7fafc;
              padding: 0.2em 0.4em;
              border-radius: 3px;
              font-family: 'Courier New', monospace;
              font-size: 0.9em;
            }
            pre {
              background: #2d3748;
              color: #f7fafc;
              padding: 1em;
              border-radius: 5px;
              overflow-x: auto;
              margin: 1em 0;
            }
            pre code {
              background: none;
              padding: 0;
              color: inherit;
            }
            table {
              border-collapse: collapse;
              width: 100%;
              margin: 1em 0;
            }
            th, td {
              border: 1px solid #e2e8f0;
              padding: 0.5em;
              text-align: left;
            }
            th {
              background: #f7fafc;
              font-weight: 600;
            }
            hr {
              border: none;
              height: 1px;
              background: #e2e8f0;
              margin: 2em 0;
            }
            strong { font-weight: 600; }
            em { font-style: italic; }
          </style>
        </head>
        <body>
          <h1>${currentContent.title}</h1>
          <div id="markdown-content"></div>
        </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();

    const markdownDiv = printWindow.document.getElementById('markdown-content');
    if (markdownDiv) {
      let htmlText = '';
      
      if (typeof currentContent.content === 'string') {
        htmlText = currentContent.content;
      } else if (
        typeof currentContent.content === 'object' &&
        currentContent.content !== null &&
        'value' in currentContent.content &&
        typeof currentContent.content.value === 'string'
      ) {
        htmlText = currentContent.content.value;
      } else if (Array.isArray(currentContent.content)) {
        htmlText = currentContent.content
          .map((block) => (block.type === 'text' ? block.value : ''))
          .join('');
      }
      
      // Simple markdown to HTML conversion
      htmlText = htmlText.replace(/^### (.*$)/gim, '<h3>$1</h3>');
      htmlText = htmlText.replace(/^## (.*$)/gim, '<h2>$1</h2>');
      htmlText = htmlText.replace(/^# (.*$)/gim, '<h1>$1</h1>');
      htmlText = htmlText.replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>');
      htmlText = htmlText.replace(/\*(.*)\*/gim, '<em>$1</em>');
      htmlText = htmlText.replace(/^\* (.*$)/gim, '<li>$1</li>');
      htmlText = htmlText.replace(/(<li>.*<\/li>)/gim, '<ul>$1</ul>');
      htmlText = htmlText.replace(/^\d+\. (.*$)/gim, '<li>$1</li>');
      htmlText = htmlText.replace(/\n\n/g, '</p><p>');
      htmlText = '<p>' + htmlText + '</p>';
      htmlText = htmlText.replace(/<p><\/p>/g, '');
      
      markdownDiv.innerHTML = htmlText;
    }

    setTimeout(() => {
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    }, 250);
  };

  if (!currentContent) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <BookMarked className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Konten tidak ditemukan</h3>
          <p className="text-gray-600">Silakan pilih konten dari navigasi di sebelah kiri.</p>
        </div>
      </div>
    );
  }

  const getContentIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <Play className="h-5 w-5 text-red-500" />;
      case 'pdf':
        return <FileText className="h-5 w-5 text-red-600" />;
      case 'zoom-recording':
        return <Play className="h-5 w-5 text-blue-500" />;
      case 'image':
        return <Image className="h-5 w-5 text-green-500" />;
      default:
        return <BookMarked className="h-5 w-5 text-blue-500" />;
    }
  };

  const getContentTypeLabel = (type: string) => {
    switch (type) {
      case 'video':
        return 'Video';
      case 'pdf':
        return 'PDF Document';
      case 'zoom-recording':
        return 'Zoom Recording';
      case 'image':
        return 'Image';
      default:
        return 'Reading Material';
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Content Header */}
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          {getContentIcon(currentContent.type)}
          <Badge variant="outline" className="text-xs">
            {getContentTypeLabel(currentContent.type)}
          </Badge>
          {currentContent.duration && (
            <div className="flex items-center text-sm text-gray-500 ml-2">
              <Clock className="h-4 w-4 mr-1" />
              {currentContent.duration}
            </div>
          )}
        </div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          {currentContent.title}
        </h1>
        <p className="text-gray-600">
          {currentModule?.title} • {currentChapter?.title}
        </p>
      </div>

      {/* Content Body */}
      <div className="bg-white rounded-lg border p-6 mb-6">
        {/* Text Content with Markdown */}
        {currentContent.type === 'text' && (
          <div className="space-y-4">
            <div className="prose prose-sm max-w-none text-gray-700">
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                  h1: ({ node, ...props }) => (
                    <h1 className="mb-4 text-2xl font-bold text-gray-900" {...props} />
                  ),
                  h2: ({ node, ...props }) => (
                    <h2 className="mb-3 text-xl font-semibold text-gray-800" {...props} />
                  ),
                  h3: ({ node, ...props }) => (
                    <h3 className="mb-2 text-lg font-semibold text-gray-800" {...props} />
                  ),
                  h4: ({ node, ...props }) => (
                    <h4 className="mb-2 text-base font-semibold text-gray-700" {...props} />
                  ),
                  p: ({ node, ...props }) => (
                    <p className="mb-3 leading-relaxed" {...props} />
                  ),
                  ul: ({ node, ...props }) => (
                    <ul className="mb-3 ml-4 list-disc" {...props} />
                  ),
                  ol: ({ node, ...props }) => (
                    <ol className="mb-3 ml-4 list-decimal" {...props} />
                  ),
                  li: ({ node, ...props }) => (
                    <li className="mb-1" {...props} />
                  ),
                  blockquote: ({ node, ...props }) => (
                    <blockquote className="mb-4 rounded-r border-l-4 border-blue-200 bg-blue-50 py-2 pl-4 italic" {...props} />
                  ),
                  code: ({ node, className, children, ...props }) => {
                    const match = /language-(\w+)/.exec(className || '');
                    const isInline = !match;
                    return isInline ? (
                      <code className="rounded bg-gray-100 px-1 py-0.5 font-mono text-sm" {...props}>
                        {children}
                      </code>
                    ) : (
                      <code className="block overflow-x-auto rounded bg-gray-900 p-4 font-mono text-sm text-gray-100" {...props}>
                        {children}
                      </code>
                    );
                  },
                  pre: ({ node, ...props }) => (
                    <pre className="mb-4" {...props} />
                  ),
                  table: ({ node, ...props }) => (
                    <div className="mb-4 overflow-x-auto">
                      <table className="min-w-full rounded border border-gray-200" {...props} />
                    </div>
                  ),
                  thead: ({ node, ...props }) => (
                    <thead className="bg-gray-50" {...props} />
                  ),
                  th: ({ node, ...props }) => (
                    <th className="border border-gray-200 px-3 py-2 text-left font-semibold" {...props} />
                  ),
                  td: ({ node, ...props }) => (
                    <td className="border border-gray-200 px-3 py-2" {...props} />
                  ),
                  hr: ({ node, ...props }) => (
                    <hr className="my-6 border-gray-300" {...props} />
                  ),
                  strong: ({ node, ...props }) => (
                    <strong className="font-semibold text-gray-900" {...props} />
                  ),
                  em: ({ node, ...props }) => (
                    <em className="italic" {...props} />
                  )
                }}
              >
                {typeof currentContent.content === 'string'
                  ? currentContent.content
                  : typeof currentContent.content === 'object' &&
                      currentContent.content !== null &&
                      'value' in currentContent.content &&
                      typeof currentContent.content.value === 'string'
                    ? currentContent.content.value
                    : Array.isArray(currentContent.content)
                      ? currentContent.content
                          .map((block) => (block.type === 'text' ? block.value : ''))
                          .join('')
                      : ''}
              </ReactMarkdown>
            </div>
            <Button
              size="sm"
              variant="outline"
              className="border-blue-200 text-blue-600 hover:bg-blue-50"
              onClick={handleDownloadMarkdownAsPDF}
            >
              <Download className="mr-2 h-4 w-4" />
              Download as PDF
            </Button>
          </div>
        )}

        {/* PDF Content */}
        {currentContent.type === 'pdf' && (
          <div className="space-y-4">
            <iframe
              src={`${typeof currentContent.content === 'string' ? currentContent.content : 
                (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : ''}#toolbar=0&navpanes=0`}
              className="w-full h-96 rounded border"
              title={currentContent.title}
            />
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="outline"
                className="border-blue-200 text-blue-600 hover:bg-blue-50"
                onClick={() => {
                  const pdfUrl = typeof currentContent.content === 'string' ? currentContent.content :
                    (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : '';
                  if (pdfUrl) window.open(pdfUrl, '_blank');
                }}
              >
                <Eye className="mr-2 h-4 w-4" />
                Open in New Tab
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="text-gray-600 hover:bg-gray-50"
                onClick={() => {
                  const pdfUrl = typeof currentContent.content === 'string' ? currentContent.content :
                    (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : '';
                  if (pdfUrl) {
                    const link = document.createElement('a');
                    link.href = pdfUrl;
                    link.download = currentContent.title || 'document.pdf';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }
                }}
              >
                <Download className="mr-2 h-4 w-4" />
                Download
              </Button>
            </div>
          </div>
        )}

        {/* Image Content */}
        {currentContent.type === 'image' && (
          <div className="space-y-4">
            <img
              src={typeof currentContent.content === 'string' ? currentContent.content :
                (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : ''}
              alt={currentContent.title || 'Image'}
              className="max-w-full h-auto rounded border"
            />
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="outline"
                className="border-blue-200 text-blue-600 hover:bg-blue-50"
                onClick={() => {
                  const imageUrl = typeof currentContent.content === 'string' ? currentContent.content :
                    (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : '';
                  if (imageUrl) window.open(imageUrl, '_blank');
                }}
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Open in New Tab
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="text-gray-600 hover:bg-gray-50"
                onClick={() => {
                  const imageUrl = typeof currentContent.content === 'string' ? currentContent.content :
                    (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : '';
                  if (imageUrl) {
                    const link = document.createElement('a');
                    link.href = imageUrl;
                    link.download = currentContent.title || 'image.jpg';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                  }
                }}
              >
                <Download className="mr-2 h-4 w-4" />
                Download
              </Button>
            </div>
          </div>
        )}

        {/* Video Content */}
        {(currentContent.type === 'video' || currentContent.type === 'zoom-recording') && (
          <div className="space-y-4">
            <div className="aspect-video w-full overflow-hidden rounded-lg bg-gray-100">
              {(() => {
                const videoUrl = typeof currentContent.content === 'string' ? currentContent.content :
                                 (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : '';

                if (!videoUrl) {
                  return (
                    <div className="flex h-full w-full items-center justify-center text-center text-gray-500">
                      No video URL provided.
                    </div>
                  );
                }

                if (videoUrl.includes('youtube.com/watch?v=') || videoUrl.includes('youtu.be/')) {
                  const youtubeId = videoUrl.split('v=')[1]?.split('&')[0] || videoUrl.split('/').pop();
                  return (
                    <iframe
                      className="h-full w-full"
                      src={`https://www.youtube.com/embed/${youtubeId}`}
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                      title={currentContent.title}
                    ></iframe>
                  );
                } else if (videoUrl.includes('vimeo.com/')) {
                  const vimeoId = videoUrl.split('/').pop();
                  return (
                    <iframe
                      className="h-full w-full"
                      src={`https://player.vimeo.com/video/${vimeoId}`}
                      frameBorder="0"
                      allow="autoplay; fullscreen; picture-in-picture"
                      allowFullScreen
                      title={currentContent.title}
                    ></iframe>
                  );
                } else {
                  return (
                    <video
                      controls
                      className="h-full w-full"
                      src={videoUrl}
                      title={currentContent.title}
                    >
                      Your browser does not support the video tag.
                    </video>
                  );
                }
              })()}
            </div>
            <div className="flex space-x-2">
              <Button
                size="sm"
                variant="outline"
                className="border-blue-200 text-blue-600 hover:bg-blue-50"
                onClick={() => {
                  const videoUrl = typeof currentContent.content === 'string' ? currentContent.content :
                                   (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : '';
                  if (videoUrl) window.open(videoUrl, '_blank');
                }}
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Open in New Tab
              </Button>
            </div>
          </div>
        )}

        {/* Unsupported content type */}
        {!['text', 'video', 'pdf', 'zoom-recording', 'image'].includes(currentContent.type) && (
          <div className="space-y-4">
            <div className="flex aspect-video items-center justify-center rounded-lg bg-gray-100">
              <div className="text-center">
                <Play className="mx-auto mb-2 h-12 w-12 text-gray-400" />
                <p className="text-sm text-gray-500">
                  Unsupported Media Type or Invalid Content.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Content description */}
        {currentContent.description && (
          <div className="mt-4 pt-4 border-t">
            <div className="prose max-w-none">
              <p className="text-gray-700">{currentContent.description}</p>
            </div>
          </div>
        )}
      </div>

      {/* Content Actions */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          {!isCompleted ? (
            <Button 
              onClick={handleContentComplete}
              className="flex items-center space-x-2 bg-green-600 hover:bg-green-700"
            >
              <CheckCircle className="h-4 w-4" />
              <span>Tandai Selesai</span>
            </Button>
          ) : (
            <div className="flex items-center text-green-600">
              <CheckCircle className="h-4 w-4 mr-2" />
              <span className="text-sm font-medium">Selesai</span>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={!prevContent}
            className="flex items-center space-x-2"
          >
            <ChevronLeft className="h-4 w-4" />
            <span>Sebelumnya</span>
          </Button>
          
          <Button
            onClick={handleNext}
            disabled={!nextContent}
            className="flex items-center space-x-2"
          >
            <span>Selanjutnya</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="mt-6">
        <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
          <span>Kemajuan Bab</span>
          <span>{Math.round((contentIndex + 1) / (currentChapter?.contents.length || 1) * 100)}%</span>
        </div>
        <Progress 
          value={(contentIndex + 1) / (currentChapter?.contents.length || 1) * 100} 
          className="h-2" 
        />
      </div>
    </div>
  );
};