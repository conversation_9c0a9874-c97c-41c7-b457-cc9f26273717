{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|static|.*\\..*|_static|_vercel).*){(\\\\.json)}?", "originalSource": "/((?!_next|static|.*\\..*|_static|_vercel).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oQU80BEb9Ysp0y5Cc6KvygFOTlbLolBab1Eeg0Ic8No=", "__NEXT_PREVIEW_MODE_ID": "b2756d040bfe3443965966359d8ec364", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "17143a5a882672d3ee9091165bead3e75e27942172d907984cbb53d81104e322", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "715bbc8ea837a96b8f5cd74148d03055c93e86c50b6cc95953e31c23f03c49ef"}}}, "instrumentation": null, "functions": {}}