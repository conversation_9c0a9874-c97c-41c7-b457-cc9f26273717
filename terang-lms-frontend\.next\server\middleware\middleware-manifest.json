{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|static|.*\\..*|_static|_vercel).*){(\\\\.json)}?", "originalSource": "/((?!_next|static|.*\\..*|_static|_vercel).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oQU80BEb9Ysp0y5Cc6KvygFOTlbLolBab1Eeg0Ic8No=", "__NEXT_PREVIEW_MODE_ID": "b516bec791f3a6e7216ffcdc46c7ccd7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a58a50beffe60c1ffd5832f62063a77b92eb37adc05171fc8d248b8fa88fbaf2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3927c1ac91658aee84a4b9898c90af78858e457f7b0a6445954f34724872a513"}}}, "instrumentation": null, "functions": {}}