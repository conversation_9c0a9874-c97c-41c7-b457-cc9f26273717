{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport function formatBytes(\r\n  bytes: number,\r\n  opts: {\r\n    decimals?: number;\r\n    sizeType?: 'accurate' | 'normal';\r\n  } = {}\r\n) {\r\n  const { decimals = 0, sizeType = 'normal' } = opts;\r\n\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const accurateSizes = ['Bytes', 'KiB', 'MiB', 'GiB', 'TiB'];\r\n  if (bytes === 0) return '0 Byte';\r\n  const i = Math.floor(Math.log(bytes) / Math.log(1024));\r\n  return `${(bytes / Math.pow(1024, i)).toFixed(decimals)} ${\r\n    sizeType === 'accurate'\r\n      ? (accurateSizes[i] ?? 'Bytest')\r\n      : (sizes[i] ?? 'Bytes')\r\n  }`;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YACd,KAAa,EACb,OAGI,CAAC,CAAC;IAEN,MAAM,EAAE,WAAW,CAAC,EAAE,WAAW,QAAQ,EAAE,GAAG;IAE9C,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,gBAAgB;QAAC;QAAS;QAAO;QAAO;QAAO;KAAM;IAC3D,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,UAAU,CAAC,EACvD,aAAa,aACR,aAAa,CAAC,EAAE,IAAI,WACpB,KAAK,CAAC,EAAE,IAAI,SACjB;AACJ", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary:\r\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost:\r\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n        iai: 'bg-[var(--iai-primary)] text-white shadow-xs hover:bg-[var(--iai-secondary)] focus-visible:ring-[var(--iai-primary)]/20',\r\n        'iai-outline': 'border border-[var(--iai-primary)] text-[var(--iai-primary)] bg-background shadow-xs hover:bg-[var(--iai-primary)] hover:text-white'\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='button'\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;YACN,KAAK;YACL,eAAe;QACjB;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Progress = React.forwardRef<\r\n  React.ElementRef<typeof ProgressPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\r\n>(({ className, value, ...props }, ref) => (\r\n  <ProgressPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ProgressPrimitive.Indicator\r\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\r\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n    />\r\n  </ProgressPrimitive.Root>\r\n))\r\nProgress.displayName = ProgressPrimitive.Root.displayName\r\n\r\nexport { Progress }"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst badgeVariants = cva(\r\n  'inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\r\n        secondary:\r\n          'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\r\n        destructive:\r\n          'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'span'> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : 'span';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='badge'\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Tabs = TabsPrimitive.Root\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsList.displayName = TabsPrimitive.List.displayName\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm cursor-pointer\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nTabsContent.displayName = TabsPrimitive.Content.displayName\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sZACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/tooltip.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as TooltipPrimitive from '@radix-ui/react-tooltip';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot='tooltip-provider'\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot='tooltip' {...props} />\r\n    </TooltipProvider>\r\n  );\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot='tooltip-trigger' {...props} />;\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot='tooltip-content'\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className='bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]' />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  );\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,6LAAC,sKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,6LAAC;kBACC,cAAA,6LAAC,sKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,sKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/tree-node.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { ChevronDown, ChevronRight, Lock, CheckCircle2 } from 'lucide-react';\r\nimport { TreeNodeProps } from '@/types/lms';\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from '@/components/ui/tooltip';\r\n\r\nexport const TreeNode: React.FC<TreeNodeProps> = ({\r\n  title,\r\n  icon,\r\n  isUnlocked,\r\n  isCompleted = false,\r\n  children,\r\n  level,\r\n  isExpanded = false,\r\n  onToggle,\r\n  onClick,\r\n  hasChildren = false,\r\n  isActive = false\r\n}) => {\r\n  // Check if text might be truncated (rough estimate)\r\n  const mightBeTruncated = title.length > 20;\r\n\r\n  return (\r\n    <div className='select-none'>\r\n      <TooltipProvider>\r\n        <Tooltip delayDuration={500}>\r\n          <TooltipTrigger asChild>\r\n            <button\r\n              onClick={() => {\r\n                if (hasChildren && onToggle) {\r\n                  onToggle();\r\n                } else if (onClick) {\r\n                  onClick();\r\n                }\r\n              }}\r\n              className={`flex w-full items-center space-x-3 rounded-lg px-4 py-3 text-left text-base transition-colors ${\r\n                isUnlocked\r\n                  ? isActive\r\n                    ? 'bg-blue-100 text-blue-800 border-2 border-blue-300 hover:bg-blue-150'\r\n                    : 'text-blue-700 hover:bg-blue-50'\r\n                  : 'cursor-not-allowed text-gray-400'\r\n              }`}\r\n              style={{ paddingLeft: `${level * 20 + 16}px` }}\r\n              disabled={!isUnlocked}\r\n            >\r\n              {hasChildren && (\r\n                <span className='flex h-5 w-5 items-center justify-center'>\r\n                  {isExpanded ? (\r\n                    <ChevronDown className='h-4 w-4' />\r\n                  ) : (\r\n                    <ChevronRight className='h-4 w-4' />\r\n                  )}\r\n                </span>\r\n              )}\r\n              <span className='flex h-5 w-5 items-center justify-center'>{icon}</span>\r\n              <span className={`flex-1 font-medium ${title.length > 35 ? 'text-sm leading-tight' : 'truncate'}`}>\r\n                {title.length > 35 ? (\r\n                  <span className=\"block\">{title}</span>\r\n                ) : (\r\n                  title\r\n                )}\r\n              </span>\r\n              {!isUnlocked && <Lock className='h-4 w-4' />}\r\n              {isCompleted && <CheckCircle2 className='h-4 w-4 text-green-500' />}\r\n            </button>\r\n          </TooltipTrigger>\r\n          {mightBeTruncated && (\r\n            <TooltipContent side=\"right\" className=\"max-w-xs\">\r\n              <p>{title}</p>\r\n            </TooltipContent>\r\n          )}\r\n        </Tooltip>\r\n      </TooltipProvider>\r\n      {isExpanded && children && <div>{children}</div>}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAEA;;;;AAOO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,IAAI,EACJ,UAAU,EACV,cAAc,KAAK,EACnB,QAAQ,EACR,KAAK,EACL,aAAa,KAAK,EAClB,QAAQ,EACR,OAAO,EACP,cAAc,KAAK,EACnB,WAAW,KAAK,EACjB;IACC,oDAAoD;IACpD,MAAM,mBAAmB,MAAM,MAAM,GAAG;IAExC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,sIAAA,CAAA,kBAAe;0BACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;oBAAC,eAAe;;sCACtB,6LAAC,sIAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,6LAAC;gCACC,SAAS;oCACP,IAAI,eAAe,UAAU;wCAC3B;oCACF,OAAO,IAAI,SAAS;wCAClB;oCACF;gCACF;gCACA,WAAW,CAAC,8FAA8F,EACxG,aACI,WACE,yEACA,mCACF,oCACJ;gCACF,OAAO;oCAAE,aAAa,GAAG,QAAQ,KAAK,GAAG,EAAE,CAAC;gCAAC;gCAC7C,UAAU,CAAC;;oCAEV,6BACC,6LAAC;wCAAK,WAAU;kDACb,2BACC,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;iEAEvB,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAI9B,6LAAC;wCAAK,WAAU;kDAA4C;;;;;;kDAC5D,6LAAC;wCAAK,WAAW,CAAC,mBAAmB,EAAE,MAAM,MAAM,GAAG,KAAK,0BAA0B,YAAY;kDAC9F,MAAM,MAAM,GAAG,mBACd,6LAAC;4CAAK,WAAU;sDAAS;;;;;mDAEzB;;;;;;oCAGH,CAAC,4BAAc,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAC/B,6BAAe,6LAAC,wNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAG3C,kCACC,6LAAC,sIAAA,CAAA,iBAAc;4BAAC,MAAK;4BAAQ,WAAU;sCACrC,cAAA,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;YAKX,cAAc,0BAAY,6LAAC;0BAAK;;;;;;;;;;;;AAGvC;KAtEa", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/table-of-contents.tsx"], "sourcesContent": ["import React from 'react';\r\nimport {\r\n  BookOpen,\r\n  Book,\r\n  List,\r\n  Play,\r\n  FileText,\r\n  BookMarked,\r\n  Target,\r\n  Trophy\r\n} from 'lucide-react';\r\nimport { TableOfContentsProps } from '@/types/lms';\r\nimport { TreeNode } from './tree-node';\r\n\r\nexport const TableOfContents: React.FC<TableOfContentsProps> = ({\r\n  course,\r\n  onNavigate,\r\n  expandedModules,\r\n  expandedChapters,\r\n  onToggleModule,\r\n  onToggleChapter,\r\n  currentModuleIndex\r\n}) => {\r\n  return (\r\n    <div className='w-full'>\r\n      <div className='mb-4'>\r\n        <h3 className='flex items-center text-lg font-semibold text-gray-900'>\r\n          <List className='mr-3 h-5 w-5' />\r\n          Table of Contents\r\n        </h3>\r\n      </div>\r\n      <div className='max-h-[calc(100vh-16rem)] space-y-1 overflow-y-auto overflow-x-hidden pr-2'>\r\n        {course.modules.map((module, index) => {\r\n          const moduleCompleted =\r\n            module.chapters.every(\r\n              (ch) =>\r\n                ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed\r\n            ) && module.moduleQuiz.isPassed;\r\n\r\n          const isCurrentModule = currentModuleIndex === index;\r\n\r\n          return (\r\n            <TreeNode\r\n              key={module.id}\r\n              id={module.id}\r\n              title={module.title}\r\n              icon={<BookOpen className='h-5 w-5' />}\r\n              isUnlocked={module.isUnlocked}\r\n              isCompleted={moduleCompleted}\r\n              level={0}\r\n              hasChildren={true}\r\n              isExpanded={expandedModules[module.id]}\r\n              onToggle={() => onToggleModule(module.id)}\r\n              isActive={isCurrentModule}\r\n            >\r\n              {module.chapters.map((chapter) => {\r\n                const chapterCompleted =\r\n                  chapter.contents.every((c) => c.isCompleted) &&\r\n                  chapter.quiz.isPassed;\r\n\r\n                return (\r\n                  <TreeNode\r\n                    key={chapter.id}\r\n                    id={chapter.id}\r\n                    title={chapter.title}\r\n                    icon={<Book className='h-4 w-4' />}\r\n                    isUnlocked={chapter.isUnlocked}\r\n                    isCompleted={chapterCompleted}\r\n                    level={1}\r\n                    hasChildren={true}\r\n                    isExpanded={expandedChapters[chapter.id]}\r\n                    onToggle={() => onToggleChapter(chapter.id)}\r\n                  >\r\n                    {chapter.contents.map((content) => (\r\n                      <TreeNode\r\n                        key={content.id}\r\n                        id={content.id}\r\n                        title={content.title}\r\n                        icon={\r\n                          content.type === 'video' ? (\r\n                            <Play className='h-4 w-4 text-red-500' />\r\n                          ) : content.type === 'pdf' ? (\r\n                            <FileText className='h-4 w-4 text-red-600' />\r\n                          ) : content.type === 'zoom-recording' ? (\r\n                            <Play className='h-4 w-4 text-blue-500' />\r\n                          ) : (\r\n                            <BookMarked className='h-4 w-4 text-blue-500' />\r\n                          )\r\n                        }\r\n                        isUnlocked={chapter.isUnlocked}\r\n                        isCompleted={content.isCompleted}\r\n                        level={2}\r\n                        onClick={() => onNavigate(module.id, chapter.id, content.id)}\r\n                      />\r\n                    ))}\r\n                    <TreeNode\r\n                      key={`${chapter.id}-quiz`}\r\n                      id={`${chapter.id}-quiz`}\r\n                      title='Chapter Quiz'\r\n                      icon={<Target className='h-4 w-4' />}\r\n                      isUnlocked={chapter.contents.every((c) => c.isCompleted)}\r\n                      isCompleted={chapter.quiz.isPassed}\r\n                      level={2}\r\n                      onClick={() => onNavigate(module.id, chapter.id)}\r\n                    />\r\n                  </TreeNode>\r\n                );\r\n              })}\r\n              <TreeNode\r\n                key={`${module.id}-quiz`}\r\n                id={`${module.id}-quiz`}\r\n                title='Module Quiz'\r\n                icon={<BookMarked className='h-4 w-4' />}\r\n                isUnlocked={module.chapters.every(\r\n                  (ch) =>\r\n                    ch.contents.every((c) => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)\r\n                )}\r\n                isCompleted={module.moduleQuiz.isPassed}\r\n                level={1}\r\n                onClick={() => onNavigate(module.id)}\r\n              />\r\n            </TreeNode>\r\n          );\r\n        })}\r\n\r\n        <TreeNode\r\n          key='final-exam'\r\n          id='final-exam'\r\n          title='Final Exam'\r\n          icon={<Trophy className='h-5 w-5' />}\r\n          isUnlocked={course.modules.every(\r\n            (m) =>\r\n              m.chapters.every(\r\n                (ch) =>\r\n                  ch.contents.every((c) => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)\r\n              ) && (m.moduleQuiz.isPassed || m.moduleQuiz.attempts >= m.moduleQuiz.maxAttempts)\r\n          )}\r\n          isCompleted={course.finalExam.isPassed}\r\n          level={0}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;;AAEO,MAAM,kBAAkD,CAAC,EAC9D,MAAM,EACN,UAAU,EACV,eAAe,EACf,gBAAgB,EAChB,cAAc,EACd,eAAe,EACf,kBAAkB,EACnB;IACC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;;sCACZ,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;0BAIrC,6LAAC;gBAAI,WAAU;;oBACZ,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;wBAC3B,MAAM,kBACJ,OAAO,QAAQ,CAAC,KAAK,CACnB,CAAC,KACC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAM,EAAE,WAAW,KAAK,GAAG,IAAI,CAAC,QAAQ,KAC1D,OAAO,UAAU,CAAC,QAAQ;wBAEjC,MAAM,kBAAkB,uBAAuB;wBAE/C,qBACE,6LAAC,4IAAA,CAAA,WAAQ;4BAEP,IAAI,OAAO,EAAE;4BACb,OAAO,OAAO,KAAK;4BACnB,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAC1B,YAAY,OAAO,UAAU;4BAC7B,aAAa;4BACb,OAAO;4BACP,aAAa;4BACb,YAAY,eAAe,CAAC,OAAO,EAAE,CAAC;4BACtC,UAAU,IAAM,eAAe,OAAO,EAAE;4BACxC,UAAU;;gCAET,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;oCACpB,MAAM,mBACJ,QAAQ,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAM,EAAE,WAAW,KAC3C,QAAQ,IAAI,CAAC,QAAQ;oCAEvB,qBACE,6LAAC,4IAAA,CAAA,WAAQ;wCAEP,IAAI,QAAQ,EAAE;wCACd,OAAO,QAAQ,KAAK;wCACpB,oBAAM,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACtB,YAAY,QAAQ,UAAU;wCAC9B,aAAa;wCACb,OAAO;wCACP,aAAa;wCACb,YAAY,gBAAgB,CAAC,QAAQ,EAAE,CAAC;wCACxC,UAAU,IAAM,gBAAgB,QAAQ,EAAE;;4CAEzC,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,6LAAC,4IAAA,CAAA,WAAQ;oDAEP,IAAI,QAAQ,EAAE;oDACd,OAAO,QAAQ,KAAK;oDACpB,MACE,QAAQ,IAAI,KAAK,wBACf,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;iEACd,QAAQ,IAAI,KAAK,sBACnB,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;iEAClB,QAAQ,IAAI,KAAK,iCACnB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;+EAEhB,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAG1B,YAAY,QAAQ,UAAU;oDAC9B,aAAa,QAAQ,WAAW;oDAChC,OAAO;oDACP,SAAS,IAAM,WAAW,OAAO,EAAE,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE;mDAjBtD,QAAQ,EAAE;;;;;0DAoBnB,6LAAC,4IAAA,CAAA,WAAQ;gDAEP,IAAI,GAAG,QAAQ,EAAE,CAAC,KAAK,CAAC;gDACxB,OAAM;gDACN,oBAAM,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDACxB,YAAY,QAAQ,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAM,EAAE,WAAW;gDACvD,aAAa,QAAQ,IAAI,CAAC,QAAQ;gDAClC,OAAO;gDACP,SAAS,IAAM,WAAW,OAAO,EAAE,EAAE,QAAQ,EAAE;+CAP1C,GAAG,QAAQ,EAAE,CAAC,KAAK,CAAC;;;;;;uCAlCtB,QAAQ,EAAE;;;;;gCA6CrB;8CACA,6LAAC,4IAAA,CAAA,WAAQ;oCAEP,IAAI,GAAG,OAAO,EAAE,CAAC,KAAK,CAAC;oCACvB,OAAM;oCACN,oBAAM,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAC5B,YAAY,OAAO,QAAQ,CAAC,KAAK,CAC/B,CAAC,KACC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAM,EAAE,WAAW,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,GAAG,IAAI,CAAC,WAAW;oCAE3G,aAAa,OAAO,UAAU,CAAC,QAAQ;oCACvC,OAAO;oCACP,SAAS,IAAM,WAAW,OAAO,EAAE;mCAV9B,GAAG,OAAO,EAAE,CAAC,KAAK,CAAC;;;;;;2BAlErB,OAAO,EAAE;;;;;oBAgFpB;kCAEA,6LAAC,4IAAA,CAAA,WAAQ;wBAEP,IAAG;wBACH,OAAM;wBACN,oBAAM,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACxB,YAAY,OAAO,OAAO,CAAC,KAAK,CAC9B,CAAC,IACC,EAAE,QAAQ,CAAC,KAAK,CACd,CAAC,KACC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAM,EAAE,WAAW,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,GAAG,IAAI,CAAC,WAAW,MACtG,CAAC,EAAE,UAAU,CAAC,QAAQ,IAAI,EAAE,UAAU,CAAC,QAAQ,IAAI,EAAE,UAAU,CAAC,WAAW;wBAEpF,aAAa,OAAO,SAAS,CAAC,QAAQ;wBACtC,OAAO;uBAZH;;;;;;;;;;;;;;;;;AAiBd;KAjIa", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card'\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-header'\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-title'\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-action'\r\n      className={cn(\r\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-content'\r\n      className={cn('px-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-footer'\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/content-item.tsx"], "sourcesContent": ["//content-item.tsx\r\nimport React from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport remarkGfm from 'remark-gfm';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  Play,\r\n  FileText,\r\n  BookMarked,\r\n  CheckCircle2,\r\n  ChevronUp,\r\n  ChevronDown,\r\n  Timer,\r\n  Download,\r\n  Eye,\r\n  ExternalLink,\r\n  Image\r\n} from 'lucide-react';\r\nimport { ContentItemProps } from '@/types/lms';\r\n\r\nexport const ContentItem: React.FC<ContentItemProps> = ({\r\n  content,\r\n  onToggleComplete,\r\n  isExpanded,\r\n  onToggleExpand\r\n}) => {\r\n  // Console log for debugging content object\r\n  console.log('ContentItem received content:', content);\r\n\r\n  const extractFirstMarkdownHeader = (markdown: string): string | null => {\r\n    const lines = markdown.split('\\n');\r\n    for (const line of lines) {\r\n      const match = line.match(/^#{1,4}\\s+(.*)$/);\r\n      if (match && match[1]) {\r\n        return match[1].trim();\r\n      }\r\n    }\r\n    return null;\r\n  };\r\n\r\n  const displayTitle =\r\n    content.title ||\r\n    (typeof content.content === 'object' &&\r\n    content.content !== null &&\r\n    'value' in content.content &&\r\n    typeof content.content.value === 'string'\r\n      ? extractFirstMarkdownHeader(content.content.value)\r\n      : null) ||\r\n    'No Title';\r\n\r\n  const handleDownloadMarkdownAsPDF = () => {\r\n    // Create a new window for printing\r\n    const printWindow = window.open('', '_blank');\r\n    if (!printWindow) return;\r\n\r\n    // Create HTML content for the markdown\r\n    const htmlContent = `\r\n      <!DOCTYPE html>\r\n      <html>\r\n        <head>\r\n          <title>${content.title}</title>\r\n          <style>\r\n            @media print {\r\n              @page {\r\n                size: A4;\r\n                margin: 2cm;\r\n              }\r\n              body {\r\n                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\r\n                line-height: 1.6;\r\n                color: #333;\r\n              }\r\n            }\r\n            body {\r\n              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\r\n              line-height: 1.6;\r\n              color: #333;\r\n              max-width: 800px;\r\n              margin: 0 auto;\r\n              padding: 20px;\r\n            }\r\n            h1 { color: #2d3748; font-size: 2em; margin-bottom: 1em; }\r\n            h2 { color: #4a5568; font-size: 1.5em; margin: 1.5em 0 0.5em; }\r\n            h3 { color: #4a5568; font-size: 1.25em; margin: 1.2em 0 0.5em; }\r\n            h4 { color: #718096; font-size: 1.1em; margin: 1em 0 0.5em; }\r\n            p { margin-bottom: 1em; }\r\n            ul, ol { margin-bottom: 1em; padding-left: 2em; }\r\n            li { margin-bottom: 0.25em; }\r\n            blockquote {\r\n              border-left: 4px solid #3182ce;\r\n              background: #ebf8ff;\r\n              padding: 1em;\r\n              margin: 1em 0;\r\n              font-style: italic;\r\n            }\r\n            code {\r\n              background: #f7fafc;\r\n              padding: 0.2em 0.4em;\r\n              border-radius: 3px;\r\n              font-family: 'Courier New', monospace;\r\n              font-size: 0.9em;\r\n            }\r\n            pre {\r\n              background: #2d3748;\r\n              color: #f7fafc;\r\n              padding: 1em;\r\n              border-radius: 5px;\r\n              overflow-x: auto;\r\n              margin: 1em 0;\r\n            }\r\n            pre code {\r\n              background: none;\r\n              padding: 0;\r\n              color: inherit;\r\n            }\r\n            table {\r\n              border-collapse: collapse;\r\n              width: 100%;\r\n              margin: 1em 0;\r\n            }\r\n            th, td {\r\n              border: 1px solid #e2e8f0;\r\n              padding: 0.5em;\r\n              text-align: left;\r\n            }\r\n            th {\r\n              background: #f7fafc;\r\n              font-weight: 600;\r\n            }\r\n            hr {\r\n              border: none;\r\n              height: 1px;\r\n              background: #e2e8f0;\r\n              margin: 2em 0;\r\n            }\r\n            strong { font-weight: 600; }\r\n            em { font-style: italic; }\r\n          </style>\r\n        </head>\r\n        <body>\r\n          <h1>${content.title}</h1>\r\n          <div id=\"markdown-content\"></div>\r\n        </body>\r\n      </html>\r\n    `;\r\n\r\n    printWindow.document.write(htmlContent);\r\n    printWindow.document.close();\r\n\r\n    // Convert markdown to HTML and insert it\r\n    const markdownDiv = printWindow.document.getElementById('markdown-content');\r\n    if (markdownDiv) {\r\n      // Simple markdown to HTML conversion for basic formatting\r\n      let htmlText = '';\r\n      \r\n      // Check if content.content is a string, ContentBlock object, or ContentBlock array\r\n      if (typeof content.content === 'string') {\r\n        htmlText = content.content;\r\n      } else if (\r\n        typeof content.content === 'object' &&\r\n        content.content !== null &&\r\n        'value' in content.content &&\r\n        typeof content.content.value === 'string'\r\n      ) {\r\n        htmlText = content.content.value;\r\n      } else if (Array.isArray(content.content)) {\r\n        // Convert ContentBlock array to string\r\n        htmlText = content.content\r\n          .map((block) => (block.type === 'text' ? block.value : ''))\r\n          .join('');\r\n      }\r\n      \r\n      // Headers\r\n      htmlText = htmlText.replace(/^### (.*$)/gim, '<h3>$1</h3>');\r\n      htmlText = htmlText.replace(/^## (.*$)/gim, '<h2>$1</h2>');\r\n      htmlText = htmlText.replace(/^# (.*$)/gim, '<h1>$1</h1>');\r\n      \r\n      // Bold and italic\r\n      htmlText = htmlText.replace(/\\*\\*(.*)\\*\\*/gim, '<strong>$1</strong>');\r\n      htmlText = htmlText.replace(/\\*(.*)\\*/gim, '<em>$1</em>');\r\n      \r\n      // Lists\r\n      htmlText = htmlText.replace(/^\\* (.*$)/gim, '<li>$1</li>');\r\n      htmlText = htmlText.replace(/(<li>.*<\\/li>)/gim, '<ul>$1</ul>');\r\n      htmlText = htmlText.replace(/^\\d+\\. (.*$)/gim, '<li>$1</li>');\r\n      \r\n      // Line breaks to paragraphs\r\n      htmlText = htmlText.replace(/\\n\\n/g, '</p><p>');\r\n      htmlText = '<p>' + htmlText + '</p>';\r\n      \r\n      // Clean up empty paragraphs\r\n      htmlText = htmlText.replace(/<p><\\/p>/g, '');\r\n      \r\n      markdownDiv.innerHTML = htmlText;\r\n    }\r\n\r\n    // Wait for content to load then print\r\n    setTimeout(() => {\r\n      printWindow.focus();\r\n      printWindow.print();\r\n      printWindow.close();\r\n    }, 250);\r\n  };\r\n  const getContentIcon = () => {\r\n    switch (content.type) {\r\n      case 'video':\r\n        return <Play className='h-4 w-4 text-red-500' />;\r\n      case 'pdf':\r\n        return <FileText className='h-4 w-4 text-red-600' />;\r\n      case 'zoom-recording':\r\n        return <Play className='h-4 w-4 text-blue-500' />;\r\n      case 'image':\r\n        return <Image className='h-4 w-4 text-green-500' />;\r\n      default:\r\n        return <BookMarked className='h-4 w-4 text-blue-500' />;\r\n    }\r\n  };\r\n\r\n  const getContentTypeLabel = () => {\r\n    switch (content.type) {\r\n      case 'video':\r\n        return 'Video';\r\n      case 'pdf':\r\n        return 'PDF Document';\r\n      case 'zoom-recording':\r\n        return 'Zoom Recording';\r\n      case 'image':\r\n        return 'Image';\r\n      default:\r\n        return 'Reading Material';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card id={`content-${content.id}`} className='my-2 ml-6 border-l-4 border-l-blue-200 scroll-mt-20'>\r\n      <CardContent className='py-3'>\r\n        <div className='flex flex-col'>\r\n          <div\r\n            className='flex cursor-pointer items-center justify-between'\r\n            onClick={onToggleExpand}\r\n          >\r\n            <div className='flex flex-1 items-center space-x-3'>\r\n              {getContentIcon()}\r\n              <div className='flex-1'>\r\n                <span className='text-sm font-medium'>{displayTitle}</span>\r\n                <div className='mt-1 flex items-center space-x-2'>\r\n                  <Badge variant='outline' className='text-xs'>\r\n                    {getContentTypeLabel()}\r\n                  </Badge>\r\n                  {content.duration && (\r\n                    <Badge variant='outline' className='text-xs'>\r\n                      <Timer className='mr-1 h-3 w-3' />\r\n                      {content.duration} min\r\n                    </Badge>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div className='flex items-center space-x-2'>\r\n              {isExpanded ? (\r\n                <ChevronUp className='h-4 w-4 text-gray-400' />\r\n              ) : (\r\n                <ChevronDown className='h-4 w-4 text-gray-400' />\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {isExpanded && (\r\n            <div className='mt-4 border-t pt-4 pl-7'>\r\n              {content.type === 'text' ? (\r\n                <div className='space-y-4'>\r\n                  <div className='prose prose-sm max-w-none text-gray-700'>\r\n                    <ReactMarkdown\r\n                      remarkPlugins={[remarkGfm]}\r\n                      components={{\r\n                        h1: ({ node, ...props }) => (\r\n                          <h1\r\n                            className='mb-4 text-2xl font-bold text-gray-900'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        h2: ({ node, ...props }) => (\r\n                          <h2\r\n                            className='mb-3 text-xl font-semibold text-gray-800'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        h3: ({ node, ...props }) => (\r\n                          <h3\r\n                            className='mb-2 text-lg font-semibold text-gray-800'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        h4: ({ node, ...props }) => (\r\n                          <h4\r\n                            className='mb-2 text-base font-semibold text-gray-700'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        p: ({ node, ...props }) => (\r\n                          <p className='mb-3 leading-relaxed' {...props} />\r\n                        ),\r\n                        ul: ({ node, ...props }) => (\r\n                          <ul className='mb-3 ml-4 list-disc' {...props} />\r\n                        ),\r\n                        ol: ({ node, ...props }) => (\r\n                          <ol className='mb-3 ml-4 list-decimal' {...props} />\r\n                        ),\r\n                        li: ({ node, ...props }) => (\r\n                          <li className='mb-1' {...props} />\r\n                        ),\r\n                        blockquote: ({ node, ...props }) => (\r\n                          <blockquote\r\n                            className='mb-4 rounded-r border-l-4 border-blue-200 bg-blue-50 py-2 pl-4 italic'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        code: ({ node, className, children, ...props }) => {\r\n                          const match = /language-(\\w+)/.exec(className || '');\r\n                          const isInline = !match;\r\n                          return isInline ? (\r\n                            <code\r\n                              className='rounded bg-gray-100 px-1 py-0.5 font-mono text-sm'\r\n                              {...props}\r\n                            >\r\n                              {children}\r\n                            </code>\r\n                          ) : (\r\n                            <code\r\n                              className='block overflow-x-auto rounded bg-gray-900 p-4 font-mono text-sm text-gray-100'\r\n                              {...props}\r\n                            >\r\n                              {children}\r\n                            </code>\r\n                          );\r\n                        },\r\n                        pre: ({ node, ...props }) => (\r\n                          <pre className='mb-4' {...props} />\r\n                        ),\r\n                        table: ({ node, ...props }) => (\r\n                          <div className='mb-4 overflow-x-auto'>\r\n                            <table\r\n                              className='min-w-full rounded border border-gray-200'\r\n                              {...props}\r\n                            />\r\n                          </div>\r\n                        ),\r\n                        thead: ({ node, ...props }) => (\r\n                          <thead className='bg-gray-50' {...props} />\r\n                        ),\r\n                        th: ({ node, ...props }) => (\r\n                          <th\r\n                            className='border border-gray-200 px-3 py-2 text-left font-semibold'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        td: ({ node, ...props }) => (\r\n                          <td\r\n                            className='border border-gray-200 px-3 py-2'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        hr: ({ node, ...props }) => (\r\n                          <hr className='my-6 border-gray-300' {...props} />\r\n                        ),\r\n                        strong: ({ node, ...props }) => (\r\n                          <strong\r\n                            className='font-semibold text-gray-900'\r\n                            {...props}\r\n                          />\r\n                        ),\r\n                        em: ({ node, ...props }) => (\r\n                          <em className='italic' {...props} />\r\n                        )\r\n                      }}\r\n                    >\r\n                      {typeof content.content === 'string'\r\n                        ? content.content\r\n                        : typeof content.content === 'object' &&\r\n                            content.content !== null &&\r\n                            'value' in content.content &&\r\n                            typeof content.content.value === 'string'\r\n                          ? content.content.value\r\n                          : Array.isArray(content.content)\r\n                            ? content.content\r\n                                .map((block) =>\r\n                                  block.type === 'text' ? block.value : ''\r\n                                )\r\n                                .join('')\r\n                            : ''}\r\n                    </ReactMarkdown>\r\n                  </div>\r\n                  <Button\r\n                    size='sm'\r\n                    variant='outline'\r\n                    className='border-blue-200 text-blue-600 hover:bg-blue-50'\r\n                    onClick={handleDownloadMarkdownAsPDF}\r\n                  >\r\n                    <Download className='mr-2 h-4 w-4' />\r\n                    Download as PDF\r\n                  </Button>\r\n                </div>\r\n              ) : content.type === 'pdf' ? (\r\n                <div className='space-y-4'>\r\n                  <iframe\r\n                    src={`${typeof content.content === 'string' ? content.content : (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') ? content.content.value : ''}#toolbar=0&navpanes=0`}\r\n                    className='w-full h-96 rounded border'\r\n                    title={content.title}\r\n                  />\r\n                  <div className='flex space-x-2'>\r\n                    <Button\r\n                      size='sm'\r\n                      variant='outline'\r\n                      className='border-blue-200 text-blue-600 hover:bg-blue-50'\r\n                      onClick={() => {\r\n                        const pdfUrl = typeof content.content === 'string' ? content.content :\r\n                          (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') ? content.content.value : '';\r\n                        if (pdfUrl) window.open(pdfUrl, '_blank');\r\n                      }}\r\n                    >\r\n                      <Eye className='mr-2 h-4 w-4' />\r\n                      Open in New Tab\r\n                    </Button>\r\n                    <Button\r\n                      size='sm'\r\n                      variant='outline'\r\n                      className='text-gray-600 hover:bg-gray-50'\r\n                      onClick={() => {\r\n                        const pdfUrl = typeof content.content === 'string' ? content.content :\r\n                          (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') ? content.content.value : '';\r\n                        if (pdfUrl) {\r\n                          const link = document.createElement('a');\r\n                          link.href = pdfUrl;\r\n                          link.download = content.title || 'document.pdf';\r\n                          document.body.appendChild(link);\r\n                          link.click();\r\n                          document.body.removeChild(link);\r\n                        }\r\n                      }}\r\n                    >\r\n                      <Download className='mr-2 h-4 w-4' />\r\n                      Download\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              ) : content.type === 'image' ? (\r\n                <div className='space-y-4'>\r\n                  <img\r\n                    src={\r\n                      typeof content.content === 'string'\r\n                        ? content.content\r\n                        : typeof content.content === 'object' &&\r\n                          content.content !== null &&\r\n                          'value' in content.content &&\r\n                          typeof content.content.value === 'string'\r\n                        ? content.content.value\r\n                        : ''\r\n                    }\r\n                    alt={content.title || 'Image'}\r\n                    className='max-w-full h-auto rounded border'\r\n                  />\r\n                  <div className='flex space-x-2'>\r\n                    <Button\r\n                      size='sm'\r\n                      variant='outline'\r\n                      className='border-blue-200 text-blue-600 hover:bg-blue-50'\r\n                      onClick={() => {\r\n                        const imageUrl = typeof content.content === 'string' ? content.content :\r\n                          (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') ? content.content.value : '';\r\n                        if (imageUrl) window.open(imageUrl, '_blank');\r\n                      }}\r\n                    >\r\n                      <ExternalLink className='mr-2 h-4 w-4' />\r\n                      Open in New Tab\r\n                    </Button>\r\n                    <Button\r\n                      size='sm'\r\n                      variant='outline'\r\n                      className='text-gray-600 hover:bg-gray-50'\r\n                      onClick={() => {\r\n                        const imageUrl = typeof content.content === 'string' ? content.content :\r\n                          (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') ? content.content.value : '';\r\n                        if (imageUrl) {\r\n                          const link = document.createElement('a');\r\n                          link.href = imageUrl;\r\n                          link.download = content.title || 'image.jpg';\r\n                          document.body.appendChild(link);\r\n                          link.click();\r\n                          document.body.removeChild(link);\r\n                        }\r\n                      }}\r\n                    >\r\n                      <Download className='mr-2 h-4 w-4' />\r\n                      Download\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              ) : (content.type === 'video' || content.type === 'zoom-recording') ? (\r\n                <div className='space-y-4'>\r\n                  <div className='aspect-video w-full overflow-hidden rounded-lg bg-gray-100'>\r\n                    {(() => {\r\n                      const videoUrl = typeof content.content === 'string' ? content.content :\r\n                                       (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') ? content.content.value : '';\r\n\r\n                      if (!videoUrl) {\r\n                        return (\r\n                          <div className='flex h-full w-full items-center justify-center text-center text-gray-500'>\r\n                            No video URL provided.\r\n                          </div>\r\n                        );\r\n                      }\r\n\r\n                      if (videoUrl.includes('youtube.com/watch?v=') || videoUrl.includes('youtu.be/')) {\r\n                        const youtubeId = videoUrl.split('v=')[1]?.split('&')[0] || videoUrl.split('/').pop();\r\n                        return (\r\n                          <iframe\r\n                            className='h-full w-full'\r\n                            src={`https://www.youtube.com/embed/${youtubeId}`}\r\n                            frameBorder='0'\r\n                            allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'\r\n                            allowFullScreen\r\n                            title={content.title}\r\n                          ></iframe>\r\n                        );\r\n                      } else if (videoUrl.includes('vimeo.com/')) {\r\n                        const vimeoId = videoUrl.split('/').pop();\r\n                        return (\r\n                          <iframe\r\n                            className='h-full w-full'\r\n                            src={`https://player.vimeo.com/video/${vimeoId}`}\r\n                            frameBorder='0'\r\n                            allow='autoplay; fullscreen; picture-in-picture'\r\n                            allowFullScreen\r\n                            title={content.title}\r\n                          ></iframe>\r\n                        );\r\n                      } else {\r\n                        // Generic video tag for other direct video links\r\n                        return (\r\n                          <video\r\n                            controls\r\n                            className='h-full w-full'\r\n                            src={videoUrl}\r\n                            title={content.title}\r\n                          >\r\n                            Your browser does not support the video tag.\r\n                          </video>\r\n                        );\r\n                      }\r\n                    })()}\r\n                  </div>\r\n                  <div className='flex space-x-2'>\r\n                    <Button\r\n                      size='sm'\r\n                      variant='outline'\r\n                      className='border-blue-200 text-blue-600 hover:bg-blue-50'\r\n                      onClick={() => {\r\n                        const videoUrl = typeof content.content === 'string' ? content.content :\r\n                                         (typeof content.content === 'object' && content.content !== null && 'value' in content.content && typeof content.content.value === 'string') ? content.content.value : '';\r\n                        if (videoUrl) window.open(videoUrl, '_blank');\r\n                      }}\r\n                    >\r\n                      <ExternalLink className='mr-2 h-4 w-4' />\r\n                      Open in New Tab\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className='space-y-4'>\r\n                  <div className='flex aspect-video items-center justify-center rounded-lg bg-gray-100'>\r\n                    <div className='text-center'>\r\n                      <Play className='mx-auto mb-2 h-12 w-12 text-gray-400' />\r\n                      <p className='text-sm text-gray-500'>\r\n                        Unsupported Media Type or Invalid Content.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n              \r\n              {/* Mark as Complete Button - Moved to bottom */}\r\n              <div className='mt-4 pt-4 border-t'>\r\n                <Button\r\n                  size='sm'\r\n                  variant={content.isCompleted ? 'default' : 'outline'}\r\n                  className={`min-w-[120px] ${\r\n                    content.isCompleted\r\n                      ? 'bg-green-600 text-white hover:bg-green-700'\r\n                      : 'text-gray-600 hover:bg-gray-50'\r\n                  }`}\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    onToggleComplete();\r\n                  }}\r\n                >\r\n                  <CheckCircle2 className='mr-2 h-4 w-4' />\r\n                  {content.isCompleted ? 'Completed' : 'Mark Complete'}\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};"], "names": [], "mappings": "AAAA,kBAAkB;;;;;AAElB;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;AAeO,MAAM,cAA0C,CAAC,EACtD,OAAO,EACP,gBAAgB,EAChB,UAAU,EACV,cAAc,EACf;IACC,2CAA2C;IAC3C,QAAQ,GAAG,CAAC,iCAAiC;IAE7C,MAAM,6BAA6B,CAAC;QAClC,MAAM,QAAQ,SAAS,KAAK,CAAC;QAC7B,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;gBACrB,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;YACtB;QACF;QACA,OAAO;IACT;IAEA,MAAM,eACJ,QAAQ,KAAK,IACb,CAAC,OAAO,QAAQ,OAAO,KAAK,YAC5B,QAAQ,OAAO,KAAK,QACpB,WAAW,QAAQ,OAAO,IAC1B,OAAO,QAAQ,OAAO,CAAC,KAAK,KAAK,WAC7B,2BAA2B,QAAQ,OAAO,CAAC,KAAK,IAChD,IAAI,KACR;IAEF,MAAM,8BAA8B;QAClC,mCAAmC;QACnC,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;QACpC,IAAI,CAAC,aAAa;QAElB,uCAAuC;QACvC,MAAM,cAAc,CAAC;;;;iBAIR,EAAE,QAAQ,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cAgFnB,EAAE,QAAQ,KAAK,CAAC;;;;IAI1B,CAAC;QAED,YAAY,QAAQ,CAAC,KAAK,CAAC;QAC3B,YAAY,QAAQ,CAAC,KAAK;QAE1B,yCAAyC;QACzC,MAAM,cAAc,YAAY,QAAQ,CAAC,cAAc,CAAC;QACxD,IAAI,aAAa;YACf,0DAA0D;YAC1D,IAAI,WAAW;YAEf,mFAAmF;YACnF,IAAI,OAAO,QAAQ,OAAO,KAAK,UAAU;gBACvC,WAAW,QAAQ,OAAO;YAC5B,OAAO,IACL,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,KAAK,QACpB,WAAW,QAAQ,OAAO,IAC1B,OAAO,QAAQ,OAAO,CAAC,KAAK,KAAK,UACjC;gBACA,WAAW,QAAQ,OAAO,CAAC,KAAK;YAClC,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,OAAO,GAAG;gBACzC,uCAAuC;gBACvC,WAAW,QAAQ,OAAO,CACvB,GAAG,CAAC,CAAC,QAAW,MAAM,IAAI,KAAK,SAAS,MAAM,KAAK,GAAG,IACtD,IAAI,CAAC;YACV;YAEA,UAAU;YACV,WAAW,SAAS,OAAO,CAAC,iBAAiB;YAC7C,WAAW,SAAS,OAAO,CAAC,gBAAgB;YAC5C,WAAW,SAAS,OAAO,CAAC,eAAe;YAE3C,kBAAkB;YAClB,WAAW,SAAS,OAAO,CAAC,mBAAmB;YAC/C,WAAW,SAAS,OAAO,CAAC,eAAe;YAE3C,QAAQ;YACR,WAAW,SAAS,OAAO,CAAC,gBAAgB;YAC5C,WAAW,SAAS,OAAO,CAAC,qBAAqB;YACjD,WAAW,SAAS,OAAO,CAAC,mBAAmB;YAE/C,4BAA4B;YAC5B,WAAW,SAAS,OAAO,CAAC,SAAS;YACrC,WAAW,QAAQ,WAAW;YAE9B,4BAA4B;YAC5B,WAAW,SAAS,OAAO,CAAC,aAAa;YAEzC,YAAY,SAAS,GAAG;QAC1B;QAEA,sCAAsC;QACtC,WAAW;YACT,YAAY,KAAK;YACjB,YAAY,KAAK;YACjB,YAAY,KAAK;QACnB,GAAG;IACL;IACA,MAAM,iBAAiB;QACrB,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;QACjC;IACF;IAEA,MAAM,sBAAsB;QAC1B,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;QAAE,WAAU;kBAC3C,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACV,SAAS;;0CAET,6LAAC;gCAAI,WAAU;;oCACZ;kDACD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAChC;;;;;;oDAEF,QAAQ,QAAQ,kBACf,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;;0EACjC,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,QAAQ,QAAQ;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;0CAM5B,6LAAC;gCAAI,WAAU;0CACZ,2BACC,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;yDAErB,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAK5B,4BACC,6LAAC;wBAAI,WAAU;;4BACZ,QAAQ,IAAI,KAAK,uBAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2LAAA,CAAA,UAAa;4CACZ,eAAe;gDAAC,gJAAA,CAAA,UAAS;6CAAC;4CAC1B,YAAY;gDACV,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;wDACC,WAAU;wDACT,GAAG,KAAK;;;;;;gDAGb,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;wDACC,WAAU;wDACT,GAAG,KAAK;;;;;;gDAGb,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;wDACC,WAAU;wDACT,GAAG,KAAK;;;;;;gDAGb,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;wDACC,WAAU;wDACT,GAAG,KAAK;;;;;;gDAGb,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACpB,6LAAC;wDAAE,WAAU;wDAAwB,GAAG,KAAK;;;;;;gDAE/C,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;wDAAG,WAAU;wDAAuB,GAAG,KAAK;;;;;;gDAE/C,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;wDAAG,WAAU;wDAA0B,GAAG,KAAK;;;;;;gDAElD,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;wDAAG,WAAU;wDAAQ,GAAG,KAAK;;;;;;gDAEhC,YAAY,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBAC7B,6LAAC;wDACC,WAAU;wDACT,GAAG,KAAK;;;;;;gDAGb,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;oDAC5C,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;oDACjD,MAAM,WAAW,CAAC;oDAClB,OAAO,yBACL,6LAAC;wDACC,WAAU;wDACT,GAAG,KAAK;kEAER;;;;;+EAGH,6LAAC;wDACC,WAAU;wDACT,GAAG,KAAK;kEAER;;;;;;gDAGP;gDACA,KAAK,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACtB,6LAAC;wDAAI,WAAU;wDAAQ,GAAG,KAAK;;;;;;gDAEjC,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACxB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAU;4DACT,GAAG,KAAK;;;;;;;;;;;gDAIf,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACxB,6LAAC;wDAAM,WAAU;wDAAc,GAAG,KAAK;;;;;;gDAEzC,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;wDACC,WAAU;wDACT,GAAG,KAAK;;;;;;gDAGb,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;wDACC,WAAU;wDACT,GAAG,KAAK;;;;;;gDAGb,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;wDAAG,WAAU;wDAAwB,GAAG,KAAK;;;;;;gDAEhD,QAAQ,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACzB,6LAAC;wDACC,WAAU;wDACT,GAAG,KAAK;;;;;;gDAGb,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;wDAAG,WAAU;wDAAU,GAAG,KAAK;;;;;;4CAEpC;sDAEC,OAAO,QAAQ,OAAO,KAAK,WACxB,QAAQ,OAAO,GACf,OAAO,QAAQ,OAAO,KAAK,YACzB,QAAQ,OAAO,KAAK,QACpB,WAAW,QAAQ,OAAO,IAC1B,OAAO,QAAQ,OAAO,CAAC,KAAK,KAAK,WACjC,QAAQ,OAAO,CAAC,KAAK,GACrB,MAAM,OAAO,CAAC,QAAQ,OAAO,IAC3B,QAAQ,OAAO,CACZ,GAAG,CAAC,CAAC,QACJ,MAAM,IAAI,KAAK,SAAS,MAAM,KAAK,GAAG,IAEvC,IAAI,CAAC,MACR;;;;;;;;;;;kDAGZ,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS;;0DAET,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;uCAIvC,QAAQ,IAAI,KAAK,sBACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK,GAAG,OAAO,QAAQ,OAAO,KAAK,WAAW,QAAQ,OAAO,GAAG,AAAC,OAAO,QAAQ,OAAO,KAAK,YAAY,QAAQ,OAAO,KAAK,QAAQ,WAAW,QAAQ,OAAO,IAAI,OAAO,QAAQ,OAAO,CAAC,KAAK,KAAK,WAAY,QAAQ,OAAO,CAAC,KAAK,GAAG,GAAG,qBAAqB,CAAC;wCAChQ,WAAU;wCACV,OAAO,QAAQ,KAAK;;;;;;kDAEtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS;oDACP,MAAM,SAAS,OAAO,QAAQ,OAAO,KAAK,WAAW,QAAQ,OAAO,GAClE,AAAC,OAAO,QAAQ,OAAO,KAAK,YAAY,QAAQ,OAAO,KAAK,QAAQ,WAAW,QAAQ,OAAO,IAAI,OAAO,QAAQ,OAAO,CAAC,KAAK,KAAK,WAAY,QAAQ,OAAO,CAAC,KAAK,GAAG;oDACzK,IAAI,QAAQ,OAAO,IAAI,CAAC,QAAQ;gDAClC;;kEAEA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGlC,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS;oDACP,MAAM,SAAS,OAAO,QAAQ,OAAO,KAAK,WAAW,QAAQ,OAAO,GAClE,AAAC,OAAO,QAAQ,OAAO,KAAK,YAAY,QAAQ,OAAO,KAAK,QAAQ,WAAW,QAAQ,OAAO,IAAI,OAAO,QAAQ,OAAO,CAAC,KAAK,KAAK,WAAY,QAAQ,OAAO,CAAC,KAAK,GAAG;oDACzK,IAAI,QAAQ;wDACV,MAAM,OAAO,SAAS,aAAa,CAAC;wDACpC,KAAK,IAAI,GAAG;wDACZ,KAAK,QAAQ,GAAG,QAAQ,KAAK,IAAI;wDACjC,SAAS,IAAI,CAAC,WAAW,CAAC;wDAC1B,KAAK,KAAK;wDACV,SAAS,IAAI,CAAC,WAAW,CAAC;oDAC5B;gDACF;;kEAEA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;uCAKzC,QAAQ,IAAI,KAAK,wBACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KACE,OAAO,QAAQ,OAAO,KAAK,WACvB,QAAQ,OAAO,GACf,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,KAAK,QACpB,WAAW,QAAQ,OAAO,IAC1B,OAAO,QAAQ,OAAO,CAAC,KAAK,KAAK,WACjC,QAAQ,OAAO,CAAC,KAAK,GACrB;wCAEN,KAAK,QAAQ,KAAK,IAAI;wCACtB,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS;oDACP,MAAM,WAAW,OAAO,QAAQ,OAAO,KAAK,WAAW,QAAQ,OAAO,GACpE,AAAC,OAAO,QAAQ,OAAO,KAAK,YAAY,QAAQ,OAAO,KAAK,QAAQ,WAAW,QAAQ,OAAO,IAAI,OAAO,QAAQ,OAAO,CAAC,KAAK,KAAK,WAAY,QAAQ,OAAO,CAAC,KAAK,GAAG;oDACzK,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU;gDACtC;;kEAEA,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG3C,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,SAAS;oDACP,MAAM,WAAW,OAAO,QAAQ,OAAO,KAAK,WAAW,QAAQ,OAAO,GACpE,AAAC,OAAO,QAAQ,OAAO,KAAK,YAAY,QAAQ,OAAO,KAAK,QAAQ,WAAW,QAAQ,OAAO,IAAI,OAAO,QAAQ,OAAO,CAAC,KAAK,KAAK,WAAY,QAAQ,OAAO,CAAC,KAAK,GAAG;oDACzK,IAAI,UAAU;wDACZ,MAAM,OAAO,SAAS,aAAa,CAAC;wDACpC,KAAK,IAAI,GAAG;wDACZ,KAAK,QAAQ,GAAG,QAAQ,KAAK,IAAI;wDACjC,SAAS,IAAI,CAAC,WAAW,CAAC;wDAC1B,KAAK,KAAK;wDACV,SAAS,IAAI,CAAC,WAAW,CAAC;oDAC5B;gDACF;;kEAEA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;uCAKzC,AAAC,QAAQ,IAAI,KAAK,WAAW,QAAQ,IAAI,KAAK,iCAChD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,CAAC;4CACA,MAAM,WAAW,OAAO,QAAQ,OAAO,KAAK,WAAW,QAAQ,OAAO,GACrD,AAAC,OAAO,QAAQ,OAAO,KAAK,YAAY,QAAQ,OAAO,KAAK,QAAQ,WAAW,QAAQ,OAAO,IAAI,OAAO,QAAQ,OAAO,CAAC,KAAK,KAAK,WAAY,QAAQ,OAAO,CAAC,KAAK,GAAG;4CAExL,IAAI,CAAC,UAAU;gDACb,qBACE,6LAAC;oDAAI,WAAU;8DAA2E;;;;;;4CAI9F;4CAEA,IAAI,SAAS,QAAQ,CAAC,2BAA2B,SAAS,QAAQ,CAAC,cAAc;gDAC/E,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI,SAAS,KAAK,CAAC,KAAK,GAAG;gDACnF,qBACE,6LAAC;oDACC,WAAU;oDACV,KAAK,CAAC,8BAA8B,EAAE,WAAW;oDACjD,aAAY;oDACZ,OAAM;oDACN,eAAe;oDACf,OAAO,QAAQ,KAAK;;;;;;4CAG1B,OAAO,IAAI,SAAS,QAAQ,CAAC,eAAe;gDAC1C,MAAM,UAAU,SAAS,KAAK,CAAC,KAAK,GAAG;gDACvC,qBACE,6LAAC;oDACC,WAAU;oDACV,KAAK,CAAC,+BAA+B,EAAE,SAAS;oDAChD,aAAY;oDACZ,OAAM;oDACN,eAAe;oDACf,OAAO,QAAQ,KAAK;;;;;;4CAG1B,OAAO;gDACL,iDAAiD;gDACjD,qBACE,6LAAC;oDACC,QAAQ;oDACR,WAAU;oDACV,KAAK;oDACL,OAAO,QAAQ,KAAK;8DACrB;;;;;;4CAIL;wCACF,CAAC;;;;;;kDAEH,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,WAAU;4CACV,SAAS;gDACP,MAAM,WAAW,OAAO,QAAQ,OAAO,KAAK,WAAW,QAAQ,OAAO,GACrD,AAAC,OAAO,QAAQ,OAAO,KAAK,YAAY,QAAQ,OAAO,KAAK,QAAQ,WAAW,QAAQ,OAAO,IAAI,OAAO,QAAQ,OAAO,CAAC,KAAK,KAAK,WAAY,QAAQ,OAAO,CAAC,KAAK,GAAG;gDACxL,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU;4CACtC;;8DAEA,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;qDAM/C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;0CAS7C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS,QAAQ,WAAW,GAAG,YAAY;oCAC3C,WAAW,CAAC,cAAc,EACxB,QAAQ,WAAW,GACf,+CACA,kCACJ;oCACF,SAAS,CAAC;wCACR,EAAE,eAAe;wCACjB;oCACF;;sDAEA,6LAAC,wNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCACvB,QAAQ,WAAW,GAAG,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvD;KAzkBa", "debugId": null}}, {"offset": {"line": 1777, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/content-viewer.tsx"], "sourcesContent": ["//content-viewer.tsx\r\nimport React, { useState, useEffect } from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport remarkGfm from 'remark-gfm';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { \r\n  Play, \r\n  FileText, \r\n  BookMarked, \r\n  CheckCircle, \r\n  Lock, \r\n  ChevronLeft, \r\n  ChevronRight,\r\n  Clock,\r\n  Target,\r\n  Award,\r\n  Download,\r\n  Eye,\r\n  ExternalLink,\r\n  Image\r\n} from 'lucide-react';\r\nimport { Course, Content, Quiz } from '@/types/lms';\r\n\r\ninterface ContentViewerProps {\r\n  course: Course;\r\n  currentModuleId: string;\r\n  currentChapterId: string;\r\n  currentContentId: string;\r\n  onNavigate: (moduleId: string, chapterId: string, contentId: string) => void;\r\n  onStartQuiz: (quizId: string, quizType: 'chapter' | 'module' | 'final') => void;\r\n  onContentComplete: (contentId: string) => void;\r\n}\r\n\r\nexport const ContentViewer: React.FC<ContentViewerProps> = ({\r\n  course,\r\n  currentModuleId,\r\n  currentChapterId,\r\n  currentContentId,\r\n  onNavigate,\r\n  onStartQuiz,\r\n  onContentComplete\r\n}) => {\r\n  const [isCompleted, setIsCompleted] = useState(false);\r\n\r\n  // Find current content\r\n  const currentModule = course.modules.find(m => m.id === currentModuleId);\r\n  const currentChapter = currentModule?.chapters.find(c => c.id === currentChapterId);\r\n  const currentContent = currentChapter?.contents.find(c => c.id === currentContentId);\r\n\r\n  // Find navigation info\r\n  const moduleIndex = course.modules.findIndex(m => m.id === currentModuleId);\r\n  const chapterIndex = currentModule?.chapters.findIndex(c => c.id === currentChapterId) || 0;\r\n  const contentIndex = currentChapter?.contents.findIndex(c => c.id === currentContentId) || 0;\r\n\r\n  // Navigation helpers\r\n  const getNextContent = () => {\r\n    if (!currentModule || !currentChapter || !currentContent) return null;\r\n    \r\n    const nextContentIndex = contentIndex + 1;\r\n    if (nextContentIndex < currentChapter.contents.length) {\r\n      return {\r\n        moduleId: currentModuleId,\r\n        chapterId: currentChapterId,\r\n        contentId: currentChapter.contents[nextContentIndex].id\r\n      };\r\n    }\r\n    \r\n    // Check if there's a chapter quiz\r\n    if (currentChapter.quiz && currentChapter.contents.every(c => c.isCompleted)) {\r\n      return {\r\n        moduleId: currentModuleId,\r\n        chapterId: currentChapterId,\r\n        contentId: 'chapter-quiz'\r\n      };\r\n    }\r\n    \r\n    // Check next chapter\r\n    const nextChapterIndex = chapterIndex + 1;\r\n    if (nextChapterIndex < currentModule.chapters.length) {\r\n      const nextChapter = currentModule.chapters[nextChapterIndex];\r\n      if (nextChapter.isUnlocked && nextChapter.contents.length > 0) {\r\n        return {\r\n          moduleId: currentModuleId,\r\n          chapterId: nextChapter.id,\r\n          contentId: nextChapter.contents[0].id\r\n        };\r\n      }\r\n    }\r\n    \r\n    // Check module quiz\r\n    if (currentModule.moduleQuiz && currentModule.chapters.every(ch => \r\n      ch.contents.every(c => c.isCompleted) && ch.quiz.isPassed\r\n    )) {\r\n      return {\r\n        moduleId: currentModuleId,\r\n        chapterId: 'module-quiz',\r\n        contentId: 'module-quiz'\r\n      };\r\n    }\r\n    \r\n    return null;\r\n  };\r\n\r\n  const getPreviousContent = () => {\r\n    if (!currentModule || !currentChapter || !currentContent) return null;\r\n    \r\n    const prevContentIndex = contentIndex - 1;\r\n    if (prevContentIndex >= 0) {\r\n      return {\r\n        moduleId: currentModuleId,\r\n        chapterId: currentChapterId,\r\n        contentId: currentChapter.contents[prevContentIndex].id\r\n      };\r\n    }\r\n    \r\n    // Check previous chapter\r\n    const prevChapterIndex = chapterIndex - 1;\r\n    if (prevChapterIndex >= 0) {\r\n      const prevChapter = currentModule.chapters[prevChapterIndex];\r\n      if (prevChapter.contents.length > 0) {\r\n        return {\r\n          moduleId: currentModuleId,\r\n          chapterId: prevChapter.id,\r\n          contentId: prevChapter.contents[prevChapter.contents.length - 1].id\r\n        };\r\n      }\r\n    }\r\n    \r\n    return null;\r\n  };\r\n\r\n  const nextContent = getNextContent();\r\n  const prevContent = getPreviousContent();\r\n\r\n  useEffect(() => {\r\n    if (currentContent) {\r\n      setIsCompleted(currentContent.isCompleted);\r\n    }\r\n  }, [currentContent]);\r\n\r\n  const handleContentComplete = () => {\r\n    if (currentContent && !currentContent.isCompleted) {\r\n      onContentComplete(currentContent.id);\r\n      setIsCompleted(true);\r\n    }\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (nextContent) {\r\n      if (nextContent.contentId === 'chapter-quiz') {\r\n        onStartQuiz(currentChapter?.quiz?.id || '', 'chapter');\r\n      } else if (nextContent.contentId === 'module-quiz') {\r\n        onStartQuiz(currentModule?.moduleQuiz?.id || '', 'module');\r\n      } else {\r\n        onNavigate(nextContent.moduleId, nextContent.chapterId, nextContent.contentId);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (prevContent) {\r\n      onNavigate(prevContent.moduleId, prevContent.chapterId, prevContent.contentId);\r\n    }\r\n  };\r\n\r\n  // Download markdown as PDF function\r\n  const handleDownloadMarkdownAsPDF = () => {\r\n    if (currentContent?.type !== 'text') return;\r\n    \r\n    const printWindow = window.open('', '_blank');\r\n    if (!printWindow) return;\r\n\r\n    const htmlContent = `\r\n      <!DOCTYPE html>\r\n      <html>\r\n        <head>\r\n          <title>${currentContent.title}</title>\r\n          <style>\r\n            @media print {\r\n              @page {\r\n                size: A4;\r\n                margin: 2cm;\r\n              }\r\n              body {\r\n                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\r\n                line-height: 1.6;\r\n                color: #333;\r\n              }\r\n            }\r\n            body {\r\n              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\r\n              line-height: 1.6;\r\n              color: #333;\r\n              max-width: 800px;\r\n              margin: 0 auto;\r\n              padding: 20px;\r\n            }\r\n            h1 { color: #2d3748; font-size: 2em; margin-bottom: 1em; }\r\n            h2 { color: #4a5568; font-size: 1.5em; margin: 1.5em 0 0.5em; }\r\n            h3 { color: #4a5568; font-size: 1.25em; margin: 1.2em 0 0.5em; }\r\n            h4 { color: #718096; font-size: 1.1em; margin: 1em 0 0.5em; }\r\n            p { margin-bottom: 1em; }\r\n            ul, ol { margin-bottom: 1em; padding-left: 2em; }\r\n            li { margin-bottom: 0.25em; }\r\n            blockquote {\r\n              border-left: 4px solid #3182ce;\r\n              background: #ebf8ff;\r\n              padding: 1em;\r\n              margin: 1em 0;\r\n              font-style: italic;\r\n            }\r\n            code {\r\n              background: #f7fafc;\r\n              padding: 0.2em 0.4em;\r\n              border-radius: 3px;\r\n              font-family: 'Courier New', monospace;\r\n              font-size: 0.9em;\r\n            }\r\n            pre {\r\n              background: #2d3748;\r\n              color: #f7fafc;\r\n              padding: 1em;\r\n              border-radius: 5px;\r\n              overflow-x: auto;\r\n              margin: 1em 0;\r\n            }\r\n            pre code {\r\n              background: none;\r\n              padding: 0;\r\n              color: inherit;\r\n            }\r\n            table {\r\n              border-collapse: collapse;\r\n              width: 100%;\r\n              margin: 1em 0;\r\n            }\r\n            th, td {\r\n              border: 1px solid #e2e8f0;\r\n              padding: 0.5em;\r\n              text-align: left;\r\n            }\r\n            th {\r\n              background: #f7fafc;\r\n              font-weight: 600;\r\n            }\r\n            hr {\r\n              border: none;\r\n              height: 1px;\r\n              background: #e2e8f0;\r\n              margin: 2em 0;\r\n            }\r\n            strong { font-weight: 600; }\r\n            em { font-style: italic; }\r\n          </style>\r\n        </head>\r\n        <body>\r\n          <h1>${currentContent.title}</h1>\r\n          <div id=\"markdown-content\"></div>\r\n        </body>\r\n      </html>\r\n    `;\r\n\r\n    printWindow.document.write(htmlContent);\r\n    printWindow.document.close();\r\n\r\n    const markdownDiv = printWindow.document.getElementById('markdown-content');\r\n    if (markdownDiv) {\r\n      let htmlText = '';\r\n      \r\n      if (typeof currentContent.content === 'string') {\r\n        htmlText = currentContent.content;\r\n      } else if (\r\n        typeof currentContent.content === 'object' &&\r\n        currentContent.content !== null &&\r\n        'value' in currentContent.content &&\r\n        typeof currentContent.content.value === 'string'\r\n      ) {\r\n        htmlText = currentContent.content.value;\r\n      } else if (Array.isArray(currentContent.content)) {\r\n        htmlText = currentContent.content\r\n          .map((block) => (block.type === 'text' ? block.value : ''))\r\n          .join('');\r\n      }\r\n      \r\n      // Simple markdown to HTML conversion\r\n      htmlText = htmlText.replace(/^### (.*$)/gim, '<h3>$1</h3>');\r\n      htmlText = htmlText.replace(/^## (.*$)/gim, '<h2>$1</h2>');\r\n      htmlText = htmlText.replace(/^# (.*$)/gim, '<h1>$1</h1>');\r\n      htmlText = htmlText.replace(/\\*\\*(.*)\\*\\*/gim, '<strong>$1</strong>');\r\n      htmlText = htmlText.replace(/\\*(.*)\\*/gim, '<em>$1</em>');\r\n      htmlText = htmlText.replace(/^\\* (.*$)/gim, '<li>$1</li>');\r\n      htmlText = htmlText.replace(/(<li>.*<\\/li>)/gim, '<ul>$1</ul>');\r\n      htmlText = htmlText.replace(/^\\d+\\. (.*$)/gim, '<li>$1</li>');\r\n      htmlText = htmlText.replace(/\\n\\n/g, '</p><p>');\r\n      htmlText = '<p>' + htmlText + '</p>';\r\n      htmlText = htmlText.replace(/<p><\\/p>/g, '');\r\n      \r\n      markdownDiv.innerHTML = htmlText;\r\n    }\r\n\r\n    setTimeout(() => {\r\n      printWindow.focus();\r\n      printWindow.print();\r\n      printWindow.close();\r\n    }, 250);\r\n  };\r\n\r\n  if (!currentContent) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-96\">\r\n        <div className=\"text-center\">\r\n          <BookMarked className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\r\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Konten tidak ditemukan</h3>\r\n          <p className=\"text-gray-600\">Silakan pilih konten dari navigasi di sebelah kiri.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const getContentIcon = (type: string) => {\r\n    switch (type) {\r\n      case 'video':\r\n        return <Play className=\"h-5 w-5 text-red-500\" />;\r\n      case 'pdf':\r\n        return <FileText className=\"h-5 w-5 text-red-600\" />;\r\n      case 'zoom-recording':\r\n        return <Play className=\"h-5 w-5 text-blue-500\" />;\r\n      case 'image':\r\n        return <Image className=\"h-5 w-5 text-green-500\" />;\r\n      default:\r\n        return <BookMarked className=\"h-5 w-5 text-blue-500\" />;\r\n    }\r\n  };\r\n\r\n  const getContentTypeLabel = (type: string) => {\r\n    switch (type) {\r\n      case 'video':\r\n        return 'Video';\r\n      case 'pdf':\r\n        return 'PDF Document';\r\n      case 'zoom-recording':\r\n        return 'Zoom Recording';\r\n      case 'image':\r\n        return 'Image';\r\n      default:\r\n        return 'Reading Material';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"max-w-4xl mx-auto\">\r\n      {/* Content Header */}\r\n      <div className=\"mb-6\">\r\n        <div className=\"flex items-center gap-2 mb-2\">\r\n          {getContentIcon(currentContent.type)}\r\n          <Badge variant=\"outline\" className=\"text-xs\">\r\n            {getContentTypeLabel(currentContent.type)}\r\n          </Badge>\r\n          {currentContent.duration && (\r\n            <div className=\"flex items-center text-sm text-gray-500 ml-2\">\r\n              <Clock className=\"h-4 w-4 mr-1\" />\r\n              {currentContent.duration}\r\n            </div>\r\n          )}\r\n        </div>\r\n        <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\r\n          {currentContent.title}\r\n        </h1>\r\n        <p className=\"text-gray-600\">\r\n          {currentModule?.title} • {currentChapter?.title}\r\n        </p>\r\n      </div>\r\n\r\n      {/* Content Body */}\r\n      <div className=\"bg-white rounded-lg border p-6 mb-6\">\r\n        {/* Text Content with Markdown */}\r\n        {currentContent.type === 'text' && (\r\n          <div className=\"space-y-4\">\r\n            <div className=\"prose prose-sm max-w-none text-gray-700\">\r\n              <ReactMarkdown\r\n                remarkPlugins={[remarkGfm]}\r\n                components={{\r\n                  h1: ({ node, ...props }) => (\r\n                    <h1 className=\"mb-4 text-2xl font-bold text-gray-900\" {...props} />\r\n                  ),\r\n                  h2: ({ node, ...props }) => (\r\n                    <h2 className=\"mb-3 text-xl font-semibold text-gray-800\" {...props} />\r\n                  ),\r\n                  h3: ({ node, ...props }) => (\r\n                    <h3 className=\"mb-2 text-lg font-semibold text-gray-800\" {...props} />\r\n                  ),\r\n                  h4: ({ node, ...props }) => (\r\n                    <h4 className=\"mb-2 text-base font-semibold text-gray-700\" {...props} />\r\n                  ),\r\n                  p: ({ node, ...props }) => (\r\n                    <p className=\"mb-3 leading-relaxed\" {...props} />\r\n                  ),\r\n                  ul: ({ node, ...props }) => (\r\n                    <ul className=\"mb-3 ml-4 list-disc\" {...props} />\r\n                  ),\r\n                  ol: ({ node, ...props }) => (\r\n                    <ol className=\"mb-3 ml-4 list-decimal\" {...props} />\r\n                  ),\r\n                  li: ({ node, ...props }) => (\r\n                    <li className=\"mb-1\" {...props} />\r\n                  ),\r\n                  blockquote: ({ node, ...props }) => (\r\n                    <blockquote className=\"mb-4 rounded-r border-l-4 border-blue-200 bg-blue-50 py-2 pl-4 italic\" {...props} />\r\n                  ),\r\n                  code: ({ node, className, children, ...props }) => {\r\n                    const match = /language-(\\w+)/.exec(className || '');\r\n                    const isInline = !match;\r\n                    return isInline ? (\r\n                      <code className=\"rounded bg-gray-100 px-1 py-0.5 font-mono text-sm\" {...props}>\r\n                        {children}\r\n                      </code>\r\n                    ) : (\r\n                      <code className=\"block overflow-x-auto rounded bg-gray-900 p-4 font-mono text-sm text-gray-100\" {...props}>\r\n                        {children}\r\n                      </code>\r\n                    );\r\n                  },\r\n                  pre: ({ node, ...props }) => (\r\n                    <pre className=\"mb-4\" {...props} />\r\n                  ),\r\n                  table: ({ node, ...props }) => (\r\n                    <div className=\"mb-4 overflow-x-auto\">\r\n                      <table className=\"min-w-full rounded border border-gray-200\" {...props} />\r\n                    </div>\r\n                  ),\r\n                  thead: ({ node, ...props }) => (\r\n                    <thead className=\"bg-gray-50\" {...props} />\r\n                  ),\r\n                  th: ({ node, ...props }) => (\r\n                    <th className=\"border border-gray-200 px-3 py-2 text-left font-semibold\" {...props} />\r\n                  ),\r\n                  td: ({ node, ...props }) => (\r\n                    <td className=\"border border-gray-200 px-3 py-2\" {...props} />\r\n                  ),\r\n                  hr: ({ node, ...props }) => (\r\n                    <hr className=\"my-6 border-gray-300\" {...props} />\r\n                  ),\r\n                  strong: ({ node, ...props }) => (\r\n                    <strong className=\"font-semibold text-gray-900\" {...props} />\r\n                  ),\r\n                  em: ({ node, ...props }) => (\r\n                    <em className=\"italic\" {...props} />\r\n                  )\r\n                }}\r\n              >\r\n                {typeof currentContent.content === 'string'\r\n                  ? currentContent.content\r\n                  : typeof currentContent.content === 'object' &&\r\n                      currentContent.content !== null &&\r\n                      'value' in currentContent.content &&\r\n                      typeof currentContent.content.value === 'string'\r\n                    ? currentContent.content.value\r\n                    : Array.isArray(currentContent.content)\r\n                      ? currentContent.content\r\n                          .map((block) => (block.type === 'text' ? block.value : ''))\r\n                          .join('')\r\n                      : ''}\r\n              </ReactMarkdown>\r\n            </div>\r\n            <Button\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n              className=\"border-blue-200 text-blue-600 hover:bg-blue-50\"\r\n              onClick={handleDownloadMarkdownAsPDF}\r\n            >\r\n              <Download className=\"mr-2 h-4 w-4\" />\r\n              Download as PDF\r\n            </Button>\r\n          </div>\r\n        )}\r\n\r\n        {/* PDF Content */}\r\n        {currentContent.type === 'pdf' && (\r\n          <div className=\"space-y-4\">\r\n            <iframe\r\n              src={`${typeof currentContent.content === 'string' ? currentContent.content : \r\n                (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : ''}#toolbar=0&navpanes=0`}\r\n              className=\"w-full h-96 rounded border\"\r\n              title={currentContent.title}\r\n            />\r\n            <div className=\"flex space-x-2\">\r\n              <Button\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n                className=\"border-blue-200 text-blue-600 hover:bg-blue-50\"\r\n                onClick={() => {\r\n                  const pdfUrl = typeof currentContent.content === 'string' ? currentContent.content :\r\n                    (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : '';\r\n                  if (pdfUrl) window.open(pdfUrl, '_blank');\r\n                }}\r\n              >\r\n                <Eye className=\"mr-2 h-4 w-4\" />\r\n                Open in New Tab\r\n              </Button>\r\n              <Button\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n                className=\"text-gray-600 hover:bg-gray-50\"\r\n                onClick={() => {\r\n                  const pdfUrl = typeof currentContent.content === 'string' ? currentContent.content :\r\n                    (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : '';\r\n                  if (pdfUrl) {\r\n                    const link = document.createElement('a');\r\n                    link.href = pdfUrl;\r\n                    link.download = currentContent.title || 'document.pdf';\r\n                    document.body.appendChild(link);\r\n                    link.click();\r\n                    document.body.removeChild(link);\r\n                  }\r\n                }}\r\n              >\r\n                <Download className=\"mr-2 h-4 w-4\" />\r\n                Download\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Image Content */}\r\n        {currentContent.type === 'image' && (\r\n          <div className=\"space-y-4\">\r\n            <img\r\n              src={typeof currentContent.content === 'string' ? currentContent.content :\r\n                (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : ''}\r\n              alt={currentContent.title || 'Image'}\r\n              className=\"max-w-full h-auto rounded border\"\r\n            />\r\n            <div className=\"flex space-x-2\">\r\n              <Button\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n                className=\"border-blue-200 text-blue-600 hover:bg-blue-50\"\r\n                onClick={() => {\r\n                  const imageUrl = typeof currentContent.content === 'string' ? currentContent.content :\r\n                    (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : '';\r\n                  if (imageUrl) window.open(imageUrl, '_blank');\r\n                }}\r\n              >\r\n                <ExternalLink className=\"mr-2 h-4 w-4\" />\r\n                Open in New Tab\r\n              </Button>\r\n              <Button\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n                className=\"text-gray-600 hover:bg-gray-50\"\r\n                onClick={() => {\r\n                  const imageUrl = typeof currentContent.content === 'string' ? currentContent.content :\r\n                    (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : '';\r\n                  if (imageUrl) {\r\n                    const link = document.createElement('a');\r\n                    link.href = imageUrl;\r\n                    link.download = currentContent.title || 'image.jpg';\r\n                    document.body.appendChild(link);\r\n                    link.click();\r\n                    document.body.removeChild(link);\r\n                  }\r\n                }}\r\n              >\r\n                <Download className=\"mr-2 h-4 w-4\" />\r\n                Download\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Video Content */}\r\n        {(currentContent.type === 'video' || currentContent.type === 'zoom-recording') && (\r\n          <div className=\"space-y-4\">\r\n            <div className=\"aspect-video w-full overflow-hidden rounded-lg bg-gray-100\">\r\n              {(() => {\r\n                const videoUrl = typeof currentContent.content === 'string' ? currentContent.content :\r\n                                 (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : '';\r\n\r\n                if (!videoUrl) {\r\n                  return (\r\n                    <div className=\"flex h-full w-full items-center justify-center text-center text-gray-500\">\r\n                      No video URL provided.\r\n                    </div>\r\n                  );\r\n                }\r\n\r\n                if (videoUrl.includes('youtube.com/watch?v=') || videoUrl.includes('youtu.be/')) {\r\n                  const youtubeId = videoUrl.split('v=')[1]?.split('&')[0] || videoUrl.split('/').pop();\r\n                  return (\r\n                    <iframe\r\n                      className=\"h-full w-full\"\r\n                      src={`https://www.youtube.com/embed/${youtubeId}`}\r\n                      frameBorder=\"0\"\r\n                      allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\r\n                      allowFullScreen\r\n                      title={currentContent.title}\r\n                    ></iframe>\r\n                  );\r\n                } else if (videoUrl.includes('vimeo.com/')) {\r\n                  const vimeoId = videoUrl.split('/').pop();\r\n                  return (\r\n                    <iframe\r\n                      className=\"h-full w-full\"\r\n                      src={`https://player.vimeo.com/video/${vimeoId}`}\r\n                      frameBorder=\"0\"\r\n                      allow=\"autoplay; fullscreen; picture-in-picture\"\r\n                      allowFullScreen\r\n                      title={currentContent.title}\r\n                    ></iframe>\r\n                  );\r\n                } else {\r\n                  return (\r\n                    <video\r\n                      controls\r\n                      className=\"h-full w-full\"\r\n                      src={videoUrl}\r\n                      title={currentContent.title}\r\n                    >\r\n                      Your browser does not support the video tag.\r\n                    </video>\r\n                  );\r\n                }\r\n              })()}\r\n            </div>\r\n            <div className=\"flex space-x-2\">\r\n              <Button\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n                className=\"border-blue-200 text-blue-600 hover:bg-blue-50\"\r\n                onClick={() => {\r\n                  const videoUrl = typeof currentContent.content === 'string' ? currentContent.content :\r\n                                   (typeof currentContent.content === 'object' && currentContent.content !== null && 'value' in currentContent.content && typeof currentContent.content.value === 'string') ? currentContent.content.value : '';\r\n                  if (videoUrl) window.open(videoUrl, '_blank');\r\n                }}\r\n              >\r\n                <ExternalLink className=\"mr-2 h-4 w-4\" />\r\n                Open in New Tab\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Unsupported content type */}\r\n        {!['text', 'video', 'pdf', 'zoom-recording', 'image'].includes(currentContent.type) && (\r\n          <div className=\"space-y-4\">\r\n            <div className=\"flex aspect-video items-center justify-center rounded-lg bg-gray-100\">\r\n              <div className=\"text-center\">\r\n                <Play className=\"mx-auto mb-2 h-12 w-12 text-gray-400\" />\r\n                <p className=\"text-sm text-gray-500\">\r\n                  Unsupported Media Type or Invalid Content.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        \r\n        \r\n      </div>\r\n\r\n      {/* Content Actions */}\r\n      <div className=\"flex items-center justify-between mb-6\">\r\n        <div className=\"flex items-center space-x-4\">\r\n          {!isCompleted ? (\r\n            <Button \r\n              onClick={handleContentComplete}\r\n              className=\"flex items-center space-x-2 bg-green-600 hover:bg-green-700\"\r\n            >\r\n              <CheckCircle className=\"h-4 w-4\" />\r\n              <span>Tandai Selesai</span>\r\n            </Button>\r\n          ) : (\r\n            <div className=\"flex items-center text-green-600\">\r\n              <CheckCircle className=\"h-4 w-4 mr-2\" />\r\n              <span className=\"text-sm font-medium\">Selesai</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex items-center space-x-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={handlePrevious}\r\n            disabled={!prevContent}\r\n            className=\"flex items-center space-x-2\"\r\n          >\r\n            <ChevronLeft className=\"h-4 w-4\" />\r\n            <span>Sebelumnya</span>\r\n          </Button>\r\n          \r\n          <Button\r\n            onClick={handleNext}\r\n            disabled={!nextContent}\r\n            className=\"flex items-center space-x-2\"\r\n          >\r\n            <span>Selanjutnya</span>\r\n            <ChevronRight className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Progress Indicator */}\r\n      <div className=\"mt-6\">\r\n        <div className=\"flex items-center justify-between text-sm text-gray-600 mb-2\">\r\n          <span>Kemajuan Bab</span>\r\n          <span>{Math.round((contentIndex + 1) / (currentChapter?.contents.length || 1) * 100)}%</span>\r\n        </div>\r\n        <Progress \r\n          value={(contentIndex + 1) / (currentChapter?.contents.length || 1) * 100} \r\n          className=\"h-2\" \r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};"], "names": [], "mappings": "AAAA,oBAAoB;;;;;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;AA4BO,MAAM,gBAA8C,CAAC,EAC1D,MAAM,EACN,eAAe,EACf,gBAAgB,EAChB,gBAAgB,EAChB,UAAU,EACV,WAAW,EACX,iBAAiB,EAClB;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,uBAAuB;IACvB,MAAM,gBAAgB,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACxD,MAAM,iBAAiB,eAAe,SAAS,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK;IAClE,MAAM,iBAAiB,gBAAgB,SAAS,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK;IAEnE,uBAAuB;IACvB,MAAM,cAAc,OAAO,OAAO,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC3D,MAAM,eAAe,eAAe,SAAS,UAAU,CAAA,IAAK,EAAE,EAAE,KAAK,qBAAqB;IAC1F,MAAM,eAAe,gBAAgB,SAAS,UAAU,CAAA,IAAK,EAAE,EAAE,KAAK,qBAAqB;IAE3F,qBAAqB;IACrB,MAAM,iBAAiB;QACrB,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,gBAAgB,OAAO;QAEjE,MAAM,mBAAmB,eAAe;QACxC,IAAI,mBAAmB,eAAe,QAAQ,CAAC,MAAM,EAAE;YACrD,OAAO;gBACL,UAAU;gBACV,WAAW;gBACX,WAAW,eAAe,QAAQ,CAAC,iBAAiB,CAAC,EAAE;YACzD;QACF;QAEA,kCAAkC;QAClC,IAAI,eAAe,IAAI,IAAI,eAAe,QAAQ,CAAC,KAAK,CAAC,CAAA,IAAK,EAAE,WAAW,GAAG;YAC5E,OAAO;gBACL,UAAU;gBACV,WAAW;gBACX,WAAW;YACb;QACF;QAEA,qBAAqB;QACrB,MAAM,mBAAmB,eAAe;QACxC,IAAI,mBAAmB,cAAc,QAAQ,CAAC,MAAM,EAAE;YACpD,MAAM,cAAc,cAAc,QAAQ,CAAC,iBAAiB;YAC5D,IAAI,YAAY,UAAU,IAAI,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC7D,OAAO;oBACL,UAAU;oBACV,WAAW,YAAY,EAAE;oBACzB,WAAW,YAAY,QAAQ,CAAC,EAAE,CAAC,EAAE;gBACvC;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,cAAc,UAAU,IAAI,cAAc,QAAQ,CAAC,KAAK,CAAC,CAAA,KAC3D,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,GAAG,IAAI,CAAC,QAAQ,GACxD;YACD,OAAO;gBACL,UAAU;gBACV,WAAW;gBACX,WAAW;YACb;QACF;QAEA,OAAO;IACT;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,gBAAgB,OAAO;QAEjE,MAAM,mBAAmB,eAAe;QACxC,IAAI,oBAAoB,GAAG;YACzB,OAAO;gBACL,UAAU;gBACV,WAAW;gBACX,WAAW,eAAe,QAAQ,CAAC,iBAAiB,CAAC,EAAE;YACzD;QACF;QAEA,yBAAyB;QACzB,MAAM,mBAAmB,eAAe;QACxC,IAAI,oBAAoB,GAAG;YACzB,MAAM,cAAc,cAAc,QAAQ,CAAC,iBAAiB;YAC5D,IAAI,YAAY,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACnC,OAAO;oBACL,UAAU;oBACV,WAAW,YAAY,EAAE;oBACzB,WAAW,YAAY,QAAQ,CAAC,YAAY,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE;gBACrE;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,cAAc;IACpB,MAAM,cAAc;IAEpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,gBAAgB;gBAClB,eAAe,eAAe,WAAW;YAC3C;QACF;kCAAG;QAAC;KAAe;IAEnB,MAAM,wBAAwB;QAC5B,IAAI,kBAAkB,CAAC,eAAe,WAAW,EAAE;YACjD,kBAAkB,eAAe,EAAE;YACnC,eAAe;QACjB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,aAAa;YACf,IAAI,YAAY,SAAS,KAAK,gBAAgB;gBAC5C,YAAY,gBAAgB,MAAM,MAAM,IAAI;YAC9C,OAAO,IAAI,YAAY,SAAS,KAAK,eAAe;gBAClD,YAAY,eAAe,YAAY,MAAM,IAAI;YACnD,OAAO;gBACL,WAAW,YAAY,QAAQ,EAAE,YAAY,SAAS,EAAE,YAAY,SAAS;YAC/E;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,aAAa;YACf,WAAW,YAAY,QAAQ,EAAE,YAAY,SAAS,EAAE,YAAY,SAAS;QAC/E;IACF;IAEA,oCAAoC;IACpC,MAAM,8BAA8B;QAClC,IAAI,gBAAgB,SAAS,QAAQ;QAErC,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;QACpC,IAAI,CAAC,aAAa;QAElB,MAAM,cAAc,CAAC;;;;iBAIR,EAAE,eAAe,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cAgF1B,EAAE,eAAe,KAAK,CAAC;;;;IAIjC,CAAC;QAED,YAAY,QAAQ,CAAC,KAAK,CAAC;QAC3B,YAAY,QAAQ,CAAC,KAAK;QAE1B,MAAM,cAAc,YAAY,QAAQ,CAAC,cAAc,CAAC;QACxD,IAAI,aAAa;YACf,IAAI,WAAW;YAEf,IAAI,OAAO,eAAe,OAAO,KAAK,UAAU;gBAC9C,WAAW,eAAe,OAAO;YACnC,OAAO,IACL,OAAO,eAAe,OAAO,KAAK,YAClC,eAAe,OAAO,KAAK,QAC3B,WAAW,eAAe,OAAO,IACjC,OAAO,eAAe,OAAO,CAAC,KAAK,KAAK,UACxC;gBACA,WAAW,eAAe,OAAO,CAAC,KAAK;YACzC,OAAO,IAAI,MAAM,OAAO,CAAC,eAAe,OAAO,GAAG;gBAChD,WAAW,eAAe,OAAO,CAC9B,GAAG,CAAC,CAAC,QAAW,MAAM,IAAI,KAAK,SAAS,MAAM,KAAK,GAAG,IACtD,IAAI,CAAC;YACV;YAEA,qCAAqC;YACrC,WAAW,SAAS,OAAO,CAAC,iBAAiB;YAC7C,WAAW,SAAS,OAAO,CAAC,gBAAgB;YAC5C,WAAW,SAAS,OAAO,CAAC,eAAe;YAC3C,WAAW,SAAS,OAAO,CAAC,mBAAmB;YAC/C,WAAW,SAAS,OAAO,CAAC,eAAe;YAC3C,WAAW,SAAS,OAAO,CAAC,gBAAgB;YAC5C,WAAW,SAAS,OAAO,CAAC,qBAAqB;YACjD,WAAW,SAAS,OAAO,CAAC,mBAAmB;YAC/C,WAAW,SAAS,OAAO,CAAC,SAAS;YACrC,WAAW,QAAQ,WAAW;YAC9B,WAAW,SAAS,OAAO,CAAC,aAAa;YAEzC,YAAY,SAAS,GAAG;QAC1B;QAEA,WAAW;YACT,YAAY,KAAK;YACjB,YAAY,KAAK;YACjB,YAAY,KAAK;QACnB,GAAG;IACL;IAEA,IAAI,CAAC,gBAAgB;QACnB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;kCACtB,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;QACjC;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,eAAe,eAAe,IAAI;0CACnC,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAChC,oBAAoB,eAAe,IAAI;;;;;;4BAEzC,eAAe,QAAQ,kBACtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAChB,eAAe,QAAQ;;;;;;;;;;;;;kCAI9B,6LAAC;wBAAG,WAAU;kCACX,eAAe,KAAK;;;;;;kCAEvB,6LAAC;wBAAE,WAAU;;4BACV,eAAe;4BAAM;4BAAI,gBAAgB;;;;;;;;;;;;;0BAK9C,6LAAC;gBAAI,WAAU;;oBAEZ,eAAe,IAAI,KAAK,wBACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,2LAAA,CAAA,UAAa;oCACZ,eAAe;wCAAC,gJAAA,CAAA,UAAS;qCAAC;oCAC1B,YAAY;wCACV,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;gDAAG,WAAU;gDAAyC,GAAG,KAAK;;;;;;wCAEjE,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;gDAAG,WAAU;gDAA4C,GAAG,KAAK;;;;;;wCAEpE,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;gDAAG,WAAU;gDAA4C,GAAG,KAAK;;;;;;wCAEpE,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;gDAAG,WAAU;gDAA8C,GAAG,KAAK;;;;;;wCAEtE,GAAG,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACpB,6LAAC;gDAAE,WAAU;gDAAwB,GAAG,KAAK;;;;;;wCAE/C,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;gDAAG,WAAU;gDAAuB,GAAG,KAAK;;;;;;wCAE/C,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;gDAAG,WAAU;gDAA0B,GAAG,KAAK;;;;;;wCAElD,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;gDAAG,WAAU;gDAAQ,GAAG,KAAK;;;;;;wCAEhC,YAAY,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBAC7B,6LAAC;gDAAW,WAAU;gDAAyE,GAAG,KAAK;;;;;;wCAEzG,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;4CAC5C,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;4CACjD,MAAM,WAAW,CAAC;4CAClB,OAAO,yBACL,6LAAC;gDAAK,WAAU;gDAAqD,GAAG,KAAK;0DAC1E;;;;;uEAGH,6LAAC;gDAAK,WAAU;gDAAiF,GAAG,KAAK;0DACtG;;;;;;wCAGP;wCACA,KAAK,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACtB,6LAAC;gDAAI,WAAU;gDAAQ,GAAG,KAAK;;;;;;wCAEjC,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACxB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAM,WAAU;oDAA6C,GAAG,KAAK;;;;;;;;;;;wCAG1E,OAAO,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACxB,6LAAC;gDAAM,WAAU;gDAAc,GAAG,KAAK;;;;;;wCAEzC,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;gDAAG,WAAU;gDAA4D,GAAG,KAAK;;;;;;wCAEpF,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;gDAAG,WAAU;gDAAoC,GAAG,KAAK;;;;;;wCAE5D,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;gDAAG,WAAU;gDAAwB,GAAG,KAAK;;;;;;wCAEhD,QAAQ,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACzB,6LAAC;gDAAO,WAAU;gDAA+B,GAAG,KAAK;;;;;;wCAE3D,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,OAAO,iBACrB,6LAAC;gDAAG,WAAU;gDAAU,GAAG,KAAK;;;;;;oCAEpC;8CAEC,OAAO,eAAe,OAAO,KAAK,WAC/B,eAAe,OAAO,GACtB,OAAO,eAAe,OAAO,KAAK,YAChC,eAAe,OAAO,KAAK,QAC3B,WAAW,eAAe,OAAO,IACjC,OAAO,eAAe,OAAO,CAAC,KAAK,KAAK,WACxC,eAAe,OAAO,CAAC,KAAK,GAC5B,MAAM,OAAO,CAAC,eAAe,OAAO,IAClC,eAAe,OAAO,CACnB,GAAG,CAAC,CAAC,QAAW,MAAM,IAAI,KAAK,SAAS,MAAM,KAAK,GAAG,IACtD,IAAI,CAAC,MACR;;;;;;;;;;;0CAGZ,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;;kDAET,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;oBAO1C,eAAe,IAAI,KAAK,uBACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,KAAK,GAAG,OAAO,eAAe,OAAO,KAAK,WAAW,eAAe,OAAO,GACzE,AAAC,OAAO,eAAe,OAAO,KAAK,YAAY,eAAe,OAAO,KAAK,QAAQ,WAAW,eAAe,OAAO,IAAI,OAAO,eAAe,OAAO,CAAC,KAAK,KAAK,WAAY,eAAe,OAAO,CAAC,KAAK,GAAG,GAAG,qBAAqB,CAAC;gCACrO,WAAU;gCACV,OAAO,eAAe,KAAK;;;;;;0CAE7B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,MAAM,SAAS,OAAO,eAAe,OAAO,KAAK,WAAW,eAAe,OAAO,GAChF,AAAC,OAAO,eAAe,OAAO,KAAK,YAAY,eAAe,OAAO,KAAK,QAAQ,WAAW,eAAe,OAAO,IAAI,OAAO,eAAe,OAAO,CAAC,KAAK,KAAK,WAAY,eAAe,OAAO,CAAC,KAAK,GAAG;4CAC5M,IAAI,QAAQ,OAAO,IAAI,CAAC,QAAQ;wCAClC;;0DAEA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGlC,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,MAAM,SAAS,OAAO,eAAe,OAAO,KAAK,WAAW,eAAe,OAAO,GAChF,AAAC,OAAO,eAAe,OAAO,KAAK,YAAY,eAAe,OAAO,KAAK,QAAQ,WAAW,eAAe,OAAO,IAAI,OAAO,eAAe,OAAO,CAAC,KAAK,KAAK,WAAY,eAAe,OAAO,CAAC,KAAK,GAAG;4CAC5M,IAAI,QAAQ;gDACV,MAAM,OAAO,SAAS,aAAa,CAAC;gDACpC,KAAK,IAAI,GAAG;gDACZ,KAAK,QAAQ,GAAG,eAAe,KAAK,IAAI;gDACxC,SAAS,IAAI,CAAC,WAAW,CAAC;gDAC1B,KAAK,KAAK;gDACV,SAAS,IAAI,CAAC,WAAW,CAAC;4CAC5B;wCACF;;0DAEA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;oBAQ5C,eAAe,IAAI,KAAK,yBACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,KAAK,OAAO,eAAe,OAAO,KAAK,WAAW,eAAe,OAAO,GACtE,AAAC,OAAO,eAAe,OAAO,KAAK,YAAY,eAAe,OAAO,KAAK,QAAQ,WAAW,eAAe,OAAO,IAAI,OAAO,eAAe,OAAO,CAAC,KAAK,KAAK,WAAY,eAAe,OAAO,CAAC,KAAK,GAAG;gCAC5M,KAAK,eAAe,KAAK,IAAI;gCAC7B,WAAU;;;;;;0CAEZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,MAAM,WAAW,OAAO,eAAe,OAAO,KAAK,WAAW,eAAe,OAAO,GAClF,AAAC,OAAO,eAAe,OAAO,KAAK,YAAY,eAAe,OAAO,KAAK,QAAQ,WAAW,eAAe,OAAO,IAAI,OAAO,eAAe,OAAO,CAAC,KAAK,KAAK,WAAY,eAAe,OAAO,CAAC,KAAK,GAAG;4CAC5M,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU;wCACtC;;0DAEA,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAG3C,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,MAAM,WAAW,OAAO,eAAe,OAAO,KAAK,WAAW,eAAe,OAAO,GAClF,AAAC,OAAO,eAAe,OAAO,KAAK,YAAY,eAAe,OAAO,KAAK,QAAQ,WAAW,eAAe,OAAO,IAAI,OAAO,eAAe,OAAO,CAAC,KAAK,KAAK,WAAY,eAAe,OAAO,CAAC,KAAK,GAAG;4CAC5M,IAAI,UAAU;gDACZ,MAAM,OAAO,SAAS,aAAa,CAAC;gDACpC,KAAK,IAAI,GAAG;gDACZ,KAAK,QAAQ,GAAG,eAAe,KAAK,IAAI;gDACxC,SAAS,IAAI,CAAC,WAAW,CAAC;gDAC1B,KAAK,KAAK;gDACV,SAAS,IAAI,CAAC,WAAW,CAAC;4CAC5B;wCACF;;0DAEA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;oBAQ5C,CAAC,eAAe,IAAI,KAAK,WAAW,eAAe,IAAI,KAAK,gBAAgB,mBAC3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,CAAC;oCACA,MAAM,WAAW,OAAO,eAAe,OAAO,KAAK,WAAW,eAAe,OAAO,GACnE,AAAC,OAAO,eAAe,OAAO,KAAK,YAAY,eAAe,OAAO,KAAK,QAAQ,WAAW,eAAe,OAAO,IAAI,OAAO,eAAe,OAAO,CAAC,KAAK,KAAK,WAAY,eAAe,OAAO,CAAC,KAAK,GAAG;oCAE3N,IAAI,CAAC,UAAU;wCACb,qBACE,6LAAC;4CAAI,WAAU;sDAA2E;;;;;;oCAI9F;oCAEA,IAAI,SAAS,QAAQ,CAAC,2BAA2B,SAAS,QAAQ,CAAC,cAAc;wCAC/E,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI,SAAS,KAAK,CAAC,KAAK,GAAG;wCACnF,qBACE,6LAAC;4CACC,WAAU;4CACV,KAAK,CAAC,8BAA8B,EAAE,WAAW;4CACjD,aAAY;4CACZ,OAAM;4CACN,eAAe;4CACf,OAAO,eAAe,KAAK;;;;;;oCAGjC,OAAO,IAAI,SAAS,QAAQ,CAAC,eAAe;wCAC1C,MAAM,UAAU,SAAS,KAAK,CAAC,KAAK,GAAG;wCACvC,qBACE,6LAAC;4CACC,WAAU;4CACV,KAAK,CAAC,+BAA+B,EAAE,SAAS;4CAChD,aAAY;4CACZ,OAAM;4CACN,eAAe;4CACf,OAAO,eAAe,KAAK;;;;;;oCAGjC,OAAO;wCACL,qBACE,6LAAC;4CACC,QAAQ;4CACR,WAAU;4CACV,KAAK;4CACL,OAAO,eAAe,KAAK;sDAC5B;;;;;;oCAIL;gCACF,CAAC;;;;;;0CAEH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;wCACP,MAAM,WAAW,OAAO,eAAe,OAAO,KAAK,WAAW,eAAe,OAAO,GACnE,AAAC,OAAO,eAAe,OAAO,KAAK,YAAY,eAAe,OAAO,KAAK,QAAQ,WAAW,eAAe,OAAO,IAAI,OAAO,eAAe,OAAO,CAAC,KAAK,KAAK,WAAY,eAAe,OAAO,CAAC,KAAK,GAAG;wCAC3N,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU;oCACtC;;sDAEA,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;oBAQhD,CAAC;wBAAC;wBAAQ;wBAAS;wBAAO;wBAAkB;qBAAQ,CAAC,QAAQ,CAAC,eAAe,IAAI,mBAChF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAa/C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,CAAC,4BACA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,WAAU;;8CAEV,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;8CAAK;;;;;;;;;;;iDAGR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;;;;;;;;kCAK5C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,CAAC;gCACX,WAAU;;kDAEV,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;kDAAK;;;;;;;;;;;;0CAGR,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,CAAC;gCACX,WAAU;;kDAEV,6LAAC;kDAAK;;;;;;kDACN,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;0BAM9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;;oCAAM,KAAK,KAAK,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,SAAS,UAAU,CAAC,IAAI;oCAAK;;;;;;;;;;;;;kCAEvF,6LAAC,uIAAA,CAAA,WAAQ;wBACP,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,SAAS,UAAU,CAAC,IAAI;wBACrE,WAAU;;;;;;;;;;;;;;;;;;AAKpB;GAzqBa;KAAA", "debugId": null}}, {"offset": {"line": 2921, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/quiz-introduction.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { <PERSON><PERSON> } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { \r\n  Target, \r\n  Clock, \r\n  Award, \r\n  BookOpen, \r\n  CheckCircle,\r\n  AlertCircle,\r\n  Trophy,\r\n  FileText\r\n} from 'lucide-react';\r\nimport { Quiz } from '@/types/lms';\r\n\r\ninterface QuizIntroductionProps {\r\n  quiz: Quiz;\r\n  quizType: 'chapter' | 'module' | 'final';\r\n  onStartQuiz: () => void;\r\n  onCancel: () => void;\r\n}\r\n\r\nexport const QuizIntroduction: React.FC<QuizIntroductionProps> = ({\r\n  quiz,\r\n  quizType,\r\n  onStartQuiz,\r\n  onCancel\r\n}) => {\r\n  const getQuizTypeInfo = () => {\r\n    switch (quizType) {\r\n      case 'chapter':\r\n        return {\r\n          title: 'Chapter Quiz',\r\n          description: 'Kuis untuk menguji pemahaman Anda pada bab ini',\r\n          icon: <BookOpen className=\"h-6 w-6\" />,\r\n          color: 'bg-blue-50 text-blue-700 border-blue-200'\r\n        };\r\n      case 'module':\r\n        return {\r\n          title: 'Module Quiz',\r\n          description: 'Kuis untuk menguji pemahaman Anda pada modul ini',\r\n          icon: <Target className=\"h-6 w-6\" />,\r\n          color: 'bg-green-50 text-green-700 border-green-200'\r\n        };\r\n      case 'final':\r\n        return {\r\n          title: 'Final Exam',\r\n          description: 'Ujian akhir untuk menyelesaikan kursus ini',\r\n          icon: <Trophy className=\"h-6 w-6\" />,\r\n          color: 'bg-purple-50 text-purple-700 border-purple-200'\r\n        };\r\n    }\r\n  };\r\n\r\n  const quizInfo = getQuizTypeInfo();\r\n\r\n  return (\r\n    <div className=\"max-w-4xl mx-auto\">\r\n      <div className=\"mb-6\">\r\n        <div className=\"flex items-center gap-3 mb-4\">\r\n          {quizInfo.icon}\r\n          <div>\r\n            <h1 className=\"text-2xl font-bold text-gray-900\">\r\n              {quizInfo.title}\r\n            </h1>\r\n            <p className=\"text-gray-600\">{quizInfo.description}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n        {/* Quiz Information */}\r\n        <div className=\"lg:col-span-2\">\r\n          <Card className=\"mb-6\">\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <FileText className=\"h-5 w-5\" />\r\n                Informasi Kuis\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Clock className=\"h-4 w-4 text-gray-500\" />\r\n                  <span className=\"text-sm text-gray-600\">Durasi:</span>\r\n                  <span className=\"font-medium\">{quiz.timeLimit} menit</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Target className=\"h-4 w-4 text-gray-500\" />\r\n                  <span className=\"text-sm text-gray-600\">Skor Minimum:</span>\r\n                  <span className=\"font-medium\">{quiz.minimumScore}%</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Award className=\"h-4 w-4 text-gray-500\" />\r\n                  <span className=\"text-sm text-gray-600\">Percobaan:</span>\r\n                  <span className=\"font-medium\">\r\n                    {quiz.attempts} / {quiz.maxAttempts}\r\n                  </span>\r\n                </div>\r\n                <div className=\"flex items-center gap-2\">\r\n                  <CheckCircle className=\"h-4 w-4 text-gray-500\" />\r\n                  <span className=\"text-sm text-gray-600\">Status:</span>\r\n                  <Badge variant={quiz.isPassed ? \"default\" : \"secondary\"}>\r\n                    {quiz.isPassed ? \"Lulus\" : \"Belum Lulus\"}\r\n                  </Badge>\r\n                </div>\r\n              </div>\r\n              \r\n              {quiz.lastScore > 0 && (\r\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                  <h4 className=\"font-medium text-gray-900 mb-2\">Skor Terakhir</h4>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <span className=\"text-2xl font-bold text-blue-600\">\r\n                      {quiz.lastScore}%\r\n                    </span>\r\n                    <span className=\"text-sm text-gray-600\">\r\n                      ({quiz.attempts} percobaan)\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Quiz Instructions */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <AlertCircle className=\"h-5 w-5\" />\r\n                Petunjuk Kuis\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"space-y-3 text-sm text-gray-700\">\r\n                <div className=\"flex items-start gap-3\">\r\n                  <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5\">\r\n                    1\r\n                  </div>\r\n                  <p>Bacalah setiap pertanyaan dengan teliti sebelum menjawab.</p>\r\n                </div>\r\n                <div className=\"flex items-start gap-3\">\r\n                  <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5\">\r\n                    2\r\n                  </div>\r\n                  <p>Anda memiliki waktu {quiz.timeLimit} menit untuk menyelesaikan kuis ini.</p>\r\n                </div>\r\n                <div className=\"flex items-start gap-3\">\r\n                  <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5\">\r\n                    3\r\n                  </div>\r\n                  <p>Skor minimum untuk lulus adalah {quiz.minimumScore}%.</p>\r\n                </div>\r\n                <div className=\"flex items-start gap-3\">\r\n                  <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5\">\r\n                    4\r\n                  </div>\r\n                  <p>Anda dapat mencoba sebanyak {quiz.maxAttempts} kali.</p>\r\n                </div>\r\n                <div className=\"flex items-start gap-3\">\r\n                  <div className=\"w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium flex-shrink-0 mt-0.5\">\r\n                    5\r\n                  </div>\r\n                  <p>Pastikan koneksi internet Anda stabil selama mengerjakan kuis.</p>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Action Panel */}\r\n        <div className=\"lg:col-span-1\">\r\n          <Card className={`${quizInfo.color} border-2`}>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                {quizInfo.icon}\r\n                Siap Memulai?\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <p className=\"text-sm\">\r\n                Pastikan Anda sudah mempersiapkan diri dengan baik sebelum memulai kuis.\r\n              </p>\r\n              \r\n              <div className=\"space-y-2\">\r\n                <Button \r\n                  onClick={onStartQuiz}\r\n                  className=\"w-full\"\r\n                  size=\"lg\"\r\n                >\r\n                  <Target className=\"h-4 w-4 mr-2\" />\r\n                  Mulai Kuis\r\n                </Button>\r\n                \r\n                <Button \r\n                  variant=\"outline\" \r\n                  onClick={onCancel}\r\n                  className=\"w-full\"\r\n                >\r\n                  Kembali\r\n                </Button>\r\n              </div>\r\n\r\n              {quiz.attempts > 0 && (\r\n                <div className=\"bg-white/50 p-3 rounded-lg\">\r\n                  <p className=\"text-xs text-gray-600 mb-1\">Percobaan sebelumnya:</p>\r\n                  <p className=\"text-sm font-medium\">\r\n                    Skor: {quiz.lastScore}% \r\n                    {quiz.isPassed ? (\r\n                      <span className=\"text-green-600 ml-2\">✓ Lulus</span>\r\n                    ) : (\r\n                      <span className=\"text-red-600 ml-2\">✗ Belum Lulus</span>\r\n                    )}\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAmBO,MAAM,mBAAoD,CAAC,EAChE,IAAI,EACJ,QAAQ,EACR,WAAW,EACX,QAAQ,EACT;IACC,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAC1B,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,oBAAM,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACxB,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;oBACP,aAAa;oBACb,oBAAM,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACxB,OAAO;gBACT;QACJ;IACF;IAEA,MAAM,WAAW;IAEjB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,SAAS,IAAI;sCACd,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CACX,SAAS,KAAK;;;;;;8CAEjB,6LAAC;oCAAE,WAAU;8CAAiB,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;0BAKxD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAIpC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;gEAAK,WAAU;;oEAAe,KAAK,SAAS;oEAAC;;;;;;;;;;;;;kEAEhD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;gEAAK,WAAU;;oEAAe,KAAK,YAAY;oEAAC;;;;;;;;;;;;;kEAEnD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC;gEAAK,WAAU;;oEACb,KAAK,QAAQ;oEAAC;oEAAI,KAAK,WAAW;;;;;;;;;;;;;kEAGvC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAS,KAAK,QAAQ,GAAG,YAAY;0EACzC,KAAK,QAAQ,GAAG,UAAU;;;;;;;;;;;;;;;;;;4CAKhC,KAAK,SAAS,GAAG,mBAChB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEACb,KAAK,SAAS;oEAAC;;;;;;;0EAElB,6LAAC;gEAAK,WAAU;;oEAAwB;oEACpC,KAAK,QAAQ;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS5B,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAIvC,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA2H;;;;;;sEAG1I,6LAAC;sEAAE;;;;;;;;;;;;8DAEL,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA2H;;;;;;sEAG1I,6LAAC;;gEAAE;gEAAqB,KAAK,SAAS;gEAAC;;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA2H;;;;;;sEAG1I,6LAAC;;gEAAE;gEAAiC,KAAK,YAAY;gEAAC;;;;;;;;;;;;;8DAExD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA2H;;;;;;sEAG1I,6LAAC;;gEAAE;gEAA6B,KAAK,WAAW;gEAAC;;;;;;;;;;;;;8DAEnD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA2H;;;;;;sEAG1I,6LAAC;sEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAW,GAAG,SAAS,KAAK,CAAC,SAAS,CAAC;;8CAC3C,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;4CAClB,SAAS,IAAI;4CAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAE,WAAU;sDAAU;;;;;;sDAIvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,WAAU;oDACV,MAAK;;sEAEL,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAIrC,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;wCAKF,KAAK,QAAQ,GAAG,mBACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,6LAAC;oDAAE,WAAU;;wDAAsB;wDAC1B,KAAK,SAAS;wDAAC;wDACrB,KAAK,QAAQ,iBACZ,6LAAC;4DAAK,WAAU;sEAAsB;;;;;iFAEtC,6LAAC;4DAAK,WAAU;sEAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1D;KAvMa", "debugId": null}}, {"offset": {"line": 3614, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/quiz-card.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Target, BookMarked, Trophy, Clock, CheckCircle } from 'lucide-react';\r\nimport { QuizCardProps } from '@/types/lms';\r\n\r\nexport const QuizCard: React.FC<QuizCardProps> = ({\r\n  quiz,\r\n  isUnlocked,\r\n  onStartQuiz\r\n}) => {\r\n  const getQuizTypeColor = () => {\r\n    switch (quiz.type) {\r\n      case 'chapter':\r\n        return 'bg-blue-100 text-blue-700 border-blue-200';\r\n      case 'module':\r\n        return 'bg-purple-100 text-purple-700 border-purple-200';\r\n      case 'final':\r\n        return 'bg-red-100 text-red-700 border-red-200';\r\n      default:\r\n        return 'bg-gray-100 text-gray-700 border-gray-200';\r\n    }\r\n  };\r\n\r\n  const getQuizIcon = () => {\r\n    switch (quiz.type) {\r\n      case 'chapter':\r\n        return <Target className='h-4 w-4' />;\r\n      case 'module':\r\n        return <BookMarked className='h-4 w-4' />;\r\n      case 'final':\r\n        return <Trophy className='h-4 w-4' />;\r\n      default:\r\n        return <Target className='h-4 w-4' />;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card className={`my-3 ml-6 border-2 ${!isUnlocked ? 'opacity-50' : ''}`}>\r\n      <CardContent className='p-4'>\r\n        <div className='flex items-center justify-between'>\r\n          <div className='flex items-center space-x-3'>\r\n            <div className={`rounded-lg p-2 ${getQuizTypeColor()}`}>\r\n              {getQuizIcon()}\r\n            </div>\r\n            <div>\r\n              <h4 className='font-medium'>{quiz.title}</h4>\r\n              <div className='mt-1 flex items-center space-x-3'>\r\n                <span className='text-sm text-gray-600'>\r\n                  Min. Score: {quiz.minimumScore}%\r\n                </span>\r\n                {quiz.timeLimit && (\r\n                  <span className='text-sm text-gray-600'>\r\n                    <Clock className='mr-1 inline h-3 w-3' />\r\n                    {quiz.timeLimit} min\r\n                  </span>\r\n                )}\r\n                <span className='text-sm text-gray-600'>\r\n                  Attempts: {quiz.attempts}/{quiz.maxAttempts}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className='flex items-center space-x-2'>\r\n            {quiz.isPassed && (\r\n              <Badge className='bg-green-100 text-green-700'>\r\n                <CheckCircle className='mr-1 h-3 w-3' />\r\n                Passed ({quiz.lastScore}%)\r\n              </Badge>\r\n            )}\r\n            <Button\r\n              size='sm'\r\n              disabled={!isUnlocked || quiz.attempts >= quiz.maxAttempts}\r\n              onClick={onStartQuiz}\r\n              variant={quiz.isPassed ? 'outline' : 'default'}\r\n            >\r\n              {quiz.attempts === 0\r\n                ? 'Start Quiz'\r\n                : quiz.isPassed\r\n                  ? 'Retake'\r\n                  : 'Continue'}\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;AAGO,MAAM,WAAoC,CAAC,EAChD,IAAI,EACJ,UAAU,EACV,WAAW,EACZ;IACC,MAAM,mBAAmB;QACvB,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc;QAClB,OAAQ,KAAK,IAAI;YACf,KAAK;gBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B;gBACE,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC,aAAa,eAAe,IAAI;kBACtE,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,CAAC,eAAe,EAAE,oBAAoB;0CACnD;;;;;;0CAEH,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAe,KAAK,KAAK;;;;;;kDACvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;oDAAwB;oDACzB,KAAK,YAAY;oDAAC;;;;;;;4CAEhC,KAAK,SAAS,kBACb,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAChB,KAAK,SAAS;oDAAC;;;;;;;0DAGpB,6LAAC;gDAAK,WAAU;;oDAAwB;oDAC3B,KAAK,QAAQ;oDAAC;oDAAE,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;kCAKnD,6LAAC;wBAAI,WAAU;;4BACZ,KAAK,QAAQ,kBACZ,6LAAC,oIAAA,CAAA,QAAK;gCAAC,WAAU;;kDACf,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAiB;oCAC/B,KAAK,SAAS;oCAAC;;;;;;;0CAG5B,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAU,CAAC,cAAc,KAAK,QAAQ,IAAI,KAAK,WAAW;gCAC1D,SAAS;gCACT,SAAS,KAAK,QAAQ,GAAG,YAAY;0CAEpC,KAAK,QAAQ,KAAK,IACf,eACA,KAAK,QAAQ,GACX,WACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;KAjFa", "debugId": null}}, {"offset": {"line": 3840, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\r\nimport { XIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot='dialog' {...props} />;\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot='dialog-trigger' {...props} />;\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot='dialog-portal' {...props} />;\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot='dialog-close' {...props} />;\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot='dialog-overlay'\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot='dialog-portal'>\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot='dialog-content'\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className='sr-only'>Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  );\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='dialog-header'\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='dialog-footer'\r\n      className={cn(\r\n        'flex flex-col-reverse gap-2 sm:flex-row sm:justify-end',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot='dialog-title'\r\n      className={cn('text-lg leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot='dialog-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 4037, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/quiz-modal.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  <PERSON><PERSON>Header,\r\n  DialogTitle\r\n} from '@/components/ui/dialog';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Clock } from 'lucide-react';\r\nimport { QuizModalProps } from '@/types/lms';\r\n\r\nexport const QuizModal: React.FC<QuizModalProps> = ({\r\n  quiz,\r\n  isOpen,\r\n  onClose,\r\n  onComplete\r\n}) => {\r\n  const [currentQuestion, setCurrentQuestion] = useState(0);\r\n  const [answers, setAnswers] = useState<{ [key: string]: any }>({});\r\n  const [timeLeft, setTimeLeft] = useState(\r\n    quiz.timeLimit ? quiz.timeLimit * 60 : null\r\n  );\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  React.useEffect(() => {\r\n    if (isOpen && timeLeft !== null && timeLeft > 0) {\r\n      const timer = setInterval(() => {\r\n        setTimeLeft((prev) => {\r\n          if (prev === null || prev <= 1) {\r\n            handleSubmitQuiz();\r\n            return 0;\r\n          }\r\n          return prev - 1;\r\n        });\r\n      }, 1000);\r\n      return () => clearInterval(timer);\r\n    }\r\n  }, [isOpen, timeLeft]);\r\n\r\n  const formatTime = (seconds: number) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  const handleAnswerChange = (questionId: string, answer: any) => {\r\n    setAnswers((prev) => ({ ...prev, [questionId]: answer }));\r\n  };\r\n\r\n  const handleSubmitQuiz = () => {\r\n    if (isSubmitting) return;\r\n    setIsSubmitting(true);\r\n\r\n    // Calculate score\r\n    let correctAnswers = 0;\r\n    quiz.questions.forEach((question) => {\r\n      const userAnswer = answers[question.id];\r\n\r\n      if (question.type === 'multiple_choice' || question.type === 'multiple-choice') {\r\n        // Check if the selected index matches the correct answer\r\n        if (userAnswer === question.correctAnswer) {\r\n          correctAnswers++;\r\n        }\r\n        // Also check for the complex format with isCorrect\r\n        else {\r\n          const selectedOption = question.options?.[userAnswer as number];\r\n          if (selectedOption && typeof selectedOption === 'object' && selectedOption.isCorrect) {\r\n            correctAnswers++;\r\n          }\r\n        }\r\n      } else if (question.type === 'true_false' || question.type === 'true-false') {\r\n        // Compare with correctAnswer for true-false questions\r\n        if (userAnswer === question.correctAnswer) {\r\n          correctAnswers++;\r\n        }\r\n      } else if (question.type === 'essay') {\r\n        // For essay, simply check if an answer was provided.\r\n        // A more complex grading logic would be needed for actual correctness.\r\n        if (userAnswer && userAnswer.trim() !== '') {\r\n          correctAnswers++;\r\n        }\r\n      }\r\n    });\r\n\r\n    const score = Math.round((correctAnswers / quiz.questions.length) * 100);\r\n    setTimeout(() => {\r\n      onComplete(score);\r\n      setIsSubmitting(false);\r\n      setAnswers({});\r\n      setCurrentQuestion(0);\r\n      if (quiz.timeLimit) setTimeLeft(quiz.timeLimit * 60);\r\n    }, 1000);\r\n  };\r\n\r\n  if (!isOpen || quiz.questions.length === 0) return null;\r\n\r\n  const currentQ = quiz.questions[currentQuestion];\r\n  const isLastQuestion = currentQuestion === quiz.questions.length - 1;\r\n  const canProceed = answers[currentQ.id] !== undefined;\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onClose}>\r\n      <DialogContent className='max-h-[90vh] max-w-4xl overflow-y-auto p-6'>\r\n        <DialogHeader>\r\n          <DialogTitle className='flex items-center justify-between'>\r\n            <span>{quiz.title}</span>\r\n            {timeLeft !== null && (\r\n              <Badge variant='outline' className='border-red-200 text-red-600'>\r\n                <Clock className='mr-1 h-4 w-4' />\r\n                {formatTime(timeLeft)}\r\n              </Badge>\r\n            )}\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className='space-y-6'>\r\n          {/* Progress Bar */}\r\n          <div className='space-y-2'>\r\n            <div className='flex justify-between text-sm text-gray-600'>\r\n              <span>\r\n                Question {currentQuestion + 1} of {quiz.questions.length}\r\n              </span>\r\n              <span>\r\n                {Math.round(\r\n                  ((currentQuestion + 1) / quiz.questions.length) * 100\r\n                )}\r\n                % Complete\r\n              </span>\r\n            </div>\r\n            <Progress\r\n              value={((currentQuestion + 1) / quiz.questions.length) * 100}\r\n            />\r\n          </div>\r\n\r\n          {/* Question */}\r\n          <Card>\r\n            <CardContent className='p-6'>\r\n              <h3 className='mb-4 text-lg font-medium'>\r\n                {typeof currentQ.question === 'string' ? (\r\n                  currentQ.question\r\n                ) : Array.isArray(currentQ.question) ? (\r\n                  currentQ.question.map((block, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {block.type === 'text' && <span>{block.value}</span>}\r\n                      {block.type === 'image' && block.value && (\r\n                        <img src={block.value} alt={`Question image ${index}`} className=\"inline-block max-h-16 object-contain ml-2\" />\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))\r\n                ) : (\r\n                  <span>{String(currentQ.question)}</span>\r\n                )}\r\n              </h3>\r\n\r\n              {(currentQ.type === 'multiple_choice' || currentQ.type === 'multiple-choice') && currentQ.options && (\r\n                <div className='space-y-3'>\r\n                  {currentQ.options.map((option, index) => (\r\n                    <label\r\n                      key={index}\r\n                      className='flex cursor-pointer items-center space-x-3'\r\n                    >\r\n                      <input\r\n                        type='radio'\r\n                        name={currentQ.id}\r\n                        value={index}\r\n                        checked={answers[currentQ.id] === index}\r\n                        onChange={() => handleAnswerChange(currentQ.id, index)}\r\n                        className='h-4 w-4 text-blue-600'\r\n                      />\r\n                      <span>\r\n                        {typeof option === 'string' ? (\r\n                          option\r\n                        ) : Array.isArray(option.content) ? (\r\n                          option.content.map((block, optionBlockIndex) => (\r\n                            <React.Fragment key={optionBlockIndex}>\r\n                              {block.type === 'text' && <span>{block.value}</span>}\r\n                              {block.type === 'image' && block.value && (\r\n                                <img src={block.value} alt={`Option image ${optionBlockIndex}`} className=\"inline-block max-h-8 object-contain ml-1\" />\r\n                              )}\r\n                            </React.Fragment>\r\n                          ))\r\n                        ) : (\r\n                          <span>{String(option.content || option)}</span>\r\n                        )}\r\n                      </span>\r\n                    </label>\r\n                  ))}\r\n                </div>\r\n              )}\r\n\r\n              {(currentQ.type === 'true_false' || currentQ.type === 'true-false') && (\r\n                <div className='space-y-3'>\r\n                  <label className='flex cursor-pointer items-center space-x-3'>\r\n                    <input\r\n                      type='radio'\r\n                      name={currentQ.id}\r\n                      value='true'\r\n                      checked={answers[currentQ.id] === 'true'}\r\n                      onChange={() => handleAnswerChange(currentQ.id, 'true')}\r\n                      className='h-4 w-4 text-blue-600'\r\n                    />\r\n                    <span>True</span>\r\n                  </label>\r\n                  <label className='flex cursor-pointer items-center space-x-3'>\r\n                    <input\r\n                      type='radio'\r\n                      name={currentQ.id}\r\n                      value='false'\r\n                      checked={answers[currentQ.id] === 'false'}\r\n                      onChange={() => handleAnswerChange(currentQ.id, 'false')}\r\n                      className='h-4 w-4 text-blue-600'\r\n                    />\r\n                    <span>False</span>\r\n                  </label>\r\n                </div>\r\n              )}\r\n\r\n              {currentQ.type === 'essay' && (\r\n                <textarea\r\n                  className='w-full resize-none rounded-lg border p-3 focus:border-transparent focus:ring-2 focus:ring-blue-500'\r\n                  rows={6}\r\n                  placeholder='Type your answer here...'\r\n                  value={answers[currentQ.id] || ''}\r\n                  onChange={(e) =>\r\n                    handleAnswerChange(currentQ.id, e.target.value)\r\n                  }\r\n                />\r\n              )}\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Navigation */}\r\n          <div className='flex justify-between'>\r\n            <Button\r\n              variant='outline'\r\n              onClick={() =>\r\n                setCurrentQuestion((prev) => Math.max(0, prev - 1))\r\n              }\r\n              disabled={currentQuestion === 0 || isSubmitting}\r\n            >\r\n              Previous\r\n            </Button>\r\n\r\n            <div className='flex space-x-2'>\r\n              {!isLastQuestion ? (\r\n                <Button\r\n                  onClick={() => setCurrentQuestion((prev) => prev + 1)}\r\n                  disabled={!canProceed || isSubmitting}\r\n                >\r\n                  Next\r\n                </Button>\r\n              ) : (\r\n                <Button\r\n                  onClick={handleSubmitQuiz}\r\n                  disabled={!canProceed || isSubmitting}\r\n                  className='bg-green-600 hover:bg-green-700'\r\n                >\r\n                  {isSubmitting ? 'Submitting...' : 'Submit Quiz'}\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAMA;AACA;AACA;AACA;AACA;;;;;;;;;;AAGO,MAAM,YAAsC,CAAC,EAClD,IAAI,EACJ,MAAM,EACN,OAAO,EACP,UAAU,EACX;;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACrC,KAAK,SAAS,GAAG,KAAK,SAAS,GAAG,KAAK;IAEzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,6JAAA,CAAA,UAAK,CAAC,SAAS;+BAAC;YACd,IAAI,UAAU,aAAa,QAAQ,WAAW,GAAG;gBAC/C,MAAM,QAAQ;iDAAY;wBACxB;yDAAY,CAAC;gCACX,IAAI,SAAS,QAAQ,QAAQ,GAAG;oCAC9B;oCACA,OAAO;gCACT;gCACA,OAAO,OAAO;4BAChB;;oBACF;gDAAG;gBACH;2CAAO,IAAM,cAAc;;YAC7B;QACF;8BAAG;QAAC;QAAQ;KAAS;IAErB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACtD;IAEA,MAAM,qBAAqB,CAAC,YAAoB;QAC9C,WAAW,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,WAAW,EAAE;YAAO,CAAC;IACzD;IAEA,MAAM,mBAAmB;QACvB,IAAI,cAAc;QAClB,gBAAgB;QAEhB,kBAAkB;QAClB,IAAI,iBAAiB;QACrB,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC;YACtB,MAAM,aAAa,OAAO,CAAC,SAAS,EAAE,CAAC;YAEvC,IAAI,SAAS,IAAI,KAAK,qBAAqB,SAAS,IAAI,KAAK,mBAAmB;gBAC9E,yDAAyD;gBACzD,IAAI,eAAe,SAAS,aAAa,EAAE;oBACzC;gBACF,OAEK;oBACH,MAAM,iBAAiB,SAAS,OAAO,EAAE,CAAC,WAAqB;oBAC/D,IAAI,kBAAkB,OAAO,mBAAmB,YAAY,eAAe,SAAS,EAAE;wBACpF;oBACF;gBACF;YACF,OAAO,IAAI,SAAS,IAAI,KAAK,gBAAgB,SAAS,IAAI,KAAK,cAAc;gBAC3E,sDAAsD;gBACtD,IAAI,eAAe,SAAS,aAAa,EAAE;oBACzC;gBACF;YACF,OAAO,IAAI,SAAS,IAAI,KAAK,SAAS;gBACpC,qDAAqD;gBACrD,uEAAuE;gBACvE,IAAI,cAAc,WAAW,IAAI,OAAO,IAAI;oBAC1C;gBACF;YACF;QACF;QAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,iBAAiB,KAAK,SAAS,CAAC,MAAM,GAAI;QACpE,WAAW;YACT,WAAW;YACX,gBAAgB;YAChB,WAAW,CAAC;YACZ,mBAAmB;YACnB,IAAI,KAAK,SAAS,EAAE,YAAY,KAAK,SAAS,GAAG;QACnD,GAAG;IACL;IAEA,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,MAAM,KAAK,GAAG,OAAO;IAEnD,MAAM,WAAW,KAAK,SAAS,CAAC,gBAAgB;IAChD,MAAM,iBAAiB,oBAAoB,KAAK,SAAS,CAAC,MAAM,GAAG;IACnE,MAAM,aAAa,OAAO,CAAC,SAAS,EAAE,CAAC,KAAK;IAE5C,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;0CAAM,KAAK,KAAK;;;;;;4BAChB,aAAa,sBACZ,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;kDACjC,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAChB,WAAW;;;;;;;;;;;;;;;;;;8BAMpB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;gDAAK;gDACM,kBAAkB;gDAAE;gDAAK,KAAK,SAAS,CAAC,MAAM;;;;;;;sDAE1D,6LAAC;;gDACE,KAAK,KAAK,CACT,AAAC,CAAC,kBAAkB,CAAC,IAAI,KAAK,SAAS,CAAC,MAAM,GAAI;gDAClD;;;;;;;;;;;;;8CAIN,6LAAC,uIAAA,CAAA,WAAQ;oCACP,OAAO,AAAC,CAAC,kBAAkB,CAAC,IAAI,KAAK,SAAS,CAAC,MAAM,GAAI;;;;;;;;;;;;sCAK7D,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAG,WAAU;kDACX,OAAO,SAAS,QAAQ,KAAK,WAC5B,SAAS,QAAQ,GACf,MAAM,OAAO,CAAC,SAAS,QAAQ,IACjC,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,MAAM,IAAI,KAAK,wBAAU,6LAAC;kEAAM,MAAM,KAAK;;;;;;oDAC3C,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK,kBACpC,6LAAC;wDAAI,KAAK,MAAM,KAAK;wDAAE,KAAK,CAAC,eAAe,EAAE,OAAO;wDAAE,WAAU;;;;;;;+CAHhD;;;;sEAQvB,6LAAC;sDAAM,OAAO,SAAS,QAAQ;;;;;;;;;;;oCAIlC,CAAC,SAAS,IAAI,KAAK,qBAAqB,SAAS,IAAI,KAAK,iBAAiB,KAAK,SAAS,OAAO,kBAC/F,6LAAC;wCAAI,WAAU;kDACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7B,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC;wDACC,MAAK;wDACL,MAAM,SAAS,EAAE;wDACjB,OAAO;wDACP,SAAS,OAAO,CAAC,SAAS,EAAE,CAAC,KAAK;wDAClC,UAAU,IAAM,mBAAmB,SAAS,EAAE,EAAE;wDAChD,WAAU;;;;;;kEAEZ,6LAAC;kEACE,OAAO,WAAW,WACjB,SACE,MAAM,OAAO,CAAC,OAAO,OAAO,IAC9B,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,iCACzB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;oEACZ,MAAM,IAAI,KAAK,wBAAU,6LAAC;kFAAM,MAAM,KAAK;;;;;;oEAC3C,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK,kBACpC,6LAAC;wEAAI,KAAK,MAAM,KAAK;wEAAE,KAAK,CAAC,aAAa,EAAE,kBAAkB;wEAAE,WAAU;;;;;;;+DAHzD;;;;sFAQvB,6LAAC;sEAAM,OAAO,OAAO,OAAO,IAAI;;;;;;;;;;;;+CAxB/B;;;;;;;;;;oCAgCZ,CAAC,SAAS,IAAI,KAAK,gBAAgB,SAAS,IAAI,KAAK,YAAY,mBAChE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,MAAM,SAAS,EAAE;wDACjB,OAAM;wDACN,SAAS,OAAO,CAAC,SAAS,EAAE,CAAC,KAAK;wDAClC,UAAU,IAAM,mBAAmB,SAAS,EAAE,EAAE;wDAChD,WAAU;;;;;;kEAEZ,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDACC,MAAK;wDACL,MAAM,SAAS,EAAE;wDACjB,OAAM;wDACN,SAAS,OAAO,CAAC,SAAS,EAAE,CAAC,KAAK;wDAClC,UAAU,IAAM,mBAAmB,SAAS,EAAE,EAAE;wDAChD,WAAU;;;;;;kEAEZ,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;oCAKX,SAAS,IAAI,KAAK,yBACjB,6LAAC;wCACC,WAAU;wCACV,MAAM;wCACN,aAAY;wCACZ,OAAO,OAAO,CAAC,SAAS,EAAE,CAAC,IAAI;wCAC/B,UAAU,CAAC,IACT,mBAAmB,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;sCAQxD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IACP,mBAAmB,CAAC,OAAS,KAAK,GAAG,CAAC,GAAG,OAAO;oCAElD,UAAU,oBAAoB,KAAK;8CACpC;;;;;;8CAID,6LAAC;oCAAI,WAAU;8CACZ,CAAC,+BACA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,mBAAmB,CAAC,OAAS,OAAO;wCACnD,UAAU,CAAC,cAAc;kDAC1B;;;;;6DAID,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,CAAC,cAAc;wCACzB,WAAU;kDAET,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpD;GA/Pa;KAAA", "debugId": null}}, {"offset": {"line": 4505, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/chapter-section.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Book, Lock, ChevronUp, ChevronDown } from 'lucide-react';\r\nimport { ChapterSectionProps } from '@/types/lms';\r\nimport { ContentItem } from './content-item';\r\nimport { QuizCard } from './quiz-card';\r\n\r\nexport const ChapterSection: React.FC<ChapterSectionProps> = ({\r\n  chapter,\r\n  expandedContents,\r\n  onToggleContent,\r\n  onToggleContentComplete,\r\n  onStartQuiz,\r\n  isExpanded,\r\n  onToggleExpanded\r\n}) => {\r\n  const completedContents = chapter.contents.filter(\r\n    (c) => c.isCompleted\r\n  ).length;\r\n  const totalContents = chapter.contents.length;\r\n  const progress =\r\n    totalContents > 0 ? (completedContents / totalContents) * 100 : 0;\r\n\r\n  return (\r\n    <Card\r\n      id={`chapter-${chapter.id}`}\r\n      className={`my-3 border-l-4 scroll-mt-20 ${chapter.isUnlocked ? 'border-l-green-400' : 'border-l-gray-300'} ${!chapter.isUnlocked ? 'opacity-60' : ''}`}\r\n    >\r\n      <CardContent className='p-4'>\r\n        <div className='flex flex-col'>\r\n          <div\r\n            className='flex cursor-pointer items-center justify-between'\r\n            onClick={() => chapter.isUnlocked && onToggleExpanded()}\r\n          >\r\n            <div className='flex flex-1 items-center space-x-3'>\r\n              <div\r\n                className={`rounded-lg p-2 ${chapter.isUnlocked ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-500'}`}\r\n              >\r\n                <Book className='h-5 w-5' />\r\n              </div>\r\n              <div className='flex-1'>\r\n                <div className='flex items-center space-x-2'>\r\n                  <span className='font-medium'>{chapter.title}</span>\r\n                  {!chapter.isUnlocked && (\r\n                    <Lock className='h-4 w-4 text-gray-400' />\r\n                  )}\r\n                </div>\r\n                <div className='mt-2 max-w-md'>\r\n                  <Progress value={progress} className='h-2' />\r\n                </div>\r\n                <p className='mt-1 text-sm text-gray-500'>\r\n                  {completedContents}/{totalContents} contents completed\r\n                  {chapter.quiz.isPassed && ' • Quiz passed'}\r\n                </p>\r\n              </div>\r\n            </div>\r\n            {chapter.isUnlocked &&\r\n              (isExpanded ? (\r\n                <ChevronUp className='h-5 w-5 text-gray-400' />\r\n              ) : (\r\n                <ChevronDown className='h-5 w-5 text-gray-400' />\r\n              ))}\r\n          </div>\r\n\r\n          {isExpanded && chapter.isUnlocked && (\r\n            <div className='mt-4 border-t pt-4'>\r\n              {/* Chapter Contents */}\r\n              <div className='space-y-2'>\r\n                {chapter.contents.map((content) => (\r\n                  <ContentItem\r\n                    key={content.id}\r\n                    content={content}\r\n                    onToggleComplete={() => onToggleContentComplete(content.id)}\r\n                    isExpanded={expandedContents[content.id] || false}\r\n                    onToggleExpand={() => onToggleContent(content.id)}\r\n                  />\r\n                ))}\r\n              </div>\r\n\r\n              {/* Chapter Quiz */}\r\n              <QuizCard\r\n                quiz={chapter.quiz}\r\n                isUnlocked={completedContents === totalContents}\r\n                onStartQuiz={() => onStartQuiz(chapter.quiz.id)}\r\n              />\r\n            </div>\r\n          )}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;;;;;;;AAEO,MAAM,iBAAgD,CAAC,EAC5D,OAAO,EACP,gBAAgB,EAChB,eAAe,EACf,uBAAuB,EACvB,WAAW,EACX,UAAU,EACV,gBAAgB,EACjB;IACC,MAAM,oBAAoB,QAAQ,QAAQ,CAAC,MAAM,CAC/C,CAAC,IAAM,EAAE,WAAW,EACpB,MAAM;IACR,MAAM,gBAAgB,QAAQ,QAAQ,CAAC,MAAM;IAC7C,MAAM,WACJ,gBAAgB,IAAI,AAAC,oBAAoB,gBAAiB,MAAM;IAElE,qBACE,6LAAC,mIAAA,CAAA,OAAI;QACH,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;QAC3B,WAAW,CAAC,6BAA6B,EAAE,QAAQ,UAAU,GAAG,uBAAuB,oBAAoB,CAAC,EAAE,CAAC,QAAQ,UAAU,GAAG,eAAe,IAAI;kBAEvJ,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,QAAQ,UAAU,IAAI;;0CAErC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAW,CAAC,eAAe,EAAE,QAAQ,UAAU,GAAG,8BAA8B,6BAA6B;kDAE7G,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAe,QAAQ,KAAK;;;;;;oDAC3C,CAAC,QAAQ,UAAU,kBAClB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;0DAGpB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,OAAO;oDAAU,WAAU;;;;;;;;;;;0DAEvC,6LAAC;gDAAE,WAAU;;oDACV;oDAAkB;oDAAE;oDAAc;oDAClC,QAAQ,IAAI,CAAC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;4BAI/B,QAAQ,UAAU,IACjB,CAAC,2BACC,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;qDAErB,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;oCACxB;;;;;;;oBAGJ,cAAc,QAAQ,UAAU,kBAC/B,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,6LAAC,+IAAA,CAAA,cAAW;wCAEV,SAAS;wCACT,kBAAkB,IAAM,wBAAwB,QAAQ,EAAE;wCAC1D,YAAY,gBAAgB,CAAC,QAAQ,EAAE,CAAC,IAAI;wCAC5C,gBAAgB,IAAM,gBAAgB,QAAQ,EAAE;uCAJ3C,QAAQ,EAAE;;;;;;;;;;0CAUrB,6LAAC,4IAAA,CAAA,WAAQ;gCACP,MAAM,QAAQ,IAAI;gCAClB,YAAY,sBAAsB;gCAClC,aAAa,IAAM,YAAY,QAAQ,IAAI,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9D;KApFa", "debugId": null}}, {"offset": {"line": 4707, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/module-section.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport {\r\n  BookOpen,\r\n  Lock,\r\n  ChevronUp,\r\n  ChevronDown,\r\n  Maximize,\r\n  Minimize\r\n} from 'lucide-react';\r\nimport { ModuleSectionProps } from '@/types/lms';\r\nimport { ChapterSection } from './chapter-section';\r\nimport { QuizCard } from './quiz-card';\r\n\r\nexport const ModuleSection: React.FC<ModuleSectionProps> = ({\r\n  module,\r\n  expandedContents,\r\n  expandedChapters,\r\n  onToggleContent,\r\n  onToggleContentComplete,\r\n  onStartQuiz,\r\n  isExpanded,\r\n  onToggleExpanded,\r\n  onToggleChapter,\r\n  onExpandAllChapters,\r\n  onCollapseAllChapters\r\n}) => {\r\n  const totalChapters = module.chapters.length;\r\n  const completedChapters = module.chapters.filter(\r\n    (ch) => ch.contents.every((c) => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)\r\n  ).length;\r\n  const progress =\r\n    totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0;\r\n\r\n  // Only check unlocked chapters for expansion state\r\n  const unlockedChapters = module.chapters.filter(ch => ch.isUnlocked);\r\n  const allChaptersExpanded = unlockedChapters.length > 0 && unlockedChapters.every(\r\n    (ch) => expandedChapters[ch.id] === true\r\n  );\r\n\r\n\r\n  return (\r\n    <Card\r\n      id={`module-${module.id}`}\r\n      className={`mb-6 shadow-md scroll-mt-20 ${module.isUnlocked ? 'border-green-200' : 'border-gray-200'}`}\r\n    >\r\n      <CardHeader\r\n        className={`border-b ${!module.isUnlocked ? 'opacity-60' : ''}`}\r\n      >\r\n        <div\r\n          className='flex cursor-pointer items-center justify-between'\r\n          onClick={() => module.isUnlocked && onToggleExpanded()}\r\n        >\r\n          <div className='flex flex-1 items-center space-x-4'>\r\n            <div\r\n              className={`rounded-lg p-3 ${module.isUnlocked ? 'bg-purple-100 text-purple-700' : 'bg-gray-100 text-gray-500'}`}\r\n            >\r\n              <BookOpen className='h-6 w-6' />\r\n            </div>\r\n            <div className='flex-1'>\r\n              <div className='flex items-center space-x-2'>\r\n                <CardTitle className='text-lg'>{module.title}</CardTitle>\r\n                {!module.isUnlocked && (\r\n                  <Lock className='h-4 w-4 text-gray-400' />\r\n                )}\r\n              </div>\r\n              <p className='mt-1 text-gray-600'>{module.description}</p>\r\n              <div className='mt-3 max-w-md'>\r\n                <Progress value={progress} className='h-3' />\r\n              </div>\r\n              <p className='mt-2 text-sm text-gray-500'>\r\n                {completedChapters}/{totalChapters} chapters completed\r\n                {module.moduleQuiz.isPassed && ' • Module quiz passed'}\r\n              </p>\r\n            </div>\r\n          </div>\r\n          {module.isUnlocked &&\r\n            (isExpanded ? (\r\n              <ChevronUp className='h-6 w-6 text-gray-400' />\r\n            ) : (\r\n              <ChevronDown className='h-6 w-6 text-gray-400' />\r\n            ))}\r\n        </div>\r\n      </CardHeader>\r\n\r\n      {isExpanded && module.isUnlocked && (\r\n        <CardContent className='pt-6'>\r\n          {/* Chapter Expand/Collapse Controls */}\r\n\r\n          {/* Module Chapters */}\r\n          <div className='space-y-2'>\r\n            {module.chapters.map((chapter) => (\r\n              <ChapterSection\r\n                key={chapter.id}\r\n                chapter={chapter}\r\n                expandedContents={expandedContents}\r\n                onToggleContent={onToggleContent}\r\n                onToggleContentComplete={onToggleContentComplete}\r\n                onStartQuiz={onStartQuiz}\r\n                isExpanded={expandedChapters[chapter.id] || false}\r\n                onToggleExpanded={() => onToggleChapter(chapter.id)}\r\n              />\r\n            ))}\r\n          </div>\r\n\r\n          {/* Module Quiz */}\r\n          <div className='mt-6 border-t pt-4'>\r\n            <h4 className='mb-3 font-medium text-purple-700'>\r\n              Module Assessment\r\n            </h4>\r\n            <QuizCard\r\n              quiz={module.moduleQuiz}\r\n              isUnlocked={completedChapters === totalChapters}\r\n              onStartQuiz={() => onStartQuiz(module.moduleQuiz.id)}\r\n            />\r\n          </div>\r\n        </CardContent>\r\n      )}\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AASA;AACA;;;;;;;AAEO,MAAM,gBAA8C,CAAC,EAC1D,MAAM,EACN,gBAAgB,EAChB,gBAAgB,EAChB,eAAe,EACf,uBAAuB,EACvB,WAAW,EACX,UAAU,EACV,gBAAgB,EAChB,eAAe,EACf,mBAAmB,EACnB,qBAAqB,EACtB;IACC,MAAM,gBAAgB,OAAO,QAAQ,CAAC,MAAM;IAC5C,MAAM,oBAAoB,OAAO,QAAQ,CAAC,MAAM,CAC9C,CAAC,KAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAM,EAAE,WAAW,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,GAAG,IAAI,CAAC,WAAW,GAC/G,MAAM;IACR,MAAM,WACJ,gBAAgB,IAAI,AAAC,oBAAoB,gBAAiB,MAAM;IAElE,mDAAmD;IACnD,MAAM,mBAAmB,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAA,KAAM,GAAG,UAAU;IACnE,MAAM,sBAAsB,iBAAiB,MAAM,GAAG,KAAK,iBAAiB,KAAK,CAC/E,CAAC,KAAO,gBAAgB,CAAC,GAAG,EAAE,CAAC,KAAK;IAItC,qBACE,6LAAC,mIAAA,CAAA,OAAI;QACH,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;QACzB,WAAW,CAAC,4BAA4B,EAAE,OAAO,UAAU,GAAG,qBAAqB,mBAAmB;;0BAEtG,6LAAC,mIAAA,CAAA,aAAU;gBACT,WAAW,CAAC,SAAS,EAAE,CAAC,OAAO,UAAU,GAAG,eAAe,IAAI;0BAE/D,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS,IAAM,OAAO,UAAU,IAAI;;sCAEpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAW,CAAC,eAAe,EAAE,OAAO,UAAU,GAAG,kCAAkC,6BAA6B;8CAEhH,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,OAAO,KAAK;;;;;;gDAC3C,CAAC,OAAO,UAAU,kBACjB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;sDAGpB,6LAAC;4CAAE,WAAU;sDAAsB,OAAO,WAAW;;;;;;sDACrD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gDAAC,OAAO;gDAAU,WAAU;;;;;;;;;;;sDAEvC,6LAAC;4CAAE,WAAU;;gDACV;gDAAkB;gDAAE;gDAAc;gDAClC,OAAO,UAAU,CAAC,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;wBAIpC,OAAO,UAAU,IAChB,CAAC,2BACC,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;iDAErB,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;gCACxB;;;;;;;;;;;;YAIN,cAAc,OAAO,UAAU,kBAC9B,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAIrB,6LAAC;wBAAI,WAAU;kCACZ,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACpB,6LAAC,kJAAA,CAAA,iBAAc;gCAEb,SAAS;gCACT,kBAAkB;gCAClB,iBAAiB;gCACjB,yBAAyB;gCACzB,aAAa;gCACb,YAAY,gBAAgB,CAAC,QAAQ,EAAE,CAAC,IAAI;gCAC5C,kBAAkB,IAAM,gBAAgB,QAAQ,EAAE;+BAP7C,QAAQ,EAAE;;;;;;;;;;kCAarB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,6LAAC,4IAAA,CAAA,WAAQ;gCACP,MAAM,OAAO,UAAU;gCACvB,YAAY,sBAAsB;gCAClC,aAAa,IAAM,YAAY,OAAO,UAAU,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAOjE;KA1Ga", "debugId": null}}, {"offset": {"line": 4933, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/certificate-template.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Building, Award } from 'lucide-react';\r\nimport Image from 'next/image'; // Import the Image component\r\nimport { CertificateTemplateProps } from '@/types/lms';\r\n\r\nexport const CertificateTemplate: React.FC<CertificateTemplateProps> = ({\r\n  institution,\r\n  course,\r\n  studentName,\r\n  completionDate\r\n}) => {\r\n  const primaryColor =\r\n    institution.certificateTemplate?.primaryColor || '#1e40af';\r\n  const secondaryColor =\r\n    institution.certificateTemplate?.secondaryColor || '#f59e0b';\r\n\r\n  return (\r\n    <div\r\n      className='space-y-6 rounded-lg border-4 p-8 text-center'\r\n      style={{\r\n        borderColor: secondaryColor,\r\n        background: `linear-gradient(to right, ${primaryColor}10, ${secondaryColor}10)`\r\n      }}\r\n    >\r\n      <div className='border-b-2 pb-4' style={{ borderColor: secondaryColor }}>\r\n        <div className='mb-2 flex items-center justify-center space-x-3'>\r\n          {institution.certificateTemplate?.logoUrl && (\r\n            <Image\r\n              src={institution.certificateTemplate.logoUrl}\r\n              alt={institution.name}\r\n              width={48} // Add the width property\r\n              height={48} // Add the height property\r\n              className='h-12 w-12'\r\n            />\r\n          )}\r\n          <Building className='h-8 w-8' style={{ color: primaryColor }} />\r\n        </div>\r\n        <h2 className='text-3xl font-bold' style={{ color: primaryColor }}>\r\n          {institution.name}\r\n        </h2>\r\n        <p style={{ color: primaryColor }}>Certificate of Completion</p>\r\n      </div>\r\n\r\n      <div className='space-y-4'>\r\n        <p className='text-lg' style={{ color: `${primaryColor}cc` }}>\r\n          This is to certify that\r\n        </p>\r\n        <h3 className='text-2xl font-bold' style={{ color: primaryColor }}>\r\n          {studentName}\r\n        </h3>\r\n        <p className='text-lg' style={{ color: `${primaryColor}cc` }}>\r\n          has successfully completed the course\r\n        </p>\r\n        <h4 className='text-xl font-semibold' style={{ color: primaryColor }}>\r\n          {course.name}\r\n        </h4>\r\n        <p style={{ color: `${primaryColor}cc` }}>Course Code: {course.code}</p>\r\n      </div>\r\n\r\n      <div className='flex justify-center space-x-12 py-6'>\r\n        <div className='text-center'>\r\n          <div\r\n            className='mb-2 h-1 w-32'\r\n            style={{ backgroundColor: secondaryColor }}\r\n          ></div>\r\n          <p className='text-sm' style={{ color: `${primaryColor}cc` }}>\r\n            Instructor\r\n          </p>\r\n          <p className='font-medium' style={{ color: primaryColor }}>\r\n            {course.instructor}\r\n          </p>\r\n        </div>\r\n        <div className='text-center'>\r\n          <div\r\n            className='mb-2 h-1 w-32'\r\n            style={{ backgroundColor: secondaryColor }}\r\n          ></div>\r\n          <p className='text-sm' style={{ color: `${primaryColor}cc` }}>\r\n            Date of Completion\r\n          </p>\r\n          <p className='font-medium' style={{ color: primaryColor }}>\r\n            {completionDate}\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className='border-t-2 pt-4' style={{ borderColor: secondaryColor }}>\r\n        <Award\r\n          className='mx-auto mb-2 h-12 w-12'\r\n          style={{ color: secondaryColor }}\r\n        />\r\n        <p className='text-sm' style={{ color: `${primaryColor}99` }}>\r\n          Certificate ID: {institution.shortName}-{course.code}-2024-001\r\n        </p>\r\n        {institution.certificateTemplate?.signatoryName && (\r\n          <div className='mt-4'>\r\n            <p className='font-medium' style={{ color: primaryColor }}>\r\n              {institution.certificateTemplate.signatoryName}\r\n            </p>\r\n            <p className='text-sm' style={{ color: `${primaryColor}cc` }}>\r\n              {institution.certificateTemplate.signatoryTitle}\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AACA;AAAA;AACA,kOAAgC,6BAA6B;;;;AAGtD,MAAM,sBAA0D,CAAC,EACtE,WAAW,EACX,MAAM,EACN,WAAW,EACX,cAAc,EACf;IACC,MAAM,eACJ,YAAY,mBAAmB,EAAE,gBAAgB;IACnD,MAAM,iBACJ,YAAY,mBAAmB,EAAE,kBAAkB;IAErD,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YACL,aAAa;YACb,YAAY,CAAC,0BAA0B,EAAE,aAAa,IAAI,EAAE,eAAe,GAAG,CAAC;QACjF;;0BAEA,6LAAC;gBAAI,WAAU;gBAAkB,OAAO;oBAAE,aAAa;gBAAe;;kCACpE,6LAAC;wBAAI,WAAU;;4BACZ,YAAY,mBAAmB,EAAE,yBAChC,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,YAAY,mBAAmB,CAAC,OAAO;gCAC5C,KAAK,YAAY,IAAI;gCACrB,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAGd,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;gCAAU,OAAO;oCAAE,OAAO;gCAAa;;;;;;;;;;;;kCAE7D,6LAAC;wBAAG,WAAU;wBAAqB,OAAO;4BAAE,OAAO;wBAAa;kCAC7D,YAAY,IAAI;;;;;;kCAEnB,6LAAC;wBAAE,OAAO;4BAAE,OAAO;wBAAa;kCAAG;;;;;;;;;;;;0BAGrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;wBAAU,OAAO;4BAAE,OAAO,GAAG,aAAa,EAAE,CAAC;wBAAC;kCAAG;;;;;;kCAG9D,6LAAC;wBAAG,WAAU;wBAAqB,OAAO;4BAAE,OAAO;wBAAa;kCAC7D;;;;;;kCAEH,6LAAC;wBAAE,WAAU;wBAAU,OAAO;4BAAE,OAAO,GAAG,aAAa,EAAE,CAAC;wBAAC;kCAAG;;;;;;kCAG9D,6LAAC;wBAAG,WAAU;wBAAwB,OAAO;4BAAE,OAAO;wBAAa;kCAChE,OAAO,IAAI;;;;;;kCAEd,6LAAC;wBAAE,OAAO;4BAAE,OAAO,GAAG,aAAa,EAAE,CAAC;wBAAC;;4BAAG;4BAAc,OAAO,IAAI;;;;;;;;;;;;;0BAGrE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB;gCAAe;;;;;;0CAE3C,6LAAC;gCAAE,WAAU;gCAAU,OAAO;oCAAE,OAAO,GAAG,aAAa,EAAE,CAAC;gCAAC;0CAAG;;;;;;0CAG9D,6LAAC;gCAAE,WAAU;gCAAc,OAAO;oCAAE,OAAO;gCAAa;0CACrD,OAAO,UAAU;;;;;;;;;;;;kCAGtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB;gCAAe;;;;;;0CAE3C,6LAAC;gCAAE,WAAU;gCAAU,OAAO;oCAAE,OAAO,GAAG,aAAa,EAAE,CAAC;gCAAC;0CAAG;;;;;;0CAG9D,6LAAC;gCAAE,WAAU;gCAAc,OAAO;oCAAE,OAAO;gCAAa;0CACrD;;;;;;;;;;;;;;;;;;0BAKP,6LAAC;gBAAI,WAAU;gBAAkB,OAAO;oBAAE,aAAa;gBAAe;;kCACpE,6LAAC,uMAAA,CAAA,QAAK;wBACJ,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAe;;;;;;kCAEjC,6LAAC;wBAAE,WAAU;wBAAU,OAAO;4BAAE,OAAO,GAAG,aAAa,EAAE,CAAC;wBAAC;;4BAAG;4BAC3C,YAAY,SAAS;4BAAC;4BAAE,OAAO,IAAI;4BAAC;;;;;;;oBAEtD,YAAY,mBAAmB,EAAE,+BAChC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;gCAAc,OAAO;oCAAE,OAAO;gCAAa;0CACrD,YAAY,mBAAmB,CAAC,aAAa;;;;;;0CAEhD,6LAAC;gCAAE,WAAU;gCAAU,OAAO;oCAAE,OAAO,GAAG,aAAa,EAAE,CAAC;gCAAC;0CACxD,YAAY,mBAAmB,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;AAO7D;KAtGa", "debugId": null}}, {"offset": {"line": 5262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/tabs/course-tab.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { BookMarked, Target, Award, Maximize, Minimize, X, ChevronLeft, ChevronRight, FileText } from 'lucide-react';\r\nimport { Course } from '@/types/lms';\r\nimport { TableOfContents } from '../table-of-contents';\r\nimport { ContentViewer } from '../content-viewer';\r\nimport { QuizIntroduction } from '../quiz-introduction';\r\n\r\ninterface CourseTabProps {\r\n  courseData: Course;\r\n  expandedModules: { [key: string]: boolean };\r\n  expandedChapters: { [key: string]: boolean };\r\n  expandedContents: { [key: string]: boolean };\r\n  onToggleModule: (moduleId: string) => void;\r\n  onToggleChapter: (chapterId: string) => void;\r\n  onToggleContent: (contentId: string) => void;\r\n  onToggleContentComplete: (contentId: string) => void;\r\n  onStartQuiz: (quizId: string) => void;\r\n  onNavigateToSection: (moduleId: string, chapterId?: string, contentId?: string) => void;\r\n  onExpandAllModules: () => void;\r\n  onCollapseAllModules: () => void;\r\n  onExpandAllChaptersInModule: (moduleId: string) => void;\r\n  onCollapseAllChaptersInModule: (moduleId: string) => void;\r\n  onNavigateToFinalExam?: () => void;\r\n  isLearningMode?: boolean;\r\n  currentModuleId?: string;\r\n  currentChapterId?: string;\r\n  currentContentId?: string;\r\n}\r\n\r\nexport const CourseTab: React.FC<CourseTabProps> = ({\r\n  courseData,\r\n  expandedModules,\r\n  expandedChapters,\r\n  expandedContents,\r\n  onToggleModule,\r\n  onToggleChapter,\r\n  onToggleContent,\r\n  onToggleContentComplete,\r\n  onStartQuiz,\r\n  onNavigateToSection,\r\n  onExpandAllModules,\r\n  onCollapseAllModules,\r\n  onExpandAllChaptersInModule,\r\n  onCollapseAllChaptersInModule,\r\n  onNavigateToFinalExam,\r\n  isLearningMode,\r\n  currentModuleId,\r\n  currentChapterId,\r\n  currentContentId\r\n}) => {\r\n  const [showCourseStructure, setShowCourseStructure] = useState(true);\r\n  const [currentModuleIndex, setCurrentModuleIndex] = useState(0);\r\n  const [showQuizIntro, setShowQuizIntro] = useState(false);\r\n  const [currentQuiz, setCurrentQuiz] = useState<any>(null);\r\n  const [quizType, setQuizType] = useState<'chapter' | 'module' | 'final'>('chapter');\r\n\r\n  const allModulesExpanded = courseData.modules\r\n    .filter((m) => m.isUnlocked)\r\n    .every((m) => expandedModules[m.id]);\r\n\r\n  const currentModule = courseData.modules[currentModuleIndex];\r\n  const canGoPrevious = currentModuleIndex > 0;\r\n  const canGoNext = currentModuleIndex < courseData.modules.length - 1;\r\n  const isLastModule = currentModuleIndex === courseData.modules.length - 1;\r\n\r\n  const handlePreviousModule = () => {\r\n    if (canGoPrevious) {\r\n      setCurrentModuleIndex(currentModuleIndex - 1);\r\n    }\r\n  };\r\n\r\n  const handleNextModule = () => {\r\n    if (canGoNext) {\r\n      setCurrentModuleIndex(currentModuleIndex + 1);\r\n    }\r\n  };\r\n\r\n  const handleNextAction = () => {\r\n    if (isLastModule && onNavigateToFinalExam) {\r\n      onNavigateToFinalExam();\r\n    } else {\r\n      handleNextModule();\r\n    }\r\n  };\r\n\r\n  const handleModuleNavigation = (moduleId: string) => {\r\n    const moduleIndex = courseData.modules.findIndex(m => m.id === moduleId);\r\n    if (moduleIndex !== -1) {\r\n      setCurrentModuleIndex(moduleIndex);\r\n    }\r\n  };\r\n\r\n  const findQuizById = (quizId: string, type: 'chapter' | 'module' | 'final') => {\r\n    if (type === 'final') {\r\n      return courseData.finalExam;\r\n    }\r\n    \r\n    for (const module of courseData.modules) {\r\n      if (type === 'module' && module.moduleQuiz.id === quizId) {\r\n        return module.moduleQuiz;\r\n      }\r\n      \r\n      for (const chapter of module.chapters) {\r\n        if (type === 'chapter' && chapter.quiz.id === quizId) {\r\n          return chapter.quiz;\r\n        }\r\n      }\r\n    }\r\n    \r\n    return null;\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      <div className='grid grid-cols-1 gap-6 lg:grid-cols-5 min-w-0'>\r\n        {/* Sidebar */}\r\n        {!isLearningMode && (\r\n          <div className='lg:col-span-2 min-w-0 overflow-y-auto'>\r\n            <div className='w-full'>\r\n              <TableOfContents\r\n                course={courseData}\r\n                onNavigate={(moduleId, chapterId, contentId) => {\r\n                  handleModuleNavigation(moduleId);\r\n                  onNavigateToSection(moduleId, chapterId, contentId);\r\n                }}\r\n                expandedModules={expandedModules}\r\n                expandedChapters={expandedChapters}\r\n                onToggleModule={onToggleModule}\r\n                onToggleChapter={onToggleChapter}\r\n                currentModuleIndex={currentModuleIndex}\r\n              />\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Main Content */}\r\n        <div\r\n          className={`${\r\n            isLearningMode ? 'lg:col-span-5' : 'lg:col-span-3'\r\n          } min-w-0`}\r\n        >\r\n          {showQuizIntro && currentQuiz ? (\r\n            <QuizIntroduction\r\n              quiz={currentQuiz}\r\n              quizType={quizType}\r\n              onStartQuiz={() => {\r\n                setShowQuizIntro(false);\r\n                onStartQuiz(currentQuiz.id);\r\n              }}\r\n              onCancel={() => {\r\n                setShowQuizIntro(false);\r\n                setCurrentQuiz(null);\r\n              }}\r\n            />\r\n          ) : currentModuleId && currentChapterId && currentContentId ? (\r\n            <ContentViewer\r\n              course={courseData}\r\n              currentModuleId={currentModuleId}\r\n              currentChapterId={currentChapterId}\r\n              currentContentId={currentContentId}\r\n              onNavigate={onNavigateToSection}\r\n              onStartQuiz={(quizId, type) => {\r\n                const quiz = findQuizById(quizId, type);\r\n                if (quiz) {\r\n                  setCurrentQuiz(quiz);\r\n                  setQuizType(type);\r\n                  setShowQuizIntro(true);\r\n                }\r\n              }}\r\n              onContentComplete={onToggleContentComplete}\r\n            />\r\n          ) : (\r\n            <div className=\"flex items-center justify-center h-96\">\r\n              <div className=\"text-center\">\r\n                <BookMarked className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\r\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Pilih Konten</h3>\r\n                <p className=\"text-gray-600\">Silakan pilih konten dari navigasi di sebelah kiri untuk memulai pembelajaran.</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAEA;AACA;AACA;;;;;;;;AAwBO,MAAM,YAAsC,CAAC,EAClD,UAAU,EACV,eAAe,EACf,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EACd,eAAe,EACf,eAAe,EACf,uBAAuB,EACvB,WAAW,EACX,mBAAmB,EACnB,kBAAkB,EAClB,oBAAoB,EACpB,2BAA2B,EAC3B,6BAA6B,EAC7B,qBAAqB,EACrB,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,gBAAgB,EACjB;;IACC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IAEzE,MAAM,qBAAqB,WAAW,OAAO,CAC1C,MAAM,CAAC,CAAC,IAAM,EAAE,UAAU,EAC1B,KAAK,CAAC,CAAC,IAAM,eAAe,CAAC,EAAE,EAAE,CAAC;IAErC,MAAM,gBAAgB,WAAW,OAAO,CAAC,mBAAmB;IAC5D,MAAM,gBAAgB,qBAAqB;IAC3C,MAAM,YAAY,qBAAqB,WAAW,OAAO,CAAC,MAAM,GAAG;IACnE,MAAM,eAAe,uBAAuB,WAAW,OAAO,CAAC,MAAM,GAAG;IAExE,MAAM,uBAAuB;QAC3B,IAAI,eAAe;YACjB,sBAAsB,qBAAqB;QAC7C;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,WAAW;YACb,sBAAsB,qBAAqB;QAC7C;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,gBAAgB,uBAAuB;YACzC;QACF,OAAO;YACL;QACF;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,cAAc,WAAW,OAAO,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/D,IAAI,gBAAgB,CAAC,GAAG;YACtB,sBAAsB;QACxB;IACF;IAEA,MAAM,eAAe,CAAC,QAAgB;QACpC,IAAI,SAAS,SAAS;YACpB,OAAO,WAAW,SAAS;QAC7B;QAEA,KAAK,MAAM,UAAU,WAAW,OAAO,CAAE;YACvC,IAAI,SAAS,YAAY,OAAO,UAAU,CAAC,EAAE,KAAK,QAAQ;gBACxD,OAAO,OAAO,UAAU;YAC1B;YAEA,KAAK,MAAM,WAAW,OAAO,QAAQ,CAAE;gBACrC,IAAI,SAAS,aAAa,QAAQ,IAAI,CAAC,EAAE,KAAK,QAAQ;oBACpD,OAAO,QAAQ,IAAI;gBACrB;YACF;QACF;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;gBAEZ,CAAC,gCACA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uJAAA,CAAA,kBAAe;4BACd,QAAQ;4BACR,YAAY,CAAC,UAAU,WAAW;gCAChC,uBAAuB;gCACvB,oBAAoB,UAAU,WAAW;4BAC3C;4BACA,iBAAiB;4BACjB,kBAAkB;4BAClB,gBAAgB;4BAChB,iBAAiB;4BACjB,oBAAoB;;;;;;;;;;;;;;;;8BAO5B,6LAAC;oBACC,WAAW,GACT,iBAAiB,kBAAkB,gBACpC,QAAQ,CAAC;8BAET,iBAAiB,4BAChB,6LAAC,oJAAA,CAAA,mBAAgB;wBACf,MAAM;wBACN,UAAU;wBACV,aAAa;4BACX,iBAAiB;4BACjB,YAAY,YAAY,EAAE;wBAC5B;wBACA,UAAU;4BACR,iBAAiB;4BACjB,eAAe;wBACjB;;;;;+BAEA,mBAAmB,oBAAoB,iCACzC,6LAAC,iJAAA,CAAA,gBAAa;wBACZ,QAAQ;wBACR,iBAAiB;wBACjB,kBAAkB;wBAClB,kBAAkB;wBAClB,YAAY;wBACZ,aAAa,CAAC,QAAQ;4BACpB,MAAM,OAAO,aAAa,QAAQ;4BAClC,IAAI,MAAM;gCACR,eAAe;gCACf,YAAY;gCACZ,iBAAiB;4BACnB;wBACF;wBACA,mBAAmB;;;;;6CAGrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C;GA3Ja;KAAA", "debugId": null}}, {"offset": {"line": 5469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/tabs/progress-tab.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { BarChart3, Target } from 'lucide-react';\r\nimport { Course } from '@/types/lms';\r\n\r\ninterface ProgressTabProps {\r\n  courseData: Course;\r\n  overallProgress: number;\r\n}\r\n\r\nexport const ProgressTab: React.FC<ProgressTabProps> = ({\r\n  courseData,\r\n  overallProgress\r\n}) => {\r\n  return (\r\n    <div className='grid gap-6'>\r\n      {/* Progress Overview */}\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle className='flex items-center space-x-2'>\r\n            <BarChart3 className='h-5 w-5' />\r\n            <span>Ringkasan <PERSON> Belaja<PERSON></span>\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className='grid grid-cols-1 gap-6 md:grid-cols-3'>\r\n            <div className='text-center'>\r\n              <div className='mb-2 text-3xl font-bold text-blue-600'>\r\n                {Math.round(overallProgress)}%\r\n              </div>\r\n              <p className='text-gray-600'><PERSON><PERSON><PERSON><PERSON></p>\r\n              <Progress value={overallProgress} className='mt-2' />\r\n            </div>\r\n            <div className='text-center'>\r\n              <div className='mb-2 text-3xl font-bold text-green-600'>\r\n                {courseData.modules.filter((m) => m.moduleQuiz.isPassed).length}\r\n              </div>\r\n              <p className='text-gray-600'>Modul Selesai</p>\r\n              <Progress\r\n                value={\r\n                  (courseData.modules.filter((m) => m.moduleQuiz.isPassed)\r\n                    .length /\r\n                    courseData.modules.length) *\r\n                  100\r\n                }\r\n                className='mt-2'\r\n              />\r\n            </div>\r\n            <div className='text-center'>\r\n              <div className='mb-2 text-3xl font-bold text-purple-600'>0</div>\r\n              <p className='text-gray-600'>Jam Belajar</p>\r\n              <p className='mt-1 text-sm text-gray-500'>\r\n                Pelacakan waktu segera tersedia\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Module Progress Details */}\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>Detail Kemajuan Modul</CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className='space-y-4'>\r\n            {courseData.modules.map((module) => {\r\n              const completedChapters = module.chapters.filter(\r\n                (ch) =>\r\n                  ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed\r\n              ).length;\r\n              const moduleProgress =\r\n                (completedChapters / module.chapters.length) * 100;\r\n\r\n              return (\r\n                <div key={module.id} className='rounded-lg border p-4'>\r\n                  <div className='mb-3 flex items-center justify-between'>\r\n                    <h4 className='font-medium'>{module.title}</h4>\r\n                    <Badge\r\n                      variant={\r\n                        module.moduleQuiz.isPassed ? 'default' : 'outline'\r\n                      }\r\n                    >\r\n                      {module.moduleQuiz.isPassed ? 'Selesai' : 'Sedang Belajar'}\r\n                    </Badge>\r\n                  </div>\r\n                  <Progress value={moduleProgress} className='mb-2' />\r\n                  <div className='flex justify-between text-sm text-gray-600'>\r\n                    <span>\r\n                      {completedChapters}/{module.chapters.length} bab\r\n                      selesai\r\n                    </span>\r\n                    <span>{Math.round(moduleProgress)}%</span>\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Quiz Results */}\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>Performa Kuis</CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          {(() => {\r\n            const allQuizzes = [];\r\n\r\n            // Collect all quizzes from chapters and modules\r\n            courseData.modules.forEach((module) => {\r\n              module.chapters.forEach((chapter) => {\r\n                if (chapter.quiz.attempts > 0) {\r\n                  allQuizzes.push({\r\n                    ...chapter.quiz,\r\n                    moduleName: module.title,\r\n                    chapterName: chapter.title,\r\n                    type: 'chapter' as const\r\n                  });\r\n                }\r\n              });\r\n\r\n              if (module.moduleQuiz.attempts > 0) {\r\n                allQuizzes.push({\r\n                  ...module.moduleQuiz,\r\n                  moduleName: module.title,\r\n                  chapterName: null,\r\n                  type: 'module' as const\r\n                });\r\n              }\r\n            });\r\n\r\n            // Add final exam if attempted\r\n            if (courseData.finalExam.attempts > 0) {\r\n              allQuizzes.push({\r\n                ...courseData.finalExam,\r\n                moduleName: 'Final Assessment',\r\n                chapterName: null,\r\n                type: 'final' as const\r\n              });\r\n            }\r\n\r\n            if (allQuizzes.length === 0) {\r\n              return (\r\n                <div className='py-8 text-center text-gray-500'>\r\n                  <Target className='mx-auto mb-4 h-12 w-12 text-gray-400' />\r\n                  <p>\r\n                    Hasil kuis akan muncul di sini saat Anda menyelesaikan penilaian\r\n                  </p>\r\n                </div>\r\n              );\r\n            }\r\n\r\n            return (\r\n              <div className='space-y-4'>\r\n                {allQuizzes.map((quiz) => (\r\n                  <div key={quiz.id} className='rounded-lg border p-4'>\r\n                    <div className='mb-2 flex items-start justify-between'>\r\n                      <div>\r\n                        <h4 className='font-medium'>{quiz.title}</h4>\r\n                        <p className='text-sm text-gray-600'>\r\n                          {quiz.moduleName}\r\n                          {quiz.chapterName ? ` • ${quiz.chapterName}` : ''}\r\n                        </p>\r\n                      </div>\r\n                      <Badge\r\n                        variant={quiz.isPassed ? 'default' : 'destructive'}\r\n                        className={quiz.isPassed ? 'bg-green-600 hover:bg-green-700 text-white' : ''}\r\n                      >\r\n                        {quiz.isPassed ? 'Lulus' : 'Tidak Lulus'}\r\n                      </Badge>\r\n                    </div>\r\n\r\n                    <div className='grid grid-cols-2 gap-4 text-sm md:grid-cols-4'>\r\n                      <div>\r\n                        <span className='text-gray-500'>Skor:</span>\r\n                        <div className='font-medium'>\r\n                          {quiz.lastScore !== undefined\r\n                            ? `${quiz.lastScore}%`\r\n                            : 'N/A'}\r\n                        </div>\r\n                      </div>\r\n                      <div>\r\n                        <span className='text-gray-500'>Diperlukan:</span>\r\n                        <div className='font-medium'>{quiz.minimumScore}%</div>\r\n                      </div>\r\n                      <div>\r\n                        <span className='text-gray-500'>Percobaan:</span>\r\n                        <div className='font-medium'>\r\n                          {quiz.attempts}/{quiz.maxAttempts}\r\n                        </div>\r\n                      </div>\r\n                      <div>\r\n                        <span className='text-gray-500'>Jenis:</span>\r\n                        <div className='font-medium capitalize'>\r\n                          {quiz.type}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {quiz.lastScore !== undefined && (\r\n                      <div className='mt-3'>\r\n                        <Progress\r\n                          value={quiz.lastScore}\r\n                          className={`h-2 ${quiz.isPassed ? 'text-green-600' : 'text-red-600'}`}\r\n                        />\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            );\r\n          })()}\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;;;;;;AAQO,MAAM,cAA0C,CAAC,EACtD,UAAU,EACV,eAAe,EAChB;IACC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,KAAK,CAAC;gDAAiB;;;;;;;sDAE/B,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,OAAO;4CAAiB,WAAU;;;;;;;;;;;;8CAE9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,WAAW,OAAO,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,UAAU,CAAC,QAAQ,EAAE,MAAM;;;;;;sDAEjE,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,6LAAC,uIAAA,CAAA,WAAQ;4CACP,OACE,AAAC,WAAW,OAAO,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,UAAU,CAAC,QAAQ,EACpD,MAAM,GACP,WAAW,OAAO,CAAC,MAAM,GAC3B;4CAEF,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA0C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC;gCACvB,MAAM,oBAAoB,OAAO,QAAQ,CAAC,MAAM,CAC9C,CAAC,KACC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAM,EAAE,WAAW,KAAK,GAAG,IAAI,CAAC,QAAQ,EAC7D,MAAM;gCACR,MAAM,iBACJ,AAAC,oBAAoB,OAAO,QAAQ,CAAC,MAAM,GAAI;gCAEjD,qBACE,6LAAC;oCAAoB,WAAU;;sDAC7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAe,OAAO,KAAK;;;;;;8DACzC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,SACE,OAAO,UAAU,CAAC,QAAQ,GAAG,YAAY;8DAG1C,OAAO,UAAU,CAAC,QAAQ,GAAG,YAAY;;;;;;;;;;;;sDAG9C,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,OAAO;4CAAgB,WAAU;;;;;;sDAC3C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDACE;wDAAkB;wDAAE,OAAO,QAAQ,CAAC,MAAM;wDAAC;;;;;;;8DAG9C,6LAAC;;wDAAM,KAAK,KAAK,CAAC;wDAAgB;;;;;;;;;;;;;;mCAjB5B,OAAO,EAAE;;;;;4BAqBvB;;;;;;;;;;;;;;;;;0BAMN,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACT,CAAC;4BACA,MAAM,aAAa,EAAE;4BAErB,gDAAgD;4BAChD,WAAW,OAAO,CAAC,OAAO,CAAC,CAAC;gCAC1B,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC;oCACvB,IAAI,QAAQ,IAAI,CAAC,QAAQ,GAAG,GAAG;wCAC7B,WAAW,IAAI,CAAC;4CACd,GAAG,QAAQ,IAAI;4CACf,YAAY,OAAO,KAAK;4CACxB,aAAa,QAAQ,KAAK;4CAC1B,MAAM;wCACR;oCACF;gCACF;gCAEA,IAAI,OAAO,UAAU,CAAC,QAAQ,GAAG,GAAG;oCAClC,WAAW,IAAI,CAAC;wCACd,GAAG,OAAO,UAAU;wCACpB,YAAY,OAAO,KAAK;wCACxB,aAAa;wCACb,MAAM;oCACR;gCACF;4BACF;4BAEA,8BAA8B;4BAC9B,IAAI,WAAW,SAAS,CAAC,QAAQ,GAAG,GAAG;gCACrC,WAAW,IAAI,CAAC;oCACd,GAAG,WAAW,SAAS;oCACvB,YAAY;oCACZ,aAAa;oCACb,MAAM;gCACR;4BACF;4BAEA,IAAI,WAAW,MAAM,KAAK,GAAG;gCAC3B,qBACE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAE;;;;;;;;;;;;4BAKT;4BAEA,qBACE,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;wCAAkB,WAAU;;0DAC3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAe,KAAK,KAAK;;;;;;0EACvC,6LAAC;gEAAE,WAAU;;oEACV,KAAK,UAAU;oEACf,KAAK,WAAW,GAAG,CAAC,GAAG,EAAE,KAAK,WAAW,EAAE,GAAG;;;;;;;;;;;;;kEAGnD,6LAAC,oIAAA,CAAA,QAAK;wDACJ,SAAS,KAAK,QAAQ,GAAG,YAAY;wDACrC,WAAW,KAAK,QAAQ,GAAG,+CAA+C;kEAEzE,KAAK,QAAQ,GAAG,UAAU;;;;;;;;;;;;0DAI/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAI,WAAU;0EACZ,KAAK,SAAS,KAAK,YAChB,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,GACpB;;;;;;;;;;;;kEAGR,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAI,WAAU;;oEAAe,KAAK,YAAY;oEAAC;;;;;;;;;;;;;kEAElD,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAI,WAAU;;oEACZ,KAAK,QAAQ;oEAAC;oEAAE,KAAK,WAAW;;;;;;;;;;;;;kEAGrC,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAI,WAAU;0EACZ,KAAK,IAAI;;;;;;;;;;;;;;;;;;4CAKf,KAAK,SAAS,KAAK,2BAClB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;oDACP,OAAO,KAAK,SAAS;oDACrB,WAAW,CAAC,IAAI,EAAE,KAAK,QAAQ,GAAG,mBAAmB,gBAAgB;;;;;;;;;;;;uCAhDnE,KAAK,EAAE;;;;;;;;;;wBAwDzB,CAAC;;;;;;;;;;;;;;;;;;AAKX;KAhNa", "debugId": null}}, {"offset": {"line": 6044, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/tabs/exam-tab.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Trophy, CheckCircle, XCircle } from 'lucide-react';\r\nimport { Course } from '@/types/lms';\r\n\r\ninterface ExamTabProps {\r\n  courseData: Course;\r\n  onStartQuiz: (quizId: string) => void;\r\n}\r\n\r\nexport const ExamTab: React.FC<ExamTabProps> = ({\r\n  courseData,\r\n  onStartQuiz\r\n}) => {\r\n  const isFinalExamUnlocked = courseData.modules.every(\r\n    (m) =>\r\n      m.chapters.every(\r\n        (ch) => ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed\r\n      ) && m.moduleQuiz.isPassed\r\n  );\r\n\r\n  return (\r\n    <Card className='shadow-sm'>\r\n      <CardHeader>\r\n        <CardTitle className='flex items-center space-x-2'>\r\n          <Trophy className='h-6 w-6' />\r\n          <span><PERSON><PERSON><PERSON></span>\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className='p-6'>\r\n        <div className='space-y-6'>\r\n          {/* Exam Requirements */}\r\n          <div className='rounded-lg border border-amber-200 bg-amber-50 p-4'>\r\n            <h4 className='mb-2 font-semibold text-amber-800'>\r\n              Persyaratan Ujian\r\n            </h4>\r\n            <ul className='space-y-1 text-sm text-amber-700'>\r\n              <li>• Selesaikan semua modul dan lulus semua kuis modul</li>\r\n              <li>\r\n                • Nilai minimum lulus: {courseData.finalExam.minimumScore}%\r\n              </li>\r\n              <li>• Batas waktu: {courseData.finalExam.timeLimit} menit</li>\r\n              <li>• Maksimal percobaan: {courseData.finalExam.maxAttempts}</li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Current Status */}\r\n          <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>\r\n            <div className='space-y-4'>\r\n              <h4 className='font-medium text-gray-900'>\r\n                Status Prasyarat\r\n              </h4>\r\n              {courseData.modules.map((module) => (\r\n                <div\r\n                  key={module.id}\r\n                  className='flex items-center justify-between rounded-lg bg-gray-50 p-3'\r\n                >\r\n                  <span className='text-sm'>{module.title}</span>\r\n                  {module.moduleQuiz.isPassed ? (\r\n                    <CheckCircle className='h-5 w-5 text-green-600' />\r\n                  ) : (\r\n                    <XCircle className='h-5 w-5 text-red-500' />\r\n                  )}\r\n                </div>\r\n              ))}\r\n            </div>\r\n            <div className='space-y-4'>\r\n              <h4 className='font-medium text-gray-900'>Informasi Ujian</h4>\r\n              <div className='space-y-3'>\r\n                <div className='flex items-center justify-between'>\r\n                  <span className='text-sm text-gray-600'>Percobaan Digunakan</span>\r\n                  <span className='font-medium'>\r\n                    {courseData.finalExam.attempts}/\r\n                    {courseData.finalExam.maxAttempts}\r\n                  </span>\r\n                </div>\r\n                <div className='flex items-center justify-between'>\r\n                  <span className='text-sm text-gray-600'>Skor Terakhir</span>\r\n                  <span className='font-medium'>\r\n                    {courseData.finalExam.lastScore\r\n                      ? `${courseData.finalExam.lastScore}%`\r\n                      : 'N/A'}\r\n                  </span>\r\n                </div>\r\n                <div className='flex items-center justify-between'>\r\n                  <span className='text-sm text-gray-600'>Status</span>\r\n                  <Badge\r\n                    variant={\r\n                      courseData.finalExam.isPassed ? 'default' : 'outline'\r\n                    }\r\n                  >\r\n                    {courseData.finalExam.isPassed ? 'Lulus' : 'Belum Diambil'}\r\n                  </Badge>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Exam Action */}\r\n          <div className='border-t pt-4 text-center'>\r\n            <Button\r\n              size='lg'\r\n              variant='iai'\r\n              disabled={\r\n                courseData.finalExam.attempts >=\r\n                  courseData.finalExam.maxAttempts\r\n              }\r\n              onClick={() => onStartQuiz(courseData.finalExam.id)}\r\n            >\r\n              <Trophy className='mr-2 h-5 w-5' />\r\n              {courseData.finalExam.attempts === 0\r\n                ? 'Mulai Ujian Akhir'\r\n                : 'Ulangi Ujian Akhir'}\r\n            </Button>\r\n            {!isFinalExamUnlocked && (\r\n              <p className='mt-2 text-sm text-gray-500'>\r\n                Selesaikan semua modul untuk membuka ujian akhir\r\n              </p>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;;AAQO,MAAM,UAAkC,CAAC,EAC9C,UAAU,EACV,WAAW,EACZ;IACC,MAAM,sBAAsB,WAAW,OAAO,CAAC,KAAK,CAClD,CAAC,IACC,EAAE,QAAQ,CAAC,KAAK,CACd,CAAC,KAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAM,EAAE,WAAW,KAAK,GAAG,IAAI,CAAC,QAAQ,KAChE,EAAE,UAAU,CAAC,QAAQ;IAG9B,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;sCAAK;;;;;;;;;;;;;;;;;0BAGV,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAGlD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;;gDAAG;gDACsB,WAAW,SAAS,CAAC,YAAY;gDAAC;;;;;;;sDAE5D,6LAAC;;gDAAG;gDAAgB,WAAW,SAAS,CAAC,SAAS;gDAAC;;;;;;;sDACnD,6LAAC;;gDAAG;gDAAuB,WAAW,SAAS,CAAC,WAAW;;;;;;;;;;;;;;;;;;;sCAK/D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;wCAGzC,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,uBACvB,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAW,OAAO,KAAK;;;;;;oDACtC,OAAO,UAAU,CAAC,QAAQ,iBACzB,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,6LAAC,+MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;+CAPhB,OAAO,EAAE;;;;;;;;;;;8CAYpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,6LAAC;4DAAK,WAAU;;gEACb,WAAW,SAAS,CAAC,QAAQ;gEAAC;gEAC9B,WAAW,SAAS,CAAC,WAAW;;;;;;;;;;;;;8DAGrC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,6LAAC;4DAAK,WAAU;sEACb,WAAW,SAAS,CAAC,SAAS,GAC3B,GAAG,WAAW,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,GACpC;;;;;;;;;;;;8DAGR,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,6LAAC,oIAAA,CAAA,QAAK;4DACJ,SACE,WAAW,SAAS,CAAC,QAAQ,GAAG,YAAY;sEAG7C,WAAW,SAAS,CAAC,QAAQ,GAAG,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQrD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,UACE,WAAW,SAAS,CAAC,QAAQ,IAC3B,WAAW,SAAS,CAAC,WAAW;oCAEpC,SAAS,IAAM,YAAY,WAAW,SAAS,CAAC,EAAE;;sDAElD,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCACjB,WAAW,SAAS,CAAC,QAAQ,KAAK,IAC/B,sBACA;;;;;;;gCAEL,CAAC,qCACA,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD;KAlHa", "debugId": null}}, {"offset": {"line": 6391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/lib/certificate.ts"], "sourcesContent": ["// Certificate generation utilities\r\n// In a real implementation, you might use libraries like jsPDF, canvas, or server-side PDF generation\r\n\r\nexport interface CertificateData {\r\n  studentName: string;\r\n  courseName: string;\r\n  courseCode: string;\r\n  completionDate: string;\r\n  finalScore: number;\r\n  instructorName: string;\r\n  institutionName: string;\r\n  certificateId: string;\r\n}\r\n\r\nexport const generateCertificateId = (): string => {\r\n  const year = new Date().getFullYear();\r\n  const randomNum = Math.floor(Math.random() * 10000)\r\n    .toString()\r\n    .padStart(4, '0');\r\n  return `CERT-${year}-${randomNum}`;\r\n};\r\n\r\nexport const generateCertificateHTML = (data: CertificateData): string => {\r\n  return `\r\n  <!DOCTYPE html>\r\n  <html lang=\"id\">\r\n  <head>\r\n      <meta charset=\"UTF-8\">\r\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n      <title>Serti<PERSON>kat Kelulusan</title>\r\n      <!-- Font -->\r\n      <link href=\"https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@700&display=swap\" rel=\"stylesheet\">\r\n      <style>\r\n          :root{\r\n              --primary:#4a90e2;\r\n              --accent:#a370e8;\r\n              --text:#2c3e50;\r\n              --muted:#7f8c8d;\r\n              --light:#f0f4f8;\r\n              --white:#ffffff;\r\n          }\r\n          *{box-sizing:border-box;margin:0;padding:0}\r\n          body{\r\n              background:linear-gradient(135deg,var(--light),#e2e8f0);\r\n              font-family:'Montserrat',sans-serif;\r\n              display:flex;\r\n              align-items:center;\r\n              justify-content:center;\r\n              min-height:100vh;\r\n              padding:20px;\r\n          }\r\n          .certificate{\r\n              width:100%;\r\n              max-width:900px;\r\n              background:var(--white);\r\n              border-radius:16px;\r\n              box-shadow:0 20px 40px rgba(0,0,0,.08);\r\n              position:relative;\r\n              overflow:hidden;\r\n              padding:80px 80px 110px;\r\n          }\r\n          .certificate::before,\r\n          .certificate::after{\r\n              content:'';\r\n              position:absolute;\r\n              width:300px;\r\n              height:300px;\r\n              border-radius:50%;\r\n              opacity:.05;\r\n              z-index:0;\r\n          }\r\n          .certificate::before{top:-80px;left:-80px;background:radial-gradient(var(--primary),transparent 70%)}\r\n          .certificate::after{bottom:-80px;right:-80px;background:radial-gradient(var(--accent),transparent 70%)}\r\n\r\n          .watermark{\r\n              position:absolute;\r\n              top:50%;left:50%;\r\n              transform:translate(-50%,-50%) rotate(-45deg);\r\n              font-family:'Playfair Display',serif;\r\n              font-size:150px;\r\n              color:rgba(0,0,0,.03);\r\n              font-weight:700;\r\n              pointer-events:none;\r\n              z-index:0;\r\n          }\r\n\r\n          .header{text-align:center;margin-bottom:50px}\r\n          .title{\r\n              font-family:'Playfair Display',serif;\r\n              font-size:44px;\r\n              color:var(--text);\r\n              margin:0;\r\n          }\r\n          .subtitle{\r\n              font-size:16px;\r\n              color:var(--muted);\r\n              margin-top:8px;\r\n          }\r\n\r\n          .main-content{\r\n              text-align:center;\r\n              margin-bottom:60px;\r\n          }\r\n          .awarded-to{\r\n              font-size:16px;\r\n              color:var(--muted);\r\n              margin-bottom:8px;\r\n          }\r\n          .student-name{\r\n              font-family:'Playfair Display',serif;\r\n              font-size:42px;\r\n              color:var(--text);\r\n              position:relative;\r\n              display:inline-block;\r\n              margin-bottom:20px;\r\n          }\r\n          .student-name::after{\r\n              content:'';\r\n              position:absolute;\r\n              left:50%;\r\n              bottom:-6px;\r\n              transform:translateX(-50%);\r\n              width:80%;\r\n              height:3px;\r\n              background:linear-gradient(90deg,var(--primary),var(--accent));\r\n              border-radius:2px;\r\n          }\r\n          .completion-text{\r\n              font-size:18px;\r\n              color:#555;\r\n              line-height:1.6;\r\n              max-width:600px;\r\n              margin:0 auto 25px;\r\n          }\r\n          .course-details{\r\n              display:inline-block;\r\n              background:var(--light);\r\n              border-radius:12px;\r\n              padding:20px 35px;\r\n              box-shadow:0 4px 15px rgba(0,0,0,.05);\r\n              margin-bottom:25px;\r\n          }\r\n          .course-name{\r\n              font-size:24px;\r\n              font-weight:600;\r\n              color:var(--text);\r\n              margin:0;\r\n          }\r\n          .course-code{\r\n              font-size:15px;\r\n              color:var(--muted);\r\n              margin-top:4px;\r\n          }\r\n          .score{\r\n              font-size:20px;\r\n              font-weight:700;\r\n              color:var(--primary);\r\n          }\r\n\r\n          .footer{\r\n              display:flex;\r\n              justify-content:space-around;\r\n              align-items:flex-end;\r\n              border-top:1px solid #ecf0f1;\r\n              padding-top:30px;\r\n          }\r\n          .signature-section{\r\n              text-align:center;\r\n              flex:1;\r\n          }\r\n          .signature-line{\r\n              width:180px;\r\n              height:1px;\r\n              background:var(--muted);\r\n              margin:0 auto 8px;\r\n          }\r\n          .signature-label{\r\n              font-size:14px;\r\n              color:var(--muted);\r\n              line-height:1.4;\r\n          }\r\n\r\n          .id-date-row{\r\n              margin-top:30px;\r\n              display:flex;\r\n              justify-content:space-between;\r\n              font-size:13px;\r\n              color:#95a5a6;\r\n          }\r\n      </style>\r\n  </head>\r\n  <body>\r\n      <div class=\"certificate\">\r\n          <div class=\"watermark\">TERANG</div>\r\n\r\n          <!-- Konten utama -->\r\n          <div class=\"header\">\r\n              <h1 class=\"title\">Sertifikat Kelulusan</h1>\r\n              <p class=\"subtitle\">${data.institutionName}</p>\r\n          </div>\r\n\r\n          <div class=\"main-content\">\r\n              <p class=\"awarded-to\">Dengan bangga mempersembahkan sertifikat ini kepada</p>\r\n              <h2 class=\"student-name\">${data.studentName}</h2>\r\n              <p class=\"completion-text\">\r\n                  karena telah berhasil menyelesaikan dan lulus dari program\r\n              </p>\r\n\r\n              <div class=\"course-details\">\r\n                  <h3 class=\"course-name\">${data.courseName}</h3>\r\n                  <div class=\"course-code\">Kode Kursus: ${data.courseCode}</div>\r\n              </div>\r\n\r\n              <p class=\"score\">Nilai Akhir: ${data.finalScore}%</p>\r\n          </div>\r\n\r\n          <div class=\"footer\">\r\n              <div class=\"signature-section\">\r\n                  <div class=\"signature-line\"></div>\r\n                  <p class=\"signature-label\">${data.instructorName}<br>Instruktur Kursus</p>\r\n              </div>\r\n              <div class=\"signature-section\">\r\n                  <div class=\"signature-line\"></div>\r\n                  <p class=\"signature-label\">Tanggal Kelulusan<br>${data.completionDate}</p>\r\n              </div>\r\n          </div>\r\n\r\n          <div class=\"id-date-row\">\r\n              <span>ID Sertifikat: ${data.certificateId}</span>\r\n              <span>Diterbitkan pada: ${data.completionDate}</span>\r\n          </div>\r\n      </div>\r\n  </body>\r\n  </html>\r\n  `;\r\n};\r\n\r\n// Modal-friendly version (just the content without html/body tags)\r\nexport const generateCertificateModalHTML = (data: CertificateData): string => {\r\n  return `\r\n  <link href=\"https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Playfair+Display:wght@700&display=swap\" rel=\"stylesheet\">\r\n  <style>\r\n      .certificate-modal-container {\r\n          --primary:#4a90e2;\r\n          --accent:#a370e8;\r\n          --text:#2c3e50;\r\n          --muted:#7f8c8d;\r\n          --light:#f0f4f8;\r\n          --white:#ffffff;\r\n          font-family:'Montserrat',sans-serif;\r\n          width: 100%;\r\n          height: 100%;\r\n          background: var(--white);\r\n          position: relative;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          min-height: 500px;\r\n      }\r\n      .certificate-modal{\r\n          width: 100%;\r\n          max-width: 900px;\r\n          aspect-ratio: 1.414/1; /* A4 landscape ratio */\r\n          background:var(--white);\r\n          border-radius:16px;\r\n          box-shadow:0 20px 40px rgba(0,0,0,.08);\r\n          position:relative;\r\n          overflow:hidden;\r\n          padding: 40px 60px;\r\n          margin: 0;\r\n          display: flex;\r\n          flex-direction: column;\r\n          justify-content: space-between;\r\n      }\r\n      .certificate-modal *{box-sizing:border-box;}\r\n      \r\n      .certificate-modal::before,\r\n      .certificate-modal::after{\r\n          content:'';\r\n          position:absolute;\r\n          width:200px;\r\n          height:200px;\r\n          border-radius:50%;\r\n          opacity:.04;\r\n          z-index:0;\r\n      }\r\n      .certificate-modal::before{top:-50px;left:-50px;background:radial-gradient(var(--primary),transparent 70%)}\r\n      .certificate-modal::after{bottom:-50px;right:-50px;background:radial-gradient(var(--accent),transparent 70%)}\r\n\r\n      .certificate-modal .watermark{\r\n          position:absolute;\r\n          top:50%;left:50%;\r\n          transform:translate(-50%,-50%) rotate(-45deg);\r\n          font-family:'Playfair Display',serif;\r\n          font-size:80px;\r\n          color:rgba(0,0,0,.025);\r\n          font-weight:700;\r\n          pointer-events:none;\r\n          z-index:0;\r\n      }\r\n\r\n      .certificate-modal .header{text-align:center;margin-bottom:30px;position:relative;z-index:1;}\r\n      .certificate-modal .title{\r\n          font-family:'Playfair Display',serif;\r\n          font-size:28px;\r\n          color:var(--text);\r\n          margin:0;\r\n      }\r\n      .certificate-modal .subtitle{\r\n          font-size:13px;\r\n          color:var(--muted);\r\n          margin-top:6px;\r\n      }\r\n\r\n      .certificate-modal .main-content{\r\n          text-align:center;\r\n          flex: 1;\r\n          display: flex;\r\n          flex-direction: column;\r\n          justify-content: center;\r\n          position:relative;\r\n          z-index:1;\r\n      }\r\n      .certificate-modal .awarded-to{\r\n          font-size:13px;\r\n          color:var(--muted);\r\n          margin-bottom:6px;\r\n      }\r\n      .certificate-modal .student-name{\r\n          font-family:'Playfair Display',serif;\r\n          font-size:24px;\r\n          color:var(--text);\r\n          position:relative;\r\n          display:inline-block;\r\n          margin-bottom:16px;\r\n      }\r\n      .certificate-modal .student-name::after{\r\n          content:'';\r\n          position:absolute;\r\n          left:50%;\r\n          bottom:-6px;\r\n          transform:translateX(-50%);\r\n          width:80%;\r\n          height:3px;\r\n          background:linear-gradient(90deg,var(--primary),var(--accent));\r\n          border-radius:2px;\r\n      }\r\n      .certificate-modal .completion-text{\r\n          font-size:14px;\r\n          color:#555;\r\n          line-height:1.5;\r\n          max-width:450px;\r\n          margin:0 auto 16px;\r\n      }\r\n      .certificate-modal .course-details{\r\n          display:inline-block;\r\n          background:var(--light);\r\n          border-radius:10px;\r\n          padding:12px 24px;\r\n          box-shadow:0 4px 15px rgba(0,0,0,.05);\r\n          margin-bottom:16px;\r\n      }\r\n      .certificate-modal .course-name{\r\n          font-size:16px;\r\n          font-weight:600;\r\n          color:var(--text);\r\n          margin:0;\r\n      }\r\n      .certificate-modal .course-code{\r\n          font-size:12px;\r\n          color:var(--muted);\r\n          margin-top:3px;\r\n      }\r\n      .certificate-modal .score{\r\n          font-size:15px;\r\n          font-weight:700;\r\n          color:var(--primary);\r\n      }\r\n\r\n      .certificate-modal .footer{\r\n          display:flex;\r\n          justify-content:space-around;\r\n          align-items:flex-end;\r\n          border-top:1px solid #ecf0f1;\r\n          padding-top:20px;\r\n          position:relative;\r\n          z-index:1;\r\n          margin-top: auto;\r\n      }\r\n      .certificate-modal .signature-section{\r\n          text-align:center;\r\n          flex:1;\r\n      }\r\n      .certificate-modal .signature-line{\r\n          width:120px;\r\n          height:1px;\r\n          background:var(--muted);\r\n          margin:0 auto 6px;\r\n      }\r\n      .certificate-modal .signature-label{\r\n          font-size:11px;\r\n          color:var(--muted);\r\n          line-height:1.3;\r\n      }\r\n\r\n      .certificate-modal .id-date-row{\r\n          margin-top:15px;\r\n          display:flex;\r\n          justify-content:space-between;\r\n          font-size:10px;\r\n          color:#95a5a6;\r\n          position:relative;\r\n          z-index:1;\r\n      }\r\n  </style>\r\n  \r\n  <div class=\"certificate-modal-container\">\r\n      <div class=\"certificate-modal\">\r\n          <div class=\"watermark\">TERANG</div>\r\n\r\n          <div class=\"header\">\r\n              <h1 class=\"title\">Sertifikat Kelulusan</h1>\r\n              <p class=\"subtitle\">${data.institutionName}</p>\r\n          </div>\r\n\r\n          <div class=\"main-content\">\r\n              <p class=\"awarded-to\">Dengan bangga mempersembahkan sertifikat ini kepada</p>\r\n              <h2 class=\"student-name\">${data.studentName}</h2>\r\n              <p class=\"completion-text\">\r\n                  karena telah berhasil menyelesaikan dan lulus dari program\r\n              </p>\r\n\r\n              <div class=\"course-details\">\r\n                  <h3 class=\"course-name\">${data.courseName}</h3>\r\n                  <div class=\"course-code\">Kode Kursus: ${data.courseCode}</div>\r\n              </div>\r\n\r\n              <p class=\"score\">Nilai Akhir: ${data.finalScore}%</p>\r\n          </div>\r\n\r\n          <div class=\"footer\">\r\n              <div class=\"signature-section\">\r\n                  <div class=\"signature-line\"></div>\r\n                  <p class=\"signature-label\">${data.instructorName}<br>Instruktur Kursus</p>\r\n              </div>\r\n              <div class=\"signature-section\">\r\n                  <div class=\"signature-line\"></div>\r\n                  <p class=\"signature-label\">Tanggal Kelulusan<br>${data.completionDate}</p>\r\n              </div>\r\n          </div>\r\n\r\n          <div class=\"id-date-row\">\r\n              <span>ID Sertifikat: ${data.certificateId}</span>\r\n              <span>Diterbitkan pada: ${data.completionDate}</span>\r\n          </div>\r\n      </div>\r\n  </div>\r\n  `;\r\n};\r\n\r\nexport const downloadCertificateAsPDF = async (\r\n  data: CertificateData\r\n): Promise<void> => {\r\n  try {\r\n    const htmlContent = generateCertificateHTML(data);\r\n    \r\n    // Call our API endpoint to generate PDF using puppeteer-service\r\n    const response = await fetch('/api/certificates', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ htmlContent }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error('Failed to generate PDF');\r\n    }\r\n\r\n    const blob = await response.blob();\r\n    const url = URL.createObjectURL(blob);\r\n\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = `certificate-${data.certificateId}.pdf`;\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n    URL.revokeObjectURL(url);\r\n  } catch (error) {\r\n    console.error('Error generating PDF:', error);\r\n    // Fallback to downloading as HTML if PDF generation fails\r\n    const htmlContent = generateCertificateHTML(data);\r\n    const blob = new Blob([htmlContent], { type: 'text/html' });\r\n    const url = URL.createObjectURL(blob);\r\n\r\n    const link = document.createElement('a');\r\n    link.href = url;\r\n    link.download = `certificate-${data.certificateId}.html`;\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n    URL.revokeObjectURL(url);\r\n  }\r\n};\r\n\r\nexport const previewCertificate = (data: CertificateData): void => {\r\n  const htmlContent = generateCertificateHTML(data);\r\n  const newWindow = window.open('', '_blank');\r\n  if (newWindow) {\r\n    newWindow.document.write(htmlContent);\r\n    newWindow.document.close();\r\n  }\r\n};\r\n\r\nexport const shareCertificate = async (\r\n  data: CertificateData\r\n): Promise<void> => {\r\n  if (navigator.share) {\r\n    try {\r\n      await navigator.share({\r\n        title: `Certificate of Completion - ${data.courseName}`,\r\n        text: `I've completed ${data.courseName} with a score of ${data.finalScore}%!`,\r\n        url: window.location.href\r\n      });\r\n    } catch (error) {\r\n      console.error('Error sharing certificate:', error);\r\n      // Fallback to copying to clipboard\r\n      copyToClipboard(\r\n        `I've completed ${data.courseName} with a score of ${data.finalScore}%! Certificate ID: ${data.certificateId}`\r\n      );\r\n    }\r\n  } else {\r\n    // Fallback for browsers that don't support Web Share API\r\n    copyToClipboard(\r\n      `I've completed ${data.courseName} with a score of ${data.finalScore}%! Certificate ID: ${data.certificateId}`\r\n    );\r\n  }\r\n};\r\n\r\nconst copyToClipboard = (text: string): void => {\r\n  navigator.clipboard\r\n    .writeText(text)\r\n    .then(() => {\r\n      // You could show a toast notification here\r\n      console.log('Certificate details copied to clipboard');\r\n    })\r\n    .catch((error) => {\r\n      console.error('Failed to copy to clipboard:', error);\r\n    });\r\n};\r\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,sGAAsG;;;;;;;;;AAa/F,MAAM,wBAAwB;IACnC,MAAM,OAAO,IAAI,OAAO,WAAW;IACnC,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAC1C,QAAQ,GACR,QAAQ,CAAC,GAAG;IACf,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,WAAW;AACpC;AAEO,MAAM,0BAA0B,CAAC;IACtC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA+KwB,EAAE,KAAK,eAAe,CAAC;;;;;uCAKlB,EAAE,KAAK,WAAW,CAAC;;;;;;0CAMhB,EAAE,KAAK,UAAU,CAAC;wDACJ,EAAE,KAAK,UAAU,CAAC;;;4CAG9B,EAAE,KAAK,UAAU,CAAC;;;;;;6CAMjB,EAAE,KAAK,cAAc,CAAC;;;;kEAID,EAAE,KAAK,cAAc,CAAC;;;;;mCAKrD,EAAE,KAAK,aAAa,CAAC;sCAClB,EAAE,KAAK,cAAc,CAAC;;;;;EAK1D,CAAC;AACH;AAGO,MAAM,+BAA+B,CAAC;IAC3C,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAuLwB,EAAE,KAAK,eAAe,CAAC;;;;;uCAKlB,EAAE,KAAK,WAAW,CAAC;;;;;;0CAMhB,EAAE,KAAK,UAAU,CAAC;wDACJ,EAAE,KAAK,UAAU,CAAC;;;4CAG9B,EAAE,KAAK,UAAU,CAAC;;;;;;6CAMjB,EAAE,KAAK,cAAc,CAAC;;;;kEAID,EAAE,KAAK,cAAc,CAAC;;;;;mCAKrD,EAAE,KAAK,aAAa,CAAC;sCAClB,EAAE,KAAK,cAAc,CAAC;;;;EAI1D,CAAC;AACH;AAEO,MAAM,2BAA2B,OACtC;IAEA,IAAI;QACF,MAAM,cAAc,wBAAwB;QAE5C,gEAAgE;QAChE,MAAM,WAAW,MAAM,MAAM,qBAAqB;YAChD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,MAAM,MAAM,IAAI,eAAe,CAAC;QAEhC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,CAAC,YAAY,EAAE,KAAK,aAAa,CAAC,IAAI,CAAC;QACvD,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,0DAA0D;QAC1D,MAAM,cAAc,wBAAwB;QAC5C,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAAY;QACzD,MAAM,MAAM,IAAI,eAAe,CAAC;QAEhC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,CAAC,YAAY,EAAE,KAAK,aAAa,CAAC,KAAK,CAAC;QACxD,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,MAAM,cAAc,wBAAwB;IAC5C,MAAM,YAAY,OAAO,IAAI,CAAC,IAAI;IAClC,IAAI,WAAW;QACb,UAAU,QAAQ,CAAC,KAAK,CAAC;QACzB,UAAU,QAAQ,CAAC,KAAK;IAC1B;AACF;AAEO,MAAM,mBAAmB,OAC9B;IAEA,IAAI,UAAU,KAAK,EAAE;QACnB,IAAI;YACF,MAAM,UAAU,KAAK,CAAC;gBACpB,OAAO,CAAC,4BAA4B,EAAE,KAAK,UAAU,EAAE;gBACvD,MAAM,CAAC,eAAe,EAAE,KAAK,UAAU,CAAC,iBAAiB,EAAE,KAAK,UAAU,CAAC,EAAE,CAAC;gBAC9E,KAAK,OAAO,QAAQ,CAAC,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,mCAAmC;YACnC,gBACE,CAAC,eAAe,EAAE,KAAK,UAAU,CAAC,iBAAiB,EAAE,KAAK,UAAU,CAAC,mBAAmB,EAAE,KAAK,aAAa,EAAE;QAElH;IACF,OAAO;QACL,yDAAyD;QACzD,gBACE,CAAC,eAAe,EAAE,KAAK,UAAU,CAAC,iBAAiB,EAAE,KAAK,UAAU,CAAC,mBAAmB,EAAE,KAAK,aAAa,EAAE;IAElH;AACF;AAEA,MAAM,kBAAkB,CAAC;IACvB,UAAU,SAAS,CAChB,SAAS,CAAC,MACV,IAAI,CAAC;QACJ,2CAA2C;QAC3C,QAAQ,GAAG,CAAC;IACd,GACC,KAAK,CAAC,CAAC;QACN,QAAQ,KAAK,CAAC,gCAAgC;IAChD;AACJ", "debugId": null}}, {"offset": {"line": 6928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/lib/auth.ts"], "sourcesContent": ["import { AuthUser } from '@/types/database';\r\n\r\n// Client-side auth utilities\r\nexport const authStorage = {\r\n  setUser: (user: AuthUser) => {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.setItem('auth_user', JSON.stringify(user));\r\n    }\r\n  },\r\n\r\n  getUser: (): AuthUser | null => {\r\n    if (typeof window !== 'undefined') {\r\n      const stored = localStorage.getItem('auth_user');\r\n      return stored ? JSON.parse(stored) : null;\r\n    }\r\n    return null;\r\n  },\r\n\r\n  removeUser: () => {\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.removeItem('auth_user');\r\n    }\r\n  },\r\n\r\n  isAuthenticated: (): boolean => {\r\n    return authStorage.getUser() !== null;\r\n  },\r\n\r\n  hasRole: (role: string): boolean => {\r\n    const user = authStorage.getUser();\r\n    return user?.role === role;\r\n  },\r\n\r\n  isSuperAdmin: (): boolean => {\r\n    return authStorage.hasRole('super_admin');\r\n  },\r\n\r\n  isTeacher: (): boolean => {\r\n    return authStorage.hasRole('teacher');\r\n  },\r\n\r\n  isStudent: (): boolean => {\r\n    return authStorage.hasRole('student');\r\n  }\r\n};\r\n\r\n// Role-based redirect logic\r\nexport const getRedirectPath = (user: AuthUser): string => {\r\n  switch (user.role) {\r\n    case 'super_admin':\r\n      return '/dashboard/admin';\r\n    case 'teacher':\r\n      return '/dashboard/teacher';\r\n    case 'student':\r\n      return '/courses';\r\n    default:\r\n      return '/dashboard';\r\n  }\r\n};\r\n\r\n// Protected route checker\r\nexport const checkAuth = (): AuthUser | null => {\r\n  const user = authStorage.getUser();\r\n  if (!user) {\r\n    // Redirect to sign in if not authenticated\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = '/auth/sign-in';\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n\r\n// Role-based access control\r\nexport const requireRole = (requiredRole: string): AuthUser | null => {\r\n  const user = checkAuth();\r\n  if (!user) return null;\r\n\r\n  if (user.role !== requiredRole) {\r\n    // Redirect to appropriate dashboard if wrong role\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = getRedirectPath(user);\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n\r\n// Multiple roles checker\r\nexport const requireAnyRole = (roles: string[]): AuthUser | null => {\r\n  const user = checkAuth();\r\n  if (!user) return null;\r\n\r\n  if (!roles.includes(user.role)) {\r\n    // Redirect to appropriate dashboard if wrong role\r\n    if (typeof window !== 'undefined') {\r\n      window.location.href = getRedirectPath(user);\r\n    }\r\n    return null;\r\n  }\r\n  return user;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAGO,MAAM,cAAc;IACzB,SAAS,CAAC;QACR,wCAAmC;YACjC,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;QACnD;IACF;IAEA,SAAS;QACP,wCAAmC;YACjC,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU;QACvC;;IAEF;IAEA,YAAY;QACV,wCAAmC;YACjC,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,iBAAiB;QACf,OAAO,YAAY,OAAO,OAAO;IACnC;IAEA,SAAS,CAAC;QACR,MAAM,OAAO,YAAY,OAAO;QAChC,OAAO,MAAM,SAAS;IACxB;IAEA,cAAc;QACZ,OAAO,YAAY,OAAO,CAAC;IAC7B;IAEA,WAAW;QACT,OAAO,YAAY,OAAO,CAAC;IAC7B;IAEA,WAAW;QACT,OAAO,YAAY,OAAO,CAAC;IAC7B;AACF;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAQ,KAAK,IAAI;QACf,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,MAAM,YAAY;IACvB,MAAM,OAAO,YAAY,OAAO;IAChC,IAAI,CAAC,MAAM;QACT,2CAA2C;QAC3C,wCAAmC;YACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;QACA,OAAO;IACT;IACA,OAAO;AACT;AAGO,MAAM,cAAc,CAAC;IAC1B,MAAM,OAAO;IACb,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI,KAAK,IAAI,KAAK,cAAc;QAC9B,kDAAkD;QAClD,wCAAmC;YACjC,OAAO,QAAQ,CAAC,IAAI,GAAG,gBAAgB;QACzC;QACA,OAAO;IACT;IACA,OAAO;AACT;AAGO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,OAAO;IACb,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,GAAG;QAC9B,kDAAkD;QAClD,wCAAmC;YACjC,OAAO,QAAQ,CAAC,IAAI,GAAG,gBAAgB;QACzC;QACA,OAAO;IACT;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 7026, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/tabs/certificate-tab.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Award, CheckCircle, XCircle, Lock, Download, Loader2 } from 'lucide-react';\r\nimport { Course, Institution } from '@/types/lms';\r\nimport { CertificateData, downloadCertificateAsPDF, generateCertificateId } from '@/lib/certificate';\r\nimport { authStorage } from '@/lib/auth';\r\n\r\ninterface CertificateTabProps {\r\n  courseData: Course;\r\n  institution: Institution;\r\n  overallProgress: number;\r\n  onGenerateCertificate: () => void;\r\n  onDownloadPDF?: () => void;\r\n}\r\n\r\nexport const CertificateTab: React.FC<CertificateTabProps> = ({\r\n  courseData,\r\n  institution,\r\n  overallProgress,\r\n  onGenerateCertificate\r\n}) => {\r\n  const [isDownloading, setIsDownloading] = useState(false);\r\n\r\n  const handleDownloadPDF = async () => {\r\n    try {\r\n      setIsDownloading(true);\r\n\r\n      // Get user from local storage\r\n      const user = authStorage.getUser();\r\n\r\n      // Create certificate data\r\n      const certificateData: CertificateData = {\r\n        studentName: user?.name || '<PERSON>', // Use user's name from context\r\n        courseName: courseData.name,\r\n        courseCode: courseData.code,\r\n        completionDate: courseData.certificate.completionDate || new Date().toISOString().split('T')[0],\r\n        finalScore: courseData.finalExam.lastScore || 0,\r\n        instructorName: courseData.instructor,\r\n        institutionName: institution.name,\r\n        certificateId: generateCertificateId()\r\n      };\r\n\r\n      // Use the new PDF download function\r\n      await downloadCertificateAsPDF(certificateData);\r\n    } catch (error) {\r\n      console.error('Error downloading PDF:', error);\r\n      // You could add a toast notification here if you have one\r\n    } finally {\r\n      setIsDownloading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle\r\n          className='flex items-center space-x-2'\r\n          style={{ color: institution.certificateTemplate?.primaryColor }}\r\n        >\r\n          <Award className='h-6 w-6' />\r\n          <span>Sertifikasi Profesional</span>\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className='p-6'>\r\n        {courseData.finalExam.attempts === 0 ? (\r\n          /* User hasn't taken final exam yet */\r\n          <div className='space-y-6 text-center'>\r\n            <div className='rounded-lg border-2 border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50 p-8'>\r\n              <Lock className='mx-auto mb-4 h-16 w-16 text-yellow-600' />\r\n              <h3 className='mb-2 text-2xl font-bold text-yellow-800'>\r\n                Belum Mengikuti Final Exam\r\n              </h3>\r\n              <p className='text-yellow-700'>\r\n                Kamu belum mengikuti final exam. Selesaikan final exam terlebih dahulu untuk mendapatkan sertifikat.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ) : courseData.certificate.isEligible &&\r\n        courseData.certificate.isGenerated ? (\r\n          /* Certificate Generated */\r\n          <div className='space-y-6 text-center'>\r\n            <div className='rounded-lg border-2 border-green-200 bg-gradient-to-r from-green-50 to-blue-50 p-8'>\r\n              <Award className='mx-auto mb-4 h-16 w-16 text-green-600' />\r\n              <h3 className='mb-2 text-2xl font-bold text-green-800'>\r\n                Selamat!\r\n              </h3>\r\n              <p className='text-green-700'>\r\n                Anda telah berhasil menyelesaikan kursus {courseData.name} dan\r\n                memperoleh sertifikasi.\r\n              </p>\r\n              {courseData.certificate.completionDate && (\r\n                <p className='mt-2 text-sm text-green-600'>\r\n                  Diselesaikan pada:{' '}\r\n                  {new Date(\r\n                    courseData.certificate.completionDate\r\n                  ).toLocaleDateString()}\r\n                </p>\r\n              )}\r\n            </div>\r\n            <div className='flex justify-center'>\r\n              <Button\r\n                variant='outline'\r\n                className='border-green-600 text-green-600 hover:bg-green-50'\r\n                onClick={handleDownloadPDF}\r\n                disabled={isDownloading}\r\n              >\r\n                {isDownloading ? (\r\n                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />\r\n                ) : (\r\n                  <Download className='mr-2 h-4 w-4' />\r\n                )}\r\n                {isDownloading ? 'Mengunduh...' : 'Unduh PDF'}\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        ) : courseData.certificate.isEligible ? (\r\n          /* Certificate Ready - Show View and Download */\r\n          <div className='space-y-6 text-center'>\r\n            <div className='rounded-lg border-2 border-green-200 bg-gradient-to-r from-green-50 to-blue-50 p-8'>\r\n              <Award className='mx-auto mb-4 h-16 w-16 text-green-600' />\r\n              <h3 className='mb-2 text-2xl font-bold text-green-800'>\r\n                Selamat!\r\n              </h3>\r\n              <p className='text-green-700'>\r\n                Anda telah berhasil menyelesaikan kursus {courseData.name} dan\r\n                memperoleh sertifikasi.\r\n              </p>\r\n              {courseData.certificate.completionDate && (\r\n                <p className='mt-2 text-sm text-green-600'>\r\n                  Diselesaikan pada:{' '}\r\n                  {new Date(\r\n                    courseData.certificate.completionDate\r\n                  ).toLocaleDateString()}\r\n                </p>\r\n              )}\r\n            </div>\r\n            <div className='flex justify-center'>\r\n              <Button\r\n                variant='outline'\r\n                className='border-green-600 text-green-600 hover:bg-green-50'\r\n                onClick={handleDownloadPDF}\r\n                disabled={isDownloading}\r\n              >\r\n                {isDownloading ? (\r\n                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />\r\n                ) : (\r\n                  <Download className='mr-2 h-4 w-4' />\r\n                )}\r\n                {isDownloading ? 'Mengunduh...' : 'Unduh PDF'}\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          /* Not Eligible Yet */\r\n          <div className='space-y-6'>\r\n            <div className='rounded-lg border-2 border-gray-200 bg-gray-50 p-8 text-center'>\r\n              <Lock className='mx-auto mb-4 h-16 w-16 text-gray-400' />\r\n              <h3 className='mb-2 text-2xl font-bold text-gray-700'>\r\n                Persyaratan Sertifikat\r\n              </h3>\r\n              <p className='mb-4 text-gray-600'>\r\n                Selesaikan semua persyaratan kursus untuk memperoleh sertifikasi\r\n                profesional Anda.\r\n              </p>\r\n            </div>\r\n\r\n            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>\r\n              <div className='space-y-3'>\r\n                <h4 className='font-medium text-gray-900'>Penyelesaian Modul</h4>\r\n                {courseData.modules.map((module) => (\r\n                  <div\r\n                    key={module.id}\r\n                    className='flex items-center justify-between rounded-lg bg-gray-50 p-3'\r\n                  >\r\n                    <span className='text-sm'>{module.title}</span>\r\n                    {module.moduleQuiz.isPassed ? (\r\n                      <CheckCircle className='h-5 w-5 text-green-600' />\r\n                    ) : (\r\n                      <XCircle className='h-5 w-5 text-gray-400' />\r\n                    )}\r\n                  </div>\r\n                ))}\r\n              </div>\r\n              <div className='space-y-3'>\r\n                <h4 className='font-medium text-gray-900'>\r\n                  Persyaratan Akhir\r\n                </h4>\r\n                <div className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>\r\n                  <span className='text-sm'>\r\n                    Final Exam (Min. {courseData.finalExam.minimumScore}%)\r\n                  </span>\r\n                  {courseData.finalExam.isPassed ? (\r\n                    <CheckCircle className='h-5 w-5 text-green-600' />\r\n                  ) : (\r\n                    <XCircle className='h-5 w-5 text-gray-400' />\r\n                  )}\r\n                </div>\r\n                <div className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>\r\n                  <span className='text-sm'>Skor Keseluruhan (Min. 70%)</span>\r\n                  {overallProgress >= 70 ? (\r\n                    <CheckCircle className='h-5 w-5 text-green-600' />\r\n                  ) : (\r\n                    <XCircle className='h-5 w-5 text-gray-400' />\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;;;;;;;;;AAUO,MAAM,iBAAgD,CAAC,EAC5D,UAAU,EACV,WAAW,EACX,eAAe,EACf,qBAAqB,EACtB;;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,oBAAoB;QACxB,IAAI;YACF,iBAAiB;YAEjB,8BAA8B;YAC9B,MAAM,OAAO,qHAAA,CAAA,cAAW,CAAC,OAAO;YAEhC,0BAA0B;YAC1B,MAAM,kBAAmC;gBACvC,aAAa,MAAM,QAAQ;gBAC3B,YAAY,WAAW,IAAI;gBAC3B,YAAY,WAAW,IAAI;gBAC3B,gBAAgB,WAAW,WAAW,CAAC,cAAc,IAAI,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC/F,YAAY,WAAW,SAAS,CAAC,SAAS,IAAI;gBAC9C,gBAAgB,WAAW,UAAU;gBACrC,iBAAiB,YAAY,IAAI;gBACjC,eAAe,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD;YACrC;YAEA,oCAAoC;YACpC,MAAM,CAAA,GAAA,4HAAA,CAAA,2BAAwB,AAAD,EAAE;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,0DAA0D;QAC5D,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBACR,WAAU;oBACV,OAAO;wBAAE,OAAO,YAAY,mBAAmB,EAAE;oBAAa;;sCAE9D,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;sCAAK;;;;;;;;;;;;;;;;;0BAGV,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACpB,WAAW,SAAS,CAAC,QAAQ,KAAK,IACjC,oCAAoC,iBACpC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAG,WAAU;0CAA0C;;;;;;0CAGxD,6LAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;;;;;2BAKjC,WAAW,WAAW,CAAC,UAAU,IACrC,WAAW,WAAW,CAAC,WAAW,GAChC,yBAAyB,iBACzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;;wCAAiB;wCACc,WAAW,IAAI;wCAAC;;;;;;;gCAG3D,WAAW,WAAW,CAAC,cAAc,kBACpC,6LAAC;oCAAE,WAAU;;wCAA8B;wCACtB;wCAClB,IAAI,KACH,WAAW,WAAW,CAAC,cAAc,EACrC,kBAAkB;;;;;;;;;;;;;sCAI1B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;gCACT,UAAU;;oCAET,8BACC,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAErB,gBAAgB,iBAAiB;;;;;;;;;;;;;;;;;2BAItC,WAAW,WAAW,CAAC,UAAU,GACnC,8CAA8C,iBAC9C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;;wCAAiB;wCACc,WAAW,IAAI;wCAAC;;;;;;;gCAG3D,WAAW,WAAW,CAAC,cAAc,kBACpC,6LAAC;oCAAE,WAAU;;wCAA8B;wCACtB;wCAClB,IAAI,KACH,WAAW,WAAW,CAAC,cAAc,EACrC,kBAAkB;;;;;;;;;;;;;sCAI1B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;gCACT,UAAU;;oCAET,8BACC,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAErB,gBAAgB,iBAAiB;;;;;;;;;;;;;;;;;2BAKxC,oBAAoB,iBACpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;;;;;;;sCAMpC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;wCACzC,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,uBACvB,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAW,OAAO,KAAK;;;;;;oDACtC,OAAO,UAAU,CAAC,QAAQ,iBACzB,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,6LAAC,+MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;+CAPhB,OAAO,EAAE;;;;;;;;;;;8CAYpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAG1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;;wDAAU;wDACN,WAAW,SAAS,CAAC,YAAY;wDAAC;;;;;;;gDAErD,WAAW,SAAS,CAAC,QAAQ,iBAC5B,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAEvB,6LAAC,+MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;sDAGvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAU;;;;;;gDACzB,mBAAmB,mBAClB,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAEvB,6LAAC,+MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvC;GArMa;KAAA", "debugId": null}}, {"offset": {"line": 7532, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/grades-tab.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport {\r\n  GameIcon as GradeIcon,\r\n  CheckmarkCircle01Icon as CheckCircleIcon,\r\n  Cancel01Icon as XCircleIcon,\r\n  Clock01Icon as ClockIcon\r\n} from 'hugeicons-react';\r\nimport { Course } from '@/types/lms';\r\n\r\ninterface GradesTabProps {\r\n  courseData: Course;\r\n}\r\n\r\nconst GradesTab: React.FC<GradesTabProps> = ({ courseData }) => {\r\n  const getGradeColor = (score: number, minimumScore: number) => {\r\n    if (score >= minimumScore) {\r\n      return 'text-green-600';\r\n    } else if (score >= minimumScore * 0.7) {\r\n      return 'text-yellow-600';\r\n    } else {\r\n      return 'text-red-600';\r\n    }\r\n  };\r\n\r\n  const getGradeBadgeVariant = (score: number, minimumScore: number) => {\r\n    if (score >= minimumScore) {\r\n      return 'default';\r\n    } else if (score >= minimumScore * 0.7) {\r\n      return 'secondary';\r\n    } else {\r\n      return 'destructive';\r\n    }\r\n  };\r\n\r\n  const getStatusIcon = (isPassed: boolean, attempts: number, maxAttempts: number) => {\r\n    if (isPassed) {\r\n      return <CheckCircleIcon className=\"h-4 w-4 text-green-600\" />;\r\n    } else if (attempts >= maxAttempts) {\r\n      return <XCircleIcon className=\"h-4 w-4 text-red-600\" />;\r\n    } else {\r\n      return <ClockIcon className=\"h-4 w-4 text-yellow-600\" />;\r\n    }\r\n  };\r\n\r\n  const getStatusText = (isPassed: boolean, attempts: number, maxAttempts: number) => {\r\n    if (isPassed) {\r\n      return 'Lulus';\r\n    } else if (attempts >= maxAttempts) {\r\n      return 'Tidak Lulus';\r\n    } else {\r\n      return 'Belum Selesai';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <GradeIcon className=\"h-5 w-5\" />\r\n            Nilai Kursus\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-6\">\r\n          {/* Overall Progress */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">Kemajuan Keseluruhan</h3>\r\n            <div className=\"space-y-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <span className=\"text-sm font-medium\">Progress Keseluruhan</span>\r\n                <span className=\"text-sm text-gray-500\">\r\n                  {Math.round(courseData.totalProgress)}%\r\n                </span>\r\n              </div>\r\n              <Progress value={courseData.totalProgress} className=\"h-3\" />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Chapter Quizzes */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">Kuis Bab</h3>\r\n            <div className=\"space-y-3\">\r\n              {courseData.modules.map((module) =>\r\n                module.chapters.map((chapter) => (\r\n                  <Card key={chapter.id} className=\"p-4\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div className=\"flex-1\">\r\n                        <div className=\"flex items-center gap-2 mb-1\">\r\n                          {getStatusIcon(\r\n                            chapter.quiz.isPassed,\r\n                            chapter.quiz.attempts,\r\n                            chapter.quiz.maxAttempts\r\n                          )}\r\n                          <span className=\"font-medium\">{chapter.title}</span>\r\n                          <Badge variant=\"outline\" className=\"text-xs\">\r\n                            {module.title}\r\n                          </Badge>\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-600\">\r\n                          Percobaan: {chapter.quiz.attempts}/{chapter.quiz.maxAttempts}\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"text-right\">\r\n                        <div className={`text-lg font-bold ${getGradeColor(\r\n                          chapter.quiz.lastScore || 0,\r\n                          chapter.quiz.minimumScore\r\n                        )}`}>\r\n                          {chapter.quiz.lastScore || 0}%\r\n                        </div>\r\n                        <Badge\r\n                          variant={getGradeBadgeVariant(\r\n                            chapter.quiz.lastScore || 0,\r\n                            chapter.quiz.minimumScore\r\n                          )}\r\n                          className=\"text-xs\"\r\n                        >\r\n                          {getStatusText(\r\n                            chapter.quiz.isPassed,\r\n                            chapter.quiz.attempts,\r\n                            chapter.quiz.maxAttempts\r\n                          )}\r\n                        </Badge>\r\n                      </div>\r\n                    </div>\r\n                  </Card>\r\n                ))\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Module Quizzes */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">Kuis Modul</h3>\r\n            <div className=\"space-y-3\">\r\n              {courseData.modules.map((module) => (\r\n                <Card key={module.id} className=\"p-4\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"flex items-center gap-2 mb-1\">\r\n                        {getStatusIcon(\r\n                          module.moduleQuiz.isPassed,\r\n                          module.moduleQuiz.attempts,\r\n                          module.moduleQuiz.maxAttempts\r\n                        )}\r\n                        <span className=\"font-medium\">{module.title}</span>\r\n                      </div>\r\n                      <div className=\"text-sm text-gray-600\">\r\n                        Percobaan: {module.moduleQuiz.attempts}/{module.moduleQuiz.maxAttempts}\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"text-right\">\r\n                      <div className={`text-lg font-bold ${getGradeColor(\r\n                        module.moduleQuiz.lastScore || 0,\r\n                        module.moduleQuiz.minimumScore\r\n                      )}`}>\r\n                        {module.moduleQuiz.lastScore || 0}%\r\n                      </div>\r\n                      <Badge\r\n                        variant={getGradeBadgeVariant(\r\n                          module.moduleQuiz.lastScore || 0,\r\n                          module.moduleQuiz.minimumScore\r\n                        )}\r\n                        className=\"text-xs\"\r\n                      >\r\n                        {getStatusText(\r\n                          module.moduleQuiz.isPassed,\r\n                          module.moduleQuiz.attempts,\r\n                          module.moduleQuiz.maxAttempts\r\n                        )}\r\n                      </Badge>\r\n                    </div>\r\n                  </div>\r\n                </Card>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Final Exam */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">Ujian Akhir</h3>\r\n            <Card className=\"p-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex-1\">\r\n                  <div className=\"flex items-center gap-2 mb-1\">\r\n                    {getStatusIcon(\r\n                      courseData.finalExam.isPassed,\r\n                      courseData.finalExam.attempts,\r\n                      courseData.finalExam.maxAttempts\r\n                    )}\r\n                    <span className=\"font-medium\">Ujian Akhir</span>\r\n                  </div>\r\n                  <div className=\"text-sm text-gray-600\">\r\n                    Percobaan: {courseData.finalExam.attempts}/{courseData.finalExam.maxAttempts}\r\n                  </div>\r\n                </div>\r\n                <div className=\"text-right\">\r\n                  <div className={`text-lg font-bold ${getGradeColor(\r\n                    courseData.finalExam.lastScore || 0,\r\n                    courseData.finalExam.minimumScore\r\n                  )}`}>\r\n                    {courseData.finalExam.lastScore || 0}%\r\n                  </div>\r\n                  <Badge\r\n                    variant={getGradeBadgeVariant(\r\n                      courseData.finalExam.lastScore || 0,\r\n                      courseData.finalExam.minimumScore\r\n                    )}\r\n                    className=\"text-xs\"\r\n                  >\r\n                    {getStatusText(\r\n                      courseData.finalExam.isPassed,\r\n                      courseData.finalExam.attempts,\r\n                      courseData.finalExam.maxAttempts\r\n                    )}\r\n                  </Badge>\r\n                </div>\r\n              </div>\r\n            </Card>\r\n          </div>\r\n\r\n          {/* Certificate Status */}\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold mb-4\">Status Sertifikat</h3>\r\n            <Card className=\"p-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  {courseData.certificate.isEligible ? (\r\n                    <CheckCircleIcon className=\"h-5 w-5 text-green-600\" />\r\n                  ) : (\r\n                    <XCircleIcon className=\"h-5 w-5 text-gray-400\" />\r\n                  )}\r\n                  <span className=\"font-medium\">Sertifikat</span>\r\n                </div>\r\n                <Badge\r\n                  variant={courseData.certificate.isEligible ? 'default' : 'secondary'}\r\n                >\r\n                  {courseData.certificate.isEligible ? 'Eligible' : 'Not Eligible'}\r\n                </Badge>\r\n              </div>\r\n            </Card>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { GradesTab };\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;AAkBA,MAAM,YAAsC,CAAC,EAAE,UAAU,EAAE;IACzD,MAAM,gBAAgB,CAAC,OAAe;QACpC,IAAI,SAAS,cAAc;YACzB,OAAO;QACT,OAAO,IAAI,SAAS,eAAe,KAAK;YACtC,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,MAAM,uBAAuB,CAAC,OAAe;QAC3C,IAAI,SAAS,cAAc;YACzB,OAAO;QACT,OAAO,IAAI,SAAS,eAAe,KAAK;YACtC,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,MAAM,gBAAgB,CAAC,UAAmB,UAAkB;QAC1D,IAAI,UAAU;YACZ,qBAAO,6LAAC,6OAAA,CAAA,wBAAe;gBAAC,WAAU;;;;;;QACpC,OAAO,IAAI,YAAY,aAAa;YAClC,qBAAO,6LAAC,0NAAA,CAAA,eAAW;gBAAC,WAAU;;;;;;QAChC,OAAO;YACL,qBAAO,6LAAC,wNAAA,CAAA,cAAS;gBAAC,WAAU;;;;;;QAC9B;IACF;IAEA,MAAM,gBAAgB,CAAC,UAAmB,UAAkB;QAC1D,IAAI,UAAU;YACZ,OAAO;QACT,OAAO,IAAI,YAAY,aAAa;YAClC,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,iNAAA,CAAA,WAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIrC,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;oDAAK,WAAU;;wDACb,KAAK,KAAK,CAAC,WAAW,aAAa;wDAAE;;;;;;;;;;;;;sDAG1C,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,OAAO,WAAW,aAAa;4CAAE,WAAU;;;;;;;;;;;;;;;;;;sCAKzD,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CACZ,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,SACvB,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACnB,6LAAC,mIAAA,CAAA,OAAI;gDAAkB,WAAU;0DAC/B,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEACZ,cACC,QAAQ,IAAI,CAAC,QAAQ,EACrB,QAAQ,IAAI,CAAC,QAAQ,EACrB,QAAQ,IAAI,CAAC,WAAW;sFAE1B,6LAAC;4EAAK,WAAU;sFAAe,QAAQ,KAAK;;;;;;sFAC5C,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;sFAChC,OAAO,KAAK;;;;;;;;;;;;8EAGjB,6LAAC;oEAAI,WAAU;;wEAAwB;wEACzB,QAAQ,IAAI,CAAC,QAAQ;wEAAC;wEAAE,QAAQ,IAAI,CAAC,WAAW;;;;;;;;;;;;;sEAGhE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAW,CAAC,kBAAkB,EAAE,cACnC,QAAQ,IAAI,CAAC,SAAS,IAAI,GAC1B,QAAQ,IAAI,CAAC,YAAY,GACxB;;wEACA,QAAQ,IAAI,CAAC,SAAS,IAAI;wEAAE;;;;;;;8EAE/B,6LAAC,oIAAA,CAAA,QAAK;oEACJ,SAAS,qBACP,QAAQ,IAAI,CAAC,SAAS,IAAI,GAC1B,QAAQ,IAAI,CAAC,YAAY;oEAE3B,WAAU;8EAET,cACC,QAAQ,IAAI,CAAC,QAAQ,EACrB,QAAQ,IAAI,CAAC,QAAQ,EACrB,QAAQ,IAAI,CAAC,WAAW;;;;;;;;;;;;;;;;;;+CAnCvB,QAAQ,EAAE;;;;;;;;;;;;;;;;sCA+C7B,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CACZ,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,uBACvB,6LAAC,mIAAA,CAAA,OAAI;4CAAiB,WAAU;sDAC9B,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;oEACZ,cACC,OAAO,UAAU,CAAC,QAAQ,EAC1B,OAAO,UAAU,CAAC,QAAQ,EAC1B,OAAO,UAAU,CAAC,WAAW;kFAE/B,6LAAC;wEAAK,WAAU;kFAAe,OAAO,KAAK;;;;;;;;;;;;0EAE7C,6LAAC;gEAAI,WAAU;;oEAAwB;oEACzB,OAAO,UAAU,CAAC,QAAQ;oEAAC;oEAAE,OAAO,UAAU,CAAC,WAAW;;;;;;;;;;;;;kEAG1E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAW,CAAC,kBAAkB,EAAE,cACnC,OAAO,UAAU,CAAC,SAAS,IAAI,GAC/B,OAAO,UAAU,CAAC,YAAY,GAC7B;;oEACA,OAAO,UAAU,CAAC,SAAS,IAAI;oEAAE;;;;;;;0EAEpC,6LAAC,oIAAA,CAAA,QAAK;gEACJ,SAAS,qBACP,OAAO,UAAU,CAAC,SAAS,IAAI,GAC/B,OAAO,UAAU,CAAC,YAAY;gEAEhC,WAAU;0EAET,cACC,OAAO,UAAU,CAAC,QAAQ,EAC1B,OAAO,UAAU,CAAC,QAAQ,EAC1B,OAAO,UAAU,CAAC,WAAW;;;;;;;;;;;;;;;;;;2CAhC5B,OAAO,EAAE;;;;;;;;;;;;;;;;sCA2C1B,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,cACC,WAAW,SAAS,CAAC,QAAQ,EAC7B,WAAW,SAAS,CAAC,QAAQ,EAC7B,WAAW,SAAS,CAAC,WAAW;0EAElC,6LAAC;gEAAK,WAAU;0EAAc;;;;;;;;;;;;kEAEhC,6LAAC;wDAAI,WAAU;;4DAAwB;4DACzB,WAAW,SAAS,CAAC,QAAQ;4DAAC;4DAAE,WAAW,SAAS,CAAC,WAAW;;;;;;;;;;;;;0DAGhF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,kBAAkB,EAAE,cACnC,WAAW,SAAS,CAAC,SAAS,IAAI,GAClC,WAAW,SAAS,CAAC,YAAY,GAChC;;4DACA,WAAW,SAAS,CAAC,SAAS,IAAI;4DAAE;;;;;;;kEAEvC,6LAAC,oIAAA,CAAA,QAAK;wDACJ,SAAS,qBACP,WAAW,SAAS,CAAC,SAAS,IAAI,GAClC,WAAW,SAAS,CAAC,YAAY;wDAEnC,WAAU;kEAET,cACC,WAAW,SAAS,CAAC,QAAQ,EAC7B,WAAW,SAAS,CAAC,QAAQ,EAC7B,WAAW,SAAS,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS5C,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,WAAW,WAAW,CAAC,UAAU,iBAChC,6LAAC,6OAAA,CAAA,wBAAe;wDAAC,WAAU;;;;;6EAE3B,6LAAC,0NAAA,CAAA,eAAW;wDAAC,WAAU;;;;;;kEAEzB,6LAAC;wDAAK,WAAU;kEAAc;;;;;;;;;;;;0DAEhC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,SAAS,WAAW,WAAW,CAAC,UAAU,GAAG,YAAY;0DAExD,WAAW,WAAW,CAAC,UAAU,GAAG,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE;KAxOM", "debugId": null}}, {"offset": {"line": 8122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/course-material-tab.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport {\r\n  BookOpen01Icon as BookOpenIcon,\r\n  PlayIcon,\r\n  CheckmarkCircle01Icon as CheckCircleIcon,\r\n  Locker01Icon as LockIcon,\r\n  Clock01Icon as ClockIcon\r\n} from 'hugeicons-react';\r\nimport { Course } from '@/types/lms';\r\n\r\ninterface CourseMaterialTabProps {\r\n  courseData: Course;\r\n  onStartLearning: () => void;\r\n}\r\n\r\nconst CourseMaterialTab: React.FC<CourseMaterialTabProps> = ({ \r\n  courseData, \r\n  onStartLearning \r\n}) => {\r\n  const getModuleProgress = (module: any) => {\r\n    const totalChapters = module.chapters.length;\r\n    const completedChapters = module.chapters.filter(\r\n      (chapter: any) => chapter.contents.every((content: any) => content.isCompleted) && chapter.quiz.isPassed\r\n    ).length;\r\n    return totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0;\r\n  };\r\n\r\n  const getChapterStatus = (chapter: any) => {\r\n    const allContentsCompleted = chapter.contents.every((content: any) => content.isCompleted);\r\n    const quizPassed = chapter.quiz.isPassed;\r\n    \r\n    if (allContentsCompleted && quizPassed) {\r\n      return 'completed';\r\n    } else if (chapter.isUnlocked) {\r\n      return 'in-progress';\r\n    } else {\r\n      return 'locked';\r\n    }\r\n  };\r\n\r\n  const getStatusIcon = (status: string) => {\r\n    switch (status) {\r\n      case 'completed':\r\n        return <CheckCircleIcon className=\"h-4 w-4 text-green-600\" />;\r\n      case 'in-progress':\r\n        return <ClockIcon className=\"h-4 w-4 text-blue-600\" />;\r\n      case 'locked':\r\n        return <LockIcon className=\"h-4 w-4 text-gray-400\" />;\r\n      default:\r\n        return <ClockIcon className=\"h-4 w-4 text-gray-400\" />;\r\n    }\r\n  };\r\n\r\n  const getStatusText = (status: string) => {\r\n    switch (status) {\r\n      case 'completed':\r\n        return 'Selesai';\r\n      case 'in-progress':\r\n        return 'Sedang Belajar';\r\n      case 'locked':\r\n        return 'Terkunci';\r\n      default:\r\n        return 'Belum Dimulai';\r\n    }\r\n  };\r\n\r\n  const getStatusBadgeVariant = (status: string) => {\r\n    switch (status) {\r\n      case 'completed':\r\n        return 'default';\r\n      case 'in-progress':\r\n        return 'secondary';\r\n      case 'locked':\r\n        return 'outline';\r\n      default:\r\n        return 'outline';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <BookOpenIcon className=\"h-5 w-5\" />\r\n            Materi Kursus\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-6\">\r\n          {/* Course Overview */}\r\n          <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n            <h3 className=\"font-semibold mb-2\">{courseData.name}</h3>\r\n            <p className=\"text-sm text-gray-600 mb-3\">{courseData.description}</p>\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"text-sm text-gray-500\">\r\n                {courseData.modules.length} Modul • {courseData.modules.reduce((total, module) => total + module.chapters.length, 0)} Bab\r\n              </div>\r\n              <Button onClick={onStartLearning} className=\"flex items-center gap-2\">\r\n                <PlayIcon className=\"h-4 w-4\" />\r\n                Mulai Pembelajaran\r\n              </Button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Modules */}\r\n          <div className=\"space-y-4\">\r\n            {courseData.modules.map((module, moduleIndex) => {\r\n              const moduleProgress = getModuleProgress(module);\r\n              const isModuleUnlocked = module.isUnlocked;\r\n              \r\n              return (\r\n                <Card key={module.id} className={`${!isModuleUnlocked ? 'opacity-60' : ''}`}>\r\n                  <CardHeader>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div className=\"flex items-center gap-3\">\r\n                        <div className=\"flex items-center gap-2\">\r\n                          {isModuleUnlocked ? (\r\n                            <CheckCircleIcon className=\"h-5 w-5 text-green-600\" />\r\n                          ) : (\r\n                            <LockIcon className=\"h-5 w-5 text-gray-400\" />\r\n                          )}\r\n                          <CardTitle className=\"text-lg\">\r\n                            Modul {moduleIndex + 1}: {module.title}\r\n                          </CardTitle>\r\n                        </div>\r\n                        <Badge variant={isModuleUnlocked ? 'default' : 'outline'}>\r\n                          {isModuleUnlocked ? 'Terbuka' : 'Terkunci'}\r\n                        </Badge>\r\n                      </div>\r\n                      <div className=\"text-right\">\r\n                        <div className=\"text-sm text-gray-500\">Progress</div>\r\n                        <div className=\"text-sm font-medium\">{Math.round(moduleProgress)}%</div>\r\n                      </div>\r\n                    </div>\r\n                    <p className=\"text-sm text-gray-600 mt-2\">{module.description}</p>\r\n                    <Progress value={moduleProgress} className=\"mt-3 h-2\" />\r\n                  </CardHeader>\r\n                  <CardContent>\r\n                    <div className=\"space-y-3\">\r\n                      {module.chapters.map((chapter, chapterIndex) => {\r\n                        const chapterStatus = getChapterStatus(chapter);\r\n                        const statusIcon = getStatusIcon(chapterStatus);\r\n                        const statusText = getStatusText(chapterStatus);\r\n                        const statusBadgeVariant = getStatusBadgeVariant(chapterStatus);\r\n                        \r\n                        return (\r\n                          <div key={chapter.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\r\n                            <div className=\"flex items-center gap-3\">\r\n                              {statusIcon}\r\n                              <div>\r\n                                <div className=\"font-medium text-sm\">\r\n                                  Bab {chapterIndex + 1}: {chapter.title}\r\n                                </div>\r\n                                <div className=\"text-xs text-gray-500\">\r\n                                  {chapter.contents.length} konten\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"flex items-center gap-2\">\r\n                              <Badge variant={statusBadgeVariant} className=\"text-xs\">\r\n                                {statusText}\r\n                              </Badge>\r\n                              <div className=\"text-xs text-gray-500\">\r\n                                {Math.round(chapter.completionPercentage)}%\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              );\r\n            })}\r\n          </div>\r\n\r\n          {/* Final Exam */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <BookOpenIcon className=\"h-5 w-5\" />\r\n                Ujian Akhir\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <ClockIcon className=\"h-4 w-4 text-blue-600\" />\r\n                  <div>\r\n                    <div className=\"font-medium\">Ujian Akhir</div>\r\n                    <div className=\"text-sm text-gray-500\">\r\n                      {courseData.finalExam.questions.length} soal\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <Badge variant=\"outline\">\r\n                  {courseData.finalExam.isPassed ? 'Lulus' : 'Belum Lulus'}\r\n                </Badge>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Certificate */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <BookOpenIcon className=\"h-5 w-5\" />\r\n                Sertifikat\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  {courseData.certificate.isEligible ? (\r\n                    <CheckCircleIcon className=\"h-4 w-4 text-green-600\" />\r\n                  ) : (\r\n                    <ClockIcon className=\"h-4 w-4 text-gray-400\" />\r\n                  )}\r\n                  <div>\r\n                    <div className=\"font-medium\">Sertifikat Penyelesaian</div>\r\n                    <div className=\"text-sm text-gray-500\">\r\n                      {courseData.certificate.isEligible ? 'Eligible' : 'Not Eligible'}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <Badge variant={courseData.certificate.isEligible ? 'default' : 'outline'}>\r\n                  {courseData.certificate.isEligible ? 'Tersedia' : 'Belum Tersedia'}\r\n                </Badge>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport { CourseMaterialTab };\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAqBA,MAAM,oBAAsD,CAAC,EAC3D,UAAU,EACV,eAAe,EAChB;IACC,MAAM,oBAAoB,CAAC;QACzB,MAAM,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QAC5C,MAAM,oBAAoB,OAAO,QAAQ,CAAC,MAAM,CAC9C,CAAC,UAAiB,QAAQ,QAAQ,CAAC,KAAK,CAAC,CAAC,UAAiB,QAAQ,WAAW,KAAK,QAAQ,IAAI,CAAC,QAAQ,EACxG,MAAM;QACR,OAAO,gBAAgB,IAAI,AAAC,oBAAoB,gBAAiB,MAAM;IACzE;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,uBAAuB,QAAQ,QAAQ,CAAC,KAAK,CAAC,CAAC,UAAiB,QAAQ,WAAW;QACzF,MAAM,aAAa,QAAQ,IAAI,CAAC,QAAQ;QAExC,IAAI,wBAAwB,YAAY;YACtC,OAAO;QACT,OAAO,IAAI,QAAQ,UAAU,EAAE;YAC7B,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,6OAAA,CAAA,wBAAe;oBAAC,WAAU;;;;;;YACpC,KAAK;gBACH,qBAAO,6LAAC,wNAAA,CAAA,cAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,0NAAA,CAAA,eAAQ;oBAAC,WAAU;;;;;;YAC7B;gBACE,qBAAO,6LAAC,wNAAA,CAAA,cAAS;oBAAC,WAAU;;;;;;QAChC;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,+NAAA,CAAA,iBAAY;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIxC,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsB,WAAW,IAAI;;;;;;8CACnD,6LAAC;oCAAE,WAAU;8CAA8B,WAAW,WAAW;;;;;;8CACjE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDACZ,WAAW,OAAO,CAAC,MAAM;gDAAC;gDAAU,WAAW,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,SAAW,QAAQ,OAAO,QAAQ,CAAC,MAAM,EAAE;gDAAG;;;;;;;sDAEvH,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAiB,WAAU;;8DAC1C,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;sCAOtC,6LAAC;4BAAI,WAAU;sCACZ,WAAW,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;gCAC/B,MAAM,iBAAiB,kBAAkB;gCACzC,MAAM,mBAAmB,OAAO,UAAU;gCAE1C,qBACE,6LAAC,mIAAA,CAAA,OAAI;oCAAiB,WAAW,GAAG,CAAC,mBAAmB,eAAe,IAAI;;sDACzE,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEACZ,iCACC,6LAAC,6OAAA,CAAA,wBAAe;4EAAC,WAAU;;;;;iGAE3B,6LAAC,0NAAA,CAAA,eAAQ;4EAAC,WAAU;;;;;;sFAEtB,6LAAC,mIAAA,CAAA,YAAS;4EAAC,WAAU;;gFAAU;gFACtB,cAAc;gFAAE;gFAAG,OAAO,KAAK;;;;;;;;;;;;;8EAG1C,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAS,mBAAmB,YAAY;8EAC5C,mBAAmB,YAAY;;;;;;;;;;;;sEAGpC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;8EACvC,6LAAC;oEAAI,WAAU;;wEAAuB,KAAK,KAAK,CAAC;wEAAgB;;;;;;;;;;;;;;;;;;;8DAGrE,6LAAC;oDAAE,WAAU;8DAA8B,OAAO,WAAW;;;;;;8DAC7D,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,OAAO;oDAAgB,WAAU;;;;;;;;;;;;sDAE7C,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;0DACZ,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS;oDAC7B,MAAM,gBAAgB,iBAAiB;oDACvC,MAAM,aAAa,cAAc;oDACjC,MAAM,aAAa,cAAc;oDACjC,MAAM,qBAAqB,sBAAsB;oDAEjD,qBACE,6LAAC;wDAAqB,WAAU;;0EAC9B,6LAAC;gEAAI,WAAU;;oEACZ;kFACD,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;;oFAAsB;oFAC9B,eAAe;oFAAE;oFAAG,QAAQ,KAAK;;;;;;;0FAExC,6LAAC;gFAAI,WAAU;;oFACZ,QAAQ,QAAQ,CAAC,MAAM;oFAAC;;;;;;;;;;;;;;;;;;;0EAI/B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAS;wEAAoB,WAAU;kFAC3C;;;;;;kFAEH,6LAAC;wEAAI,WAAU;;4EACZ,KAAK,KAAK,CAAC,QAAQ,oBAAoB;4EAAE;;;;;;;;;;;;;;uDAjBtC,QAAQ,EAAE;;;;;gDAsBxB;;;;;;;;;;;;mCAzDK,OAAO,EAAE;;;;;4BA8DxB;;;;;;sCAIF,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,+NAAA,CAAA,iBAAY;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIxC,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,wNAAA,CAAA,cAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAc;;;;;;0EAC7B,6LAAC;gEAAI,WAAU;;oEACZ,WAAW,SAAS,CAAC,SAAS,CAAC,MAAM;oEAAC;;;;;;;;;;;;;;;;;;;0DAI7C,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DACZ,WAAW,SAAS,CAAC,QAAQ,GAAG,UAAU;;;;;;;;;;;;;;;;;;;;;;;sCAOnD,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,+NAAA,CAAA,iBAAY;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAIxC,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,WAAW,WAAW,CAAC,UAAU,iBAChC,6LAAC,6OAAA,CAAA,wBAAe;wDAAC,WAAU;;;;;6EAE3B,6LAAC,wNAAA,CAAA,cAAS;wDAAC,WAAU;;;;;;kEAEvB,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAc;;;;;;0EAC7B,6LAAC;gEAAI,WAAU;0EACZ,WAAW,WAAW,CAAC,UAAU,GAAG,aAAa;;;;;;;;;;;;;;;;;;0DAIxD,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAS,WAAW,WAAW,CAAC,UAAU,GAAG,YAAY;0DAC7D,WAAW,WAAW,CAAC,UAAU,GAAG,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE;KA5NM", "debugId": null}}, {"offset": {"line": 8774, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/final-exam/option.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { ContentBlock } from '@/components/dynamic-content-editor';\r\n\r\ninterface OptionProps {\r\n  option: string | { content: ContentBlock[]; isCorrect: boolean };\r\n  index: number;\r\n  questionId: string;\r\n  selectedAnswer: number | string | number[] | undefined;\r\n  onAnswerChange: (questionId: string, answer: number | string | number[]) => void;\r\n  type: 'radio' | 'checkbox';\r\n  disabled?: boolean;\r\n  showResults?: boolean;\r\n  correctAnswer?: number | string | number[];\r\n  isCorrect?: boolean;\r\n}\r\n\r\nexport const Option: React.FC<OptionProps> = ({\r\n  option,\r\n  index,\r\n  questionId,\r\n  selectedAnswer,\r\n  onAnswerChange,\r\n  type = 'radio',\r\n  disabled = false,\r\n  showResults = false,\r\n  correctAnswer,\r\n  isCorrect\r\n}) => {\r\n  const isSelected = type === 'radio' \r\n    ? selectedAnswer === index \r\n    : Array.isArray(selectedAnswer) && selectedAnswer.includes(index);\r\n\r\n  const isCorrectOption = type === 'radio' \r\n    ? correctAnswer === index \r\n    : Array.isArray(correctAnswer) && correctAnswer.includes(index);\r\n\r\n  const handleChange = () => {\r\n    if (disabled) return;\r\n    \r\n    if (type === 'radio') {\r\n      onAnswerChange(questionId, index);\r\n    } else {\r\n      // Handle checkbox logic for multiple selection\r\n      const currentAnswers = Array.isArray(selectedAnswer) ? selectedAnswer : [];\r\n      const newAnswers = isSelected \r\n        ? currentAnswers.filter((ans) => ans !== index)\r\n        : [...currentAnswers, index];\r\n      onAnswerChange(questionId, newAnswers);\r\n    }\r\n  };\r\n\r\n  const getOptionStyles = () => {\r\n    if (!showResults) {\r\n      // Normal mode - just show selected state\r\n      return isSelected \r\n        ? 'border-blue-500 bg-blue-50' \r\n        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50';\r\n    }\r\n\r\n    // Results mode - show correct/incorrect highlighting\r\n    if (isCorrectOption) {\r\n      // This is the correct answer - always highlight green\r\n      return 'border-green-500 bg-green-50';\r\n    } else if (isSelected) {\r\n      // User selected this wrong answer - highlight red\r\n      return 'border-red-500 bg-red-50';\r\n    } else {\r\n      // Not selected, not correct - neutral\r\n      return 'border-gray-200 bg-gray-50';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <label \r\n      className={`\r\n        flex cursor-pointer items-start space-x-3 p-3 rounded-lg border-2 transition-all\r\n        ${getOptionStyles()}\r\n        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}\r\n        ${showResults && isCorrectOption ? 'ring-2 ring-green-200' : ''}\r\n        ${showResults && isSelected && !isCorrectOption ? 'ring-2 ring-red-200' : ''}\r\n      `}\r\n    >\r\n      <input\r\n        type={type}\r\n        name={questionId}\r\n        value={index}\r\n        checked={isSelected}\r\n        onChange={handleChange}\r\n        disabled={disabled}\r\n        className={`\r\n          mt-0.5 h-4 w-4 shrink-0\r\n          ${type === 'radio' ? 'text-blue-600' : 'text-blue-600 rounded'}\r\n          ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}\r\n        `}\r\n      />\r\n      <span className={`text-sm leading-relaxed flex-1 ${disabled ? 'text-gray-500' : 'text-gray-900'}`}>\r\n        <span className=\"font-medium mr-2\">\r\n          {String.fromCharCode(65 + index)}.\r\n        </span>\r\n        {typeof option === 'string' ? (\r\n          option\r\n        ) : (\r\n          option.content.map((block, blockIndex) => (\r\n            <React.Fragment key={blockIndex}>\r\n              {block.type === 'text' && <span>{block.value}</span>}\r\n              {block.type === 'image' && block.value && (\r\n                <img src={block.value} alt={`Option image ${blockIndex}`} className=\"inline-block max-h-8 object-contain ml-1\" />\r\n              )}\r\n              {block.type === 'video' && <span>[Video: {block.value}]</span>}\r\n              {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}\r\n              {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}\r\n            </React.Fragment>\r\n          ))\r\n        )}\r\n      </span>\r\n      \r\n      {/* Show correct/incorrect indicators in results mode */}\r\n      {showResults && (\r\n        <div className=\"flex items-center ml-2\">\r\n          {isCorrectOption && (\r\n            <span className=\"text-green-600 font-medium text-xs bg-green-100 px-2 py-1 rounded-full\">\r\n              ✓ Benar\r\n            </span>\r\n          )}\r\n          {isSelected && !isCorrectOption && (\r\n            <span className=\"text-red-600 font-medium text-xs bg-red-100 px-2 py-1 rounded-full\">\r\n              ✗ Salah\r\n            </span>\r\n          )}\r\n        </div>\r\n      )}\r\n    </label>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAAA;;;AAgBO,MAAM,SAAgC,CAAC,EAC5C,MAAM,EACN,KAAK,EACL,UAAU,EACV,cAAc,EACd,cAAc,EACd,OAAO,OAAO,EACd,WAAW,KAAK,EAChB,cAAc,KAAK,EACnB,aAAa,EACb,SAAS,EACV;IACC,MAAM,aAAa,SAAS,UACxB,mBAAmB,QACnB,MAAM,OAAO,CAAC,mBAAmB,eAAe,QAAQ,CAAC;IAE7D,MAAM,kBAAkB,SAAS,UAC7B,kBAAkB,QAClB,MAAM,OAAO,CAAC,kBAAkB,cAAc,QAAQ,CAAC;IAE3D,MAAM,eAAe;QACnB,IAAI,UAAU;QAEd,IAAI,SAAS,SAAS;YACpB,eAAe,YAAY;QAC7B,OAAO;YACL,+CAA+C;YAC/C,MAAM,iBAAiB,MAAM,OAAO,CAAC,kBAAkB,iBAAiB,EAAE;YAC1E,MAAM,aAAa,aACf,eAAe,MAAM,CAAC,CAAC,MAAQ,QAAQ,SACvC;mBAAI;gBAAgB;aAAM;YAC9B,eAAe,YAAY;QAC7B;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,aAAa;YAChB,yCAAyC;YACzC,OAAO,aACH,+BACA;QACN;QAEA,qDAAqD;QACrD,IAAI,iBAAiB;YACnB,sDAAsD;YACtD,OAAO;QACT,OAAO,IAAI,YAAY;YACrB,kDAAkD;YAClD,OAAO;QACT,OAAO;YACL,sCAAsC;YACtC,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC;;QAEV,EAAE,kBAAkB;QACpB,EAAE,WAAW,kCAAkC,GAAG;QAClD,EAAE,eAAe,kBAAkB,0BAA0B,GAAG;QAChE,EAAE,eAAe,cAAc,CAAC,kBAAkB,wBAAwB,GAAG;MAC/E,CAAC;;0BAED,6LAAC;gBACC,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,UAAU;gBACV,WAAW,CAAC;;UAEV,EAAE,SAAS,UAAU,kBAAkB,wBAAwB;UAC/D,EAAE,WAAW,uBAAuB,iBAAiB;QACvD,CAAC;;;;;;0BAEH,6LAAC;gBAAK,WAAW,CAAC,+BAA+B,EAAE,WAAW,kBAAkB,iBAAiB;;kCAC/F,6LAAC;wBAAK,WAAU;;4BACb,OAAO,YAAY,CAAC,KAAK;4BAAO;;;;;;;oBAElC,OAAO,WAAW,WACjB,SAEA,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,2BACzB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;gCACZ,MAAM,IAAI,KAAK,wBAAU,6LAAC;8CAAM,MAAM,KAAK;;;;;;gCAC3C,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK,kBACpC,6LAAC;oCAAI,KAAK,MAAM,KAAK;oCAAE,KAAK,CAAC,aAAa,EAAE,YAAY;oCAAE,WAAU;;;;;;gCAErE,MAAM,IAAI,KAAK,yBAAW,6LAAC;;wCAAK;wCAAS,MAAM,KAAK;wCAAC;;;;;;;gCACrD,MAAM,IAAI,KAAK,uBAAS,6LAAC;;wCAAK;wCAAO,MAAM,KAAK;wCAAC;;;;;;;gCACjD,MAAM,IAAI,KAAK,kCAAoB,6LAAC;;wCAAK;wCAAa,MAAM,KAAK;wCAAC;;;;;;;;2BAPhD;;;;;;;;;;;YAc1B,6BACC,6LAAC;gBAAI,WAAU;;oBACZ,iCACC,6LAAC;wBAAK,WAAU;kCAAyE;;;;;;oBAI1F,cAAc,CAAC,iCACd,6LAAC;wBAAK,WAAU;kCAAqE;;;;;;;;;;;;;;;;;;AAQjG;KArHa", "debugId": null}}, {"offset": {"line": 8962, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/final-exam/question.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Option } from './option';\r\nimport { Question as QuestionType } from '@/types/lms';\r\n\r\ninterface QuestionProps {\r\n  question: QuestionType;\r\n  questionNumber: number;\r\n  totalQuestions: number;\r\n  selectedAnswer: number | string | number[] | undefined;\r\n  onAnswerChange: (questionId: string, answer: number | string | number[]) => void;\r\n  showResults?: boolean;\r\n  isCorrect?: boolean;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport const Question: React.FC<QuestionProps> = ({\r\n  question,\r\n  questionNumber,\r\n  totalQuestions,\r\n  selectedAnswer,\r\n  onAnswerChange,\r\n  showResults = false,\r\n  isCorrect,\r\n  disabled = false\r\n}) => {\r\n  const getQuestionTypeLabel = (type: string) => {\r\n    switch (type) {\r\n      case 'multiple-choice':\r\n      case 'multiple_choice':\r\n        return 'Pilihan Ganda';\r\n      case 'true-false':\r\n      case 'true_false':\r\n        return 'Benar/Salah';\r\n      case 'essay':\r\n        return 'Esai';\r\n      default:\r\n        return type;\r\n    }\r\n  };\r\n\r\n  const handleEssayChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    if (!disabled) {\r\n      onAnswerChange(question.id, e.target.value);\r\n    }\r\n  };\r\n\r\n  const handleTrueFalseChange = (value: string) => {\r\n    if (!disabled) {\r\n      onAnswerChange(question.id, value);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Card className={`\r\n      border-2 transition-all\r\n      ${showResults \r\n        ? (isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50')\r\n        : 'border-gray-200'\r\n      }\r\n    `}>\r\n      <CardContent className=\"p-6\">\r\n        <div className=\"mb-4 flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            <Badge variant=\"outline\" className=\"bg-blue-50 text-blue-700 border-blue-200\">\r\n              Soal {questionNumber} dari {totalQuestions}\r\n            </Badge>\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              {getQuestionTypeLabel(question.type)}\r\n            </Badge>\r\n          </div>\r\n          {showResults && (\r\n            <Badge \r\n              variant={isCorrect ? 'default' : 'destructive'}\r\n              className={isCorrect ? 'bg-green-600 hover:bg-green-700' : ''}\r\n            >\r\n              {isCorrect ? 'Benar' : 'Salah'}\r\n            </Badge>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"mb-6\">\r\n          <p className=\"text-lg leading-relaxed text-gray-900 whitespace-pre-wrap\">\r\n            {typeof question.question === 'string' ? (\r\n              question.question\r\n            ) : Array.isArray(question.question) ? (\r\n              question.question.map((block, index) => (\r\n                <React.Fragment key={index}>\r\n                  {block.type === 'text' && <span>{block.value}</span>}\r\n                  {block.type === 'image' && block.value && (\r\n                    <img src={block.value} alt={`Question image ${index}`} className=\"inline-block max-h-16 object-contain ml-2\" />\r\n                  )}\r\n                </React.Fragment>\r\n              ))\r\n            ) : (\r\n              <span>{String(question.question)}</span>\r\n            )}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Multiple Choice Options */}\r\n        {(question.type === 'multiple-choice' || question.type === 'multiple_choice') && question.options && (\r\n          <div className=\"space-y-3\">\r\n            {question.options.map((option, index) => (\r\n              <Option\r\n                key={index}\r\n                option={option}\r\n                index={index}\r\n                questionId={question.id}\r\n                selectedAnswer={selectedAnswer}\r\n                onAnswerChange={onAnswerChange}\r\n                type=\"radio\"\r\n                disabled={disabled}\r\n                showResults={showResults}\r\n                correctAnswer={question.correctAnswer}\r\n                isCorrect={isCorrect}\r\n              />\r\n            ))}\r\n          </div>\r\n        )}\r\n\r\n        {/* True/False Options */}\r\n        {(question.type === 'true-false' || question.type === 'true_false') && (\r\n          <div className=\"space-y-3\">\r\n            {['true', 'false'].map((value, index) => {\r\n              const isSelected = selectedAnswer === value;\r\n              const isCorrectOption = question.correctAnswer === value;\r\n              \r\n              const getOptionStyles = () => {\r\n                if (!showResults) {\r\n                  return isSelected \r\n                    ? 'border-blue-500 bg-blue-50' \r\n                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50';\r\n                }\r\n                \r\n                if (isCorrectOption) {\r\n                  return 'border-green-500 bg-green-50';\r\n                } else if (isSelected) {\r\n                  return 'border-red-500 bg-red-50';\r\n                } else {\r\n                  return 'border-gray-200 bg-gray-50';\r\n                }\r\n              };\r\n              \r\n              return (\r\n                <label \r\n                  key={value}\r\n                  className={`\r\n                    flex cursor-pointer items-center justify-between p-3 rounded-lg border-2 transition-all\r\n                    ${getOptionStyles()}\r\n                    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}\r\n                    ${showResults && isCorrectOption ? 'ring-2 ring-green-200' : ''}\r\n                    ${showResults && isSelected && !isCorrectOption ? 'ring-2 ring-red-200' : ''}\r\n                  `}\r\n                >\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <input\r\n                      type=\"radio\"\r\n                      name={question.id}\r\n                      value={value}\r\n                      checked={isSelected}\r\n                      onChange={() => handleTrueFalseChange(value)}\r\n                      disabled={disabled}\r\n                      className=\"h-4 w-4 text-blue-600\"\r\n                    />\r\n                    <span className={`font-medium ${disabled ? 'text-gray-500' : 'text-gray-900'}`}>\r\n                      {String.fromCharCode(65 + index)}. {value === 'true' ? 'Benar' : 'Salah'}\r\n                    </span>\r\n                  </div>\r\n                  \r\n                  {/* Show correct/incorrect indicators in results mode */}\r\n                  {showResults && (\r\n                    <div className=\"flex items-center\">\r\n                      {isCorrectOption && (\r\n                        <span className=\"text-green-600 font-medium text-xs bg-green-100 px-2 py-1 rounded-full\">\r\n                          ✓ Benar\r\n                        </span>\r\n                      )}\r\n                      {isSelected && !isCorrectOption && (\r\n                        <span className=\"text-red-600 font-medium text-xs bg-red-100 px-2 py-1 rounded-full\">\r\n                          ✗ Salah\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  )}\r\n                </label>\r\n              );\r\n            })}\r\n          </div>\r\n        )}\r\n\r\n        {/* Essay Question */}\r\n        {question.type === 'essay' && (\r\n          <textarea\r\n            className={`\r\n              w-full resize-none rounded-lg border-2 p-4 focus:border-transparent focus:ring-2 focus:ring-blue-500\r\n              ${disabled ? 'bg-gray-50 cursor-not-allowed' : ''}\r\n            `}\r\n            rows={8}\r\n            placeholder=\"Ketik jawaban Anda di sini...\"\r\n            value={selectedAnswer as string || ''}\r\n            onChange={handleEssayChange}\r\n            disabled={disabled}\r\n          />\r\n        )}\r\n\r\n        {/* Show explanation in results */}\r\n        {showResults && question.explanation && (\r\n          <div className=\"mt-4 p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500\">\r\n            <h4 className=\"font-semibold text-gray-900 mb-2\">Penjelasan:</h4>\r\n            <p className=\"text-sm text-gray-700 leading-relaxed\">\r\n              {typeof question.explanation === 'string' ? (\r\n                question.explanation\r\n              ) : Array.isArray(question.explanation) ? (\r\n                question.explanation.map((block, index) => (\r\n                  <React.Fragment key={index}>\r\n                    {block.type === 'text' && <span>{block.value}</span>}\r\n                    {block.type === 'image' && block.value && (\r\n                      <img src={block.value} alt={`Explanation image ${index}`} className=\"inline-block max-h-16 object-contain ml-2\" />\r\n                    )}\r\n                    {block.type === 'video' && <span>[Video: {block.value}]</span>}\r\n                    {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}\r\n                    {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}\r\n                  </React.Fragment>\r\n                ))\r\n              ) : (\r\n                <span>{String(question.explanation)}</span>\r\n              )}\r\n            </p>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAcO,MAAM,WAAoC,CAAC,EAChD,QAAQ,EACR,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,KAAK,EACnB,SAAS,EACT,WAAW,KAAK,EACjB;IACC,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,UAAU;YACb,eAAe,SAAS,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;QAC5C;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,CAAC,UAAU;YACb,eAAe,SAAS,EAAE,EAAE;QAC9B;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW,CAAC;;MAEhB,EAAE,cACG,YAAY,iCAAiC,6BAC9C,kBACH;IACH,CAAC;kBACC,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAA2C;wCACtE;wCAAe;wCAAO;;;;;;;8CAE9B,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAClC,qBAAqB,SAAS,IAAI;;;;;;;;;;;;wBAGtC,6BACC,6LAAC,oIAAA,CAAA,QAAK;4BACJ,SAAS,YAAY,YAAY;4BACjC,WAAW,YAAY,oCAAoC;sCAE1D,YAAY,UAAU;;;;;;;;;;;;8BAK7B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCACV,OAAO,SAAS,QAAQ,KAAK,WAC5B,SAAS,QAAQ,GACf,MAAM,OAAO,CAAC,SAAS,QAAQ,IACjC,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;oCACZ,MAAM,IAAI,KAAK,wBAAU,6LAAC;kDAAM,MAAM,KAAK;;;;;;oCAC3C,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK,kBACpC,6LAAC;wCAAI,KAAK,MAAM,KAAK;wCAAE,KAAK,CAAC,eAAe,EAAE,OAAO;wCAAE,WAAU;;;;;;;+BAHhD;;;;sDAQvB,6LAAC;sCAAM,OAAO,SAAS,QAAQ;;;;;;;;;;;;;;;;gBAMpC,CAAC,SAAS,IAAI,KAAK,qBAAqB,SAAS,IAAI,KAAK,iBAAiB,KAAK,SAAS,OAAO,kBAC/F,6LAAC;oBAAI,WAAU;8BACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7B,6LAAC,uJAAA,CAAA,SAAM;4BAEL,QAAQ;4BACR,OAAO;4BACP,YAAY,SAAS,EAAE;4BACvB,gBAAgB;4BAChB,gBAAgB;4BAChB,MAAK;4BACL,UAAU;4BACV,aAAa;4BACb,eAAe,SAAS,aAAa;4BACrC,WAAW;2BAVN;;;;;;;;;;gBAiBZ,CAAC,SAAS,IAAI,KAAK,gBAAgB,SAAS,IAAI,KAAK,YAAY,mBAChE,6LAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAQ;qBAAQ,CAAC,GAAG,CAAC,CAAC,OAAO;wBAC7B,MAAM,aAAa,mBAAmB;wBACtC,MAAM,kBAAkB,SAAS,aAAa,KAAK;wBAEnD,MAAM,kBAAkB;4BACtB,IAAI,CAAC,aAAa;gCAChB,OAAO,aACH,+BACA;4BACN;4BAEA,IAAI,iBAAiB;gCACnB,OAAO;4BACT,OAAO,IAAI,YAAY;gCACrB,OAAO;4BACT,OAAO;gCACL,OAAO;4BACT;wBACF;wBAEA,qBACE,6LAAC;4BAEC,WAAW,CAAC;;oBAEV,EAAE,kBAAkB;oBACpB,EAAE,WAAW,kCAAkC,GAAG;oBAClD,EAAE,eAAe,kBAAkB,0BAA0B,GAAG;oBAChE,EAAE,eAAe,cAAc,CAAC,kBAAkB,wBAAwB,GAAG;kBAC/E,CAAC;;8CAED,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,MAAM,SAAS,EAAE;4CACjB,OAAO;4CACP,SAAS;4CACT,UAAU,IAAM,sBAAsB;4CACtC,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAW,CAAC,YAAY,EAAE,WAAW,kBAAkB,iBAAiB;;gDAC3E,OAAO,YAAY,CAAC,KAAK;gDAAO;gDAAG,UAAU,SAAS,UAAU;;;;;;;;;;;;;gCAKpE,6BACC,6LAAC;oCAAI,WAAU;;wCACZ,iCACC,6LAAC;4CAAK,WAAU;sDAAyE;;;;;;wCAI1F,cAAc,CAAC,iCACd,6LAAC;4CAAK,WAAU;sDAAqE;;;;;;;;;;;;;2BAjCtF;;;;;oBAyCX;;;;;;gBAKH,SAAS,IAAI,KAAK,yBACjB,6LAAC;oBACC,WAAW,CAAC;;cAEV,EAAE,WAAW,kCAAkC,GAAG;YACpD,CAAC;oBACD,MAAM;oBACN,aAAY;oBACZ,OAAO,kBAA4B;oBACnC,UAAU;oBACV,UAAU;;;;;;gBAKb,eAAe,SAAS,WAAW,kBAClC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAE,WAAU;sCACV,OAAO,SAAS,WAAW,KAAK,WAC/B,SAAS,WAAW,GAClB,MAAM,OAAO,CAAC,SAAS,WAAW,IACpC,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC/B,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;wCACZ,MAAM,IAAI,KAAK,wBAAU,6LAAC;sDAAM,MAAM,KAAK;;;;;;wCAC3C,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK,kBACpC,6LAAC;4CAAI,KAAK,MAAM,KAAK;4CAAE,KAAK,CAAC,kBAAkB,EAAE,OAAO;4CAAE,WAAU;;;;;;wCAErE,MAAM,IAAI,KAAK,yBAAW,6LAAC;;gDAAK;gDAAS,MAAM,KAAK;gDAAC;;;;;;;wCACrD,MAAM,IAAI,KAAK,uBAAS,6LAAC;;gDAAK;gDAAO,MAAM,KAAK;gDAAC;;;;;;;wCACjD,MAAM,IAAI,KAAK,kCAAoB,6LAAC;;gDAAK;gDAAa,MAAM,KAAK;gDAAC;;;;;;;;mCAPhD;;;;0DAWvB,6LAAC;0CAAM,OAAO,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD;KA1Na", "debugId": null}}, {"offset": {"line": 9351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/final-exam/question-bank.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Question } from '@/types/lms';\r\n\r\ninterface QuestionBankProps {\r\n  questions: Question[];\r\n  currentQuestion: number;\r\n  answeredQuestions: Set<number>;\r\n  onQuestionSelect: (questionIndex: number) => void;\r\n  flaggedQuestions?: Set<number>;\r\n  onToggleFlag?: (questionIndex: number) => void;\r\n  showFlags?: boolean;\r\n  onSubmit?: () => void;\r\n  canSubmit?: boolean;\r\n  isSubmitting?: boolean;\r\n  reviewMode?: boolean;\r\n  results?: { [key: string]: boolean };\r\n}\r\n\r\nexport const QuestionBank: React.FC<QuestionBankProps> = ({\r\n  questions,\r\n  currentQuestion,\r\n  answeredQuestions,\r\n  onQuestionSelect,\r\n  flaggedQuestions = new Set(),\r\n  onToggleFlag,\r\n  showFlags = true,\r\n  onSubmit,\r\n  canSubmit = false,\r\n  isSubmitting = false,\r\n  reviewMode = false,\r\n  results = {}\r\n}) => {\r\n  const getQuestionStatus = (index: number) => {\r\n    if (reviewMode) {\r\n      const question = questions[index];\r\n      const isCorrect = results[question.id];\r\n      if (index === currentQuestion) {\r\n        return isCorrect ? 'current-correct' : 'current-incorrect';\r\n      }\r\n      return isCorrect ? 'correct' : 'incorrect';\r\n    }\r\n    \r\n    if (index === currentQuestion) return 'current';\r\n    if (answeredQuestions.has(index)) return 'answered';\r\n    return 'unanswered';\r\n  };\r\n\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'current':\r\n        return 'bg-blue-600 text-white border-blue-600';\r\n      case 'current-correct':\r\n        return 'bg-green-600 text-white border-green-600 ring-2 ring-green-200';\r\n      case 'current-incorrect':\r\n        return 'bg-red-600 text-white border-red-600 ring-2 ring-red-200';\r\n      case 'correct':\r\n        return 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200';\r\n      case 'incorrect':\r\n        return 'bg-red-100 text-red-800 border-red-300 hover:bg-red-200';\r\n      case 'answered':\r\n        return 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200';\r\n      case 'unanswered':\r\n        return 'bg-gray-50 text-gray-600 border-gray-300 hover:bg-gray-100';\r\n      default:\r\n        return 'bg-gray-50 text-gray-600 border-gray-300';\r\n    }\r\n  };\r\n\r\n  const answeredCount = answeredQuestions.size;\r\n  const unansweredCount = questions.length - answeredCount;\r\n  \r\n  // Count correct/incorrect in review mode\r\n  const correctCount = reviewMode ? questions.filter(q => results[q.id]).length : 0;\r\n  const incorrectCount = reviewMode ? questions.filter(q => !results[q.id]).length : 0;\r\n\r\n  return (\r\n    <Card className=\"h-fit sticky top-4\">\r\n      <CardHeader className=\"pb-4\">\r\n        <CardTitle className=\"text-lg\">{reviewMode ? 'Review Soal' : 'Nomor Soal'}</CardTitle>\r\n        {reviewMode ? (\r\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-4 h-4 bg-green-100 border border-green-300 rounded\"></div>\r\n              <span>Benar: {correctCount}</span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-4 h-4 bg-red-100 border border-red-300 rounded\"></div>\r\n              <span>Salah: {incorrectCount}</span>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-4 h-4 bg-green-100 border border-green-300 rounded\"></div>\r\n              <span>Terjawab: {answeredCount}</span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-4 h-4 bg-gray-50 border border-gray-300 rounded\"></div>\r\n              <span>Belum: {unansweredCount}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n        {showFlags && flaggedQuestions.size > 0 && (\r\n          <div className=\"flex items-center space-x-2 text-sm\">\r\n            <div className=\"w-4 h-4 bg-yellow-100 border border-yellow-400 rounded relative\">\r\n              <div className=\"absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full\"></div>\r\n            </div>\r\n            <span>Ditandai: {flaggedQuestions.size}</span>\r\n          </div>\r\n        )}\r\n      </CardHeader>\r\n      \r\n      <CardContent className=\"space-y-4\">\r\n        {/* Question Grid */}\r\n        <div className=\"grid grid-cols-4 gap-2\">\r\n          {questions.map((_, index) => {\r\n            const status = getQuestionStatus(index);\r\n            const isFlagged = flaggedQuestions.has(index);\r\n            \r\n            return (\r\n              <Button\r\n                key={index}\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className={`\r\n                  relative h-12 w-12 p-0 font-medium transition-all\r\n                  ${getStatusColor(status)}\r\n                  ${isFlagged ? 'ring-2 ring-yellow-400' : ''}\r\n                `}\r\n                onClick={() => onQuestionSelect(index)}\r\n              >\r\n                {index + 1}\r\n                {isFlagged && (\r\n                  <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-yellow-500 rounded-full border-2 border-white\"></div>\r\n                )}\r\n              </Button>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        {/* Legend */}\r\n        <div className=\"pt-4 border-t space-y-2 text-xs text-gray-600\">\r\n          {reviewMode ? (\r\n            <>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-green-600 rounded\"></div>\r\n                <span>Soal saat ini (Benar)</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-red-600 rounded\"></div>\r\n                <span>Soal saat ini (Salah)</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-green-100 border border-green-300 rounded\"></div>\r\n                <span>Jawaban benar</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-red-100 border border-red-300 rounded\"></div>\r\n                <span>Jawaban salah</span>\r\n              </div>\r\n            </>\r\n          ) : (\r\n            <>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-blue-600 rounded\"></div>\r\n                <span>Soal saat ini</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-green-100 border border-green-300 rounded\"></div>\r\n                <span>Sudah dijawab</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-3 h-3 bg-gray-50 border border-gray-300 rounded\"></div>\r\n                <span>Belum dijawab</span>\r\n              </div>\r\n            </>\r\n          )}\r\n          {showFlags && (\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-3 h-3 bg-yellow-100 border border-yellow-400 rounded relative\">\r\n                <div className=\"absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-yellow-500 rounded-full\"></div>\r\n              </div>\r\n              <span>Ditandai untuk review</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Quick Actions */}\r\n        {showFlags && onToggleFlag && (\r\n          <div className=\"pt-4 border-t\">\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"w-full text-xs\"\r\n              onClick={() => onToggleFlag(currentQuestion)}\r\n            >\r\n              {flaggedQuestions.has(currentQuestion) ? 'Hapus Tanda' : 'Tandai Soal'}\r\n            </Button>\r\n          </div>\r\n        )}\r\n\r\n        {/* Submit Button */}\r\n        {onSubmit && (\r\n          <div className=\"pt-4 border-t\">\r\n            <Button\r\n              onClick={onSubmit}\r\n              disabled={!canSubmit || isSubmitting}\r\n              className=\"w-full bg-blue-600 hover:bg-blue-700 text-white\"\r\n              size=\"lg\"\r\n            >\r\n              {isSubmitting ? 'Menyerahkan...' : 'Submit Ujian'}\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AACA;AAEA;;;;AAkBO,MAAM,eAA4C,CAAC,EACxD,SAAS,EACT,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,mBAAmB,IAAI,KAAK,EAC5B,YAAY,EACZ,YAAY,IAAI,EAChB,QAAQ,EACR,YAAY,KAAK,EACjB,eAAe,KAAK,EACpB,aAAa,KAAK,EAClB,UAAU,CAAC,CAAC,EACb;IACC,MAAM,oBAAoB,CAAC;QACzB,IAAI,YAAY;YACd,MAAM,WAAW,SAAS,CAAC,MAAM;YACjC,MAAM,YAAY,OAAO,CAAC,SAAS,EAAE,CAAC;YACtC,IAAI,UAAU,iBAAiB;gBAC7B,OAAO,YAAY,oBAAoB;YACzC;YACA,OAAO,YAAY,YAAY;QACjC;QAEA,IAAI,UAAU,iBAAiB,OAAO;QACtC,IAAI,kBAAkB,GAAG,CAAC,QAAQ,OAAO;QACzC,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,kBAAkB,IAAI;IAC5C,MAAM,kBAAkB,UAAU,MAAM,GAAG;IAE3C,yCAAyC;IACzC,MAAM,eAAe,aAAa,UAAU,MAAM,CAAC,CAAA,IAAK,OAAO,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,GAAG;IAChF,MAAM,iBAAiB,aAAa,UAAU,MAAM,CAAC,CAAA,IAAK,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,GAAG;IAEnF,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAW,aAAa,gBAAgB;;;;;;oBAC5D,2BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;;4CAAK;4CAAQ;;;;;;;;;;;;;0CAEhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;;4CAAK;4CAAQ;;;;;;;;;;;;;;;;;;6CAIlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;;4CAAK;4CAAW;;;;;;;;;;;;;0CAEnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;;4CAAK;4CAAQ;;;;;;;;;;;;;;;;;;;oBAInB,aAAa,iBAAiB,IAAI,GAAG,mBACpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,6LAAC;;oCAAK;oCAAW,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;0BAK5C,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,GAAG;4BACjB,MAAM,SAAS,kBAAkB;4BACjC,MAAM,YAAY,iBAAiB,GAAG,CAAC;4BAEvC,qBACE,6LAAC,qIAAA,CAAA,SAAM;gCAEL,SAAQ;gCACR,MAAK;gCACL,WAAW,CAAC;;kBAEV,EAAE,eAAe,QAAQ;kBACzB,EAAE,YAAY,2BAA2B,GAAG;gBAC9C,CAAC;gCACD,SAAS,IAAM,iBAAiB;;oCAE/B,QAAQ;oCACR,2BACC,6LAAC;wCAAI,WAAU;;;;;;;+BAZZ;;;;;wBAgBX;;;;;;kCAIF,6LAAC;wBAAI,WAAU;;4BACZ,2BACC;;kDACE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;;6DAIV;;kDACE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;;;4BAIX,2BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;oBAMX,aAAa,8BACZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,aAAa;sCAE3B,iBAAiB,GAAG,CAAC,mBAAmB,gBAAgB;;;;;;;;;;;oBAM9D,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,CAAC,aAAa;4BACxB,WAAU;4BACV,MAAK;sCAEJ,eAAe,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;AAOjD;KAvMa", "debugId": null}}, {"offset": {"line": 9879, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/final-exam/index.ts"], "sourcesContent": ["export { Option } from './option';\r\nexport { Question } from './question';\r\nexport { QuestionBank } from './question-bank';"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 9906, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/components/lms/index.ts"], "sourcesContent": ["export { TreeNode } from './tree-node';\r\nexport { TableOfContents } from './table-of-contents';\r\nexport { ContentItem } from './content-item';\r\nexport { ContentViewer } from './content-viewer';\r\nexport { QuizIntroduction } from './quiz-introduction';\r\nexport { QuizCard } from './quiz-card';\r\nexport { QuizModal } from './quiz-modal';\r\nexport { ChapterSection } from './chapter-section';\r\nexport { ModuleSection } from './module-section';\r\nexport { CertificateTemplate } from './certificate-template';\r\n\r\n// Tab components\r\nexport { CourseTab } from './tabs/course-tab';\r\nexport { ProgressTab } from './tabs/progress-tab';\r\nexport { ExamTab } from './tabs/exam-tab';\r\nexport { CertificateTab } from './tabs/certificate-tab';\r\nexport { GradesTab } from './grades-tab';\r\nexport { CourseMaterialTab } from './course-material-tab';\r\n\r\n// Final Exam components\r\nexport { Question, Option, QuestionBank } from './final-exam';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AAEA,wBAAwB;AACxB", "debugId": null}}, {"offset": {"line": 9977, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/File%20Kerja%20Daffa/GAWEAN/TERANG_LMS/terang-lms-frontend/src/app/%28course-view%29/my-courses/%5BcourseId%5D/learn/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useCallback, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Progress } from '@/components/ui/progress';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  BookOpen01Icon as BookOpenIcon,\n  ChartIcon as BarChartIcon,\n  Award01Icon as TrophyIcon,\n  Award01Icon as AwardIcon,\n  Calendar01Icon as CalendarIcon,\n  Building02Icon as BuildingIcon,\n  ArrowLeft01Icon as ArrowLeftIcon\n} from 'hugeicons-react';\nimport Link from 'next/link';\nimport { useParams, useRouter, useSearchParams } from 'next/navigation';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Course, Quiz } from '@/types/lms';\nimport { useEnrollment } from '@/contexts/enrollment-context';\nimport {\n  QuizModal,\n  CourseTab,\n  ProgressTab,\n  ExamTab,\n  CertificateTab,\n  TableOfContents\n} from '@/components/lms';\n\nconst CourseLearningPage: React.FC = () => {\n  const params = useParams();\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const courseId = params.courseId as string;\n  const { courseData: defaultCourseData, updateCourseProgress, getCourseById } = useEnrollment();\n\n  // Get the specific course based on courseId from URL\n  const courseData = getCourseById(courseId) || defaultCourseData;\n\n  const [expandedContents, setExpandedContents] = useState<{\n    [key: string]: boolean;\n  }>({});\n  const [expandedModules, setExpandedModules] = useState<{\n    [key: string]: boolean;\n  }>({});\n  const [expandedChapters, setExpandedChapters] = useState<{\n    [key: string]: boolean;\n  }>({});\n  const [currentQuiz, setCurrentQuiz] = useState<Quiz | null>(null);\n  const [showCertificate, setShowCertificate] = useState(false);\n  const [activeTab, setActiveTab] = useState('course');\n  const [isLearningMode, setIsLearningMode] = useState(false);\n  const [currentModuleId, setCurrentModuleId] = useState<string>('');\n  const [currentChapterId, setCurrentChapterId] = useState<string>('');\n  const [currentContentId, setCurrentContentId] = useState<string>('');\n\n  useEffect(() => {\n    const tab = searchParams.get('tab');\n    if (tab) {\n      setActiveTab(tab);\n    }\n    \n    // Check if we're coming from course material (learning mode)\n    const fromMaterial = searchParams.get('from') === 'material';\n    setIsLearningMode(fromMaterial);\n  }, [searchParams]);\n\n  const toggleContent = useCallback((contentId: string) => {\n    setExpandedContents((prev) => ({ ...prev, [contentId]: !prev[contentId] }));\n  }, []);\n\n  const toggleModule = useCallback((moduleId: string) => {\n    setExpandedModules((prev) => ({ ...prev, [moduleId]: !prev[moduleId] }));\n  }, []);\n\n  const toggleChapter = useCallback((chapterId: string) => {\n    setExpandedChapters((prev) => ({ ...prev, [chapterId]: !prev[chapterId] }));\n  }, []);\n\n  const expandAllModules = useCallback(() => {\n    const newExpandedModules: { [key: string]: boolean } = {};\n    courseData.modules.forEach((module) => {\n      if (module.isUnlocked) {\n        newExpandedModules[module.id] = true;\n      }\n    });\n    setExpandedModules(newExpandedModules);\n  }, [courseData.modules]);\n\n  const collapseAllModules = useCallback(() => {\n    setExpandedModules({});\n  }, []);\n\n  const expandAllChaptersInModule = useCallback(\n    (moduleId: string) => {\n      const courseModule = courseData.modules.find((m) => m.id === moduleId);\n      if (!courseModule) return;\n\n      const newExpandedChapters = { ...expandedChapters };\n      courseModule.chapters.forEach((chapter) => {\n        if (chapter.isUnlocked) {\n          newExpandedChapters[chapter.id] = true;\n        }\n      });\n      setExpandedChapters(newExpandedChapters);\n    },\n    [courseData.modules, expandedChapters]\n  );\n\n  const collapseAllChaptersInModule = useCallback(\n    (moduleId: string) => {\n      const courseModule = courseData.modules.find((m) => m.id === moduleId);\n      if (!courseModule) return;\n\n      setExpandedModules((prev) => ({ ...prev, [moduleId]: false }));\n\n      const newExpandedContents = { ...expandedContents };\n      const newExpandedChapters = { ...expandedChapters };\n      courseModule.chapters.forEach((chapter) => {\n        delete newExpandedChapters[chapter.id];\n      });\n      setExpandedChapters(newExpandedChapters);\n    },\n    [courseData.modules, expandedChapters]\n  );\n\n  const toggleContentComplete = useCallback(\n    (contentId: string) => {\n      const newCourse = JSON.parse(JSON.stringify(courseData)) as Course;\n\n      let contentFound = false;\n      for (const courseModule of newCourse.modules) {\n        for (const chapter of courseModule.chapters) {\n          const content = chapter.contents.find((c) => c.id === contentId);\n          if (content) {\n            content.isCompleted = !content.isCompleted;\n            contentFound = true;\n\n            const completedContents = chapter.contents.filter(\n              (c) => c.isCompleted\n            ).length;\n            const nextChapterIndex = chapter.order;\n            const nextChapter = courseModule.chapters.find(\n              (ch) => ch.order === nextChapterIndex + 1\n            );\n\n            if (\n              nextChapter &&\n              completedContents === chapter.contents.length &&\n              chapter.quiz.isPassed\n            ) {\n              nextChapter.isUnlocked = true;\n            }\n\n            const allChaptersCompleted = courseModule.chapters.every(\n              (ch) =>\n                ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed\n            );\n\n            if (allChaptersCompleted && courseModule.moduleQuiz.isPassed) {\n              const nextModuleIndex = courseModule.order;\n              const nextModule = newCourse.modules.find(\n                (m) => m.order === nextModuleIndex + 1\n              );\n              if (nextModule) {\n                nextModule.isUnlocked = true;\n                if (nextModule.chapters.length > 0) {\n                  nextModule.chapters[0].isUnlocked = true;\n                }\n              }\n            }\n\n            break;\n          }\n        }\n        if (contentFound) break;\n      }\n\n      updateCourseProgress(newCourse);\n    },\n    [courseData, updateCourseProgress]\n  );\n\n  const startQuiz = useCallback(\n    (quizId: string) => {\n      let quiz: Quiz | undefined;\n\n      // Check if it's the final exam\n      if (courseData.finalExam.id === quizId) {\n        // Navigate to dedicated exam page for final exam\n        router.push(`/my-courses/${courseId}/exam?type=final&examId=${quizId}`);\n        return;\n      }\n\n      // Check module and chapter quizzes\n      for (const courseModule of courseData.modules) {\n        for (const chapter of courseModule.chapters) {\n          if (chapter.quiz.id === quizId) {\n            quiz = chapter.quiz;\n            setCurrentQuiz({ ...quiz });\n            return;\n          }\n        }\n        if (courseModule.moduleQuiz.id === quizId) {\n          quiz = courseModule.moduleQuiz;\n          setCurrentQuiz({ ...quiz });\n          return;\n        }\n      }\n    },\n    [courseData, courseId, router]\n  );\n\n  const handleQuizComplete = useCallback(\n    (score: number) => {\n      if (!currentQuiz) return;\n\n      const newCourse = JSON.parse(JSON.stringify(courseData)) as Course;\n\n      const updateQuiz = (quiz: Quiz) => {\n        quiz.attempts += 1;\n        quiz.lastScore = score;\n        quiz.isPassed = score >= quiz.minimumScore;\n      };\n\n      for (const courseModule of newCourse.modules) {\n        for (const chapter of courseModule.chapters) {\n          if (chapter.quiz.id === currentQuiz.id) {\n            updateQuiz(chapter.quiz);\n\n            const allContentsCompleted = chapter.contents.every(\n              (c) => c.isCompleted\n            );\n            if ((chapter.quiz.isPassed || chapter.quiz.attempts >= chapter.quiz.maxAttempts) && allContentsCompleted) {\n              const nextChapter = courseModule.chapters.find(\n                (ch) => ch.order === chapter.order + 1\n              );\n              if (nextChapter) {\n                nextChapter.isUnlocked = true;\n              }\n            }\n            break;\n          }\n        }\n\n        if (courseModule.moduleQuiz.id === currentQuiz.id) {\n          updateQuiz(courseModule.moduleQuiz);\n\n          const allChaptersCompleted = courseModule.chapters.every(\n            (ch) => ch.contents.every((c) => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)\n          );\n          if ((courseModule.moduleQuiz.isPassed || courseModule.moduleQuiz.attempts >= courseModule.moduleQuiz.maxAttempts) && allChaptersCompleted) {\n            const nextModule = newCourse.modules.find(\n              (m) => m.order === courseModule.order + 1\n            );\n            if (nextModule) {\n              nextModule.isUnlocked = true;\n              if (nextModule.chapters.length > 0) {\n                nextModule.chapters[0].isUnlocked = true;\n              }\n            }\n          }\n        }\n      }\n\n      if (newCourse.finalExam.id === currentQuiz.id) {\n        updateQuiz(newCourse.finalExam);\n\n        const allModulesCompleted = newCourse.modules.every(\n          (m) =>\n            m.chapters.every(\n              (ch) =>\n                ch.contents.every((c) => c.isCompleted) && (ch.quiz.isPassed || ch.quiz.attempts >= ch.quiz.maxAttempts)\n            ) && (m.moduleQuiz.isPassed || m.moduleQuiz.attempts >= m.moduleQuiz.maxAttempts)\n        );\n\n        if (newCourse.finalExam.isPassed && allModulesCompleted) {\n          newCourse.certificate.isEligible = true;\n          newCourse.certificate.completionDate = new Date()\n            .toISOString()\n            .split('T')[0];\n          newCourse.status = 'completed';\n        }\n      }\n\n      updateCourseProgress(newCourse);\n      setCurrentQuiz(null);\n    },\n    [currentQuiz, courseData, updateCourseProgress]\n  );\n\n  const handleNavigateToSection = useCallback(\n    (moduleId: string, chapterId?: string, contentId?: string) => {\n      setActiveTab('course');\n\n      // Set current content for single content view\n      setCurrentModuleId(moduleId);\n      if (chapterId) {\n        setCurrentChapterId(chapterId);\n      }\n      if (contentId) {\n        setCurrentContentId(contentId);\n      }\n\n      setExpandedModules((prev) => ({ ...prev, [moduleId]: true }));\n      if (chapterId) {\n        setExpandedChapters((prev) => ({ ...prev, [chapterId]: true }));\n      }\n\n      if (contentId) {\n        setExpandedContents((prev) => ({ ...prev, [contentId]: true }));\n      }\n    },\n    []\n  );\n\n  const completedChapters = courseData.modules.reduce(\n    (total, module) =>\n      total +\n      module.chapters.filter(\n        (ch) => ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed\n      ).length,\n    0\n  );\n\n  const totalChapters = courseData.modules.reduce(\n    (total, module) => total + module.chapters.length,\n    0\n  );\n  const overallProgress =\n    totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0;\n\n  const navigationItems = [\n    { id: 'course', label: 'Konten Kursus', icon: BookOpenIcon },\n    { id: 'progress', label: 'Kemajuan', icon: BarChartIcon },\n    { id: 'exam', label: 'Final Exam', icon: TrophyIcon },\n    { id: 'certificate', label: 'Sertifikat', icon: AwardIcon }\n  ];\n\n  return (\n    <div className='min-h-screen bg-gray-50'>\n      {/* Main Content Layout */}\n      <div className='flex h-screen'>\n        {/* Left Navigation Panel */}\n        <div className='w-96 bg-white border-r flex-shrink-0'>\n          <div className='p-6'>\n            {/* Course Info Header */}\n            <div className='mb-6'>\n              <div className='flex items-center gap-2 mb-3'>\n                <Link href={`/my-courses/${courseId}`}>\n                  <Button variant='ghost' size='sm' className='p-1'>\n                    <ArrowLeftIcon className='h-4 w-4' />\n                  </Button>\n                </Link>\n                <BuildingIcon className='h-6 w-6 text-[var(--iai-primary)]' />\n                <div>\n                  <h1 className='text-lg font-bold text-gray-900'>\n                    {courseData.name}\n                  </h1>\n                  <p className='text-sm text-gray-600'>{courseData.code}</p>\n                </div>\n              </div>\n              <div className='space-y-2'>\n                <div className='flex items-center justify-between text-sm'>\n                  <span className='text-gray-500'>Kemajuan</span>\n                  <span className='font-medium'>{Math.round(overallProgress)}%</span>\n                </div>\n                <Progress value={overallProgress} className='h-2' />\n                <div className='flex items-center justify-between text-sm'>\n                  <span className='text-gray-500'>Instruktur</span>\n                  <span>{courseData.instructor}</span>\n                </div>\n                <div className='flex items-center justify-between text-sm'>\n                  <span className='text-gray-500'>Deadline</span>\n                  <span>{courseData.endDate}</span>\n                </div>\n                <Badge\n                  variant={\n                    courseData.status === 'completed'\n                      ? 'default'\n                      : 'secondary'\n                  }\n                  className='w-full justify-center'\n                >\n                  {courseData.status === 'completed'\n                    ? 'Selesai'\n                    : 'Sedang Belajar'}\n                </Badge>\n              </div>\n            </div>\n\n            {isLearningMode ? (\n              <TableOfContents\n                course={courseData}\n                onNavigate={handleNavigateToSection}\n                expandedModules={expandedModules}\n                expandedChapters={expandedChapters}\n                onToggleModule={toggleModule}\n                onToggleChapter={toggleChapter}\n                currentModuleIndex={0}\n              />\n            ) : (\n              <>\n                <h2 className='text-lg font-semibold text-gray-900 mb-4'>\n                  Menu Pembelajaran\n                </h2>\n                <nav className='space-y-2'>\n                  {navigationItems.map((item) => {\n                    const Icon = item.icon;\n                    return (\n                      <button\n                        key={item.id}\n                        onClick={() => setActiveTab(item.id)}\n                        className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${\n                          activeTab === item.id\n                            ? 'bg-blue-50 text-blue-700 border border-blue-200'\n                            : 'text-gray-700 hover:bg-gray-50'\n                        }`}\n                      >\n                        <Icon className='h-4 w-4' />\n                        <span className='text-sm font-medium'>{item.label}</span>\n                      </button>\n                    );\n                  })}\n                </nav>\n              </>\n            )}\n          </div>\n        </div>\n\n        {/* Right Content Area */}\n        <div className='flex-1 overflow-auto'>\n          <div className='p-6 pb-8'>\n            <div className='max-w-6xl'>\n              <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>\n                <TabsContent value='course' className='mt-0'>\n                  <CourseTab\n                    courseData={courseData}\n                    expandedModules={expandedModules}\n                    expandedChapters={expandedChapters}\n                    expandedContents={expandedContents}\n                    onToggleModule={toggleModule}\n                    onToggleChapter={toggleChapter}\n                    onToggleContent={toggleContent}\n                    onToggleContentComplete={toggleContentComplete}\n                    onStartQuiz={startQuiz}\n                    onNavigateToSection={handleNavigateToSection}\n                    onExpandAllModules={expandAllModules}\n                    onCollapseAllModules={collapseAllModules}\n                    onExpandAllChaptersInModule={expandAllChaptersInModule}\n                    onCollapseAllChaptersInModule={collapseAllChaptersInModule}\n                    onNavigateToFinalExam={() => setActiveTab('exam')}\n                    isLearningMode={isLearningMode}\n                    currentModuleId={currentModuleId}\n                    currentChapterId={currentChapterId}\n                    currentContentId={currentContentId}\n                  />\n                </TabsContent>\n\n                <TabsContent value='progress' className='mt-4'>\n                  <ProgressTab\n                    courseData={courseData}\n                    overallProgress={overallProgress}\n                  />\n                </TabsContent>\n\n                <TabsContent value='exam' className='mt-4'>\n                  <ExamTab courseData={courseData} onStartQuiz={startQuiz} />\n                </TabsContent>\n\n                <TabsContent value='certificate' className='mt-4'>\n                  <CertificateTab\n                    courseData={courseData}\n                    institution={{\n                      id: 'iai-indonesia',\n                      name: 'Indonesian Institute of Architects',\n                      shortName: 'IAI',\n                      website: 'https://iai.or.id',\n                      certificateTemplate: {\n                        primaryColor: '#1e40af',\n                        secondaryColor: '#f59e0b',\n                        signatoryName: 'Ar. Georgius Budi Yulianto, IAI, AA',\n                        signatoryTitle: 'Ketua Umum IAI 2024-2027'\n                      }\n                    }}\n                    overallProgress={overallProgress}\n                    onGenerateCertificate={() => setShowCertificate(true)}\n                  />\n                </TabsContent>\n              </Tabs>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quiz Modal - Only for chapter and module quizzes */}\n      {currentQuiz && (\n        <QuizModal\n          quiz={currentQuiz}\n          isOpen={true}\n          onComplete={handleQuizComplete}\n          onClose={() => setCurrentQuiz(null)}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default CourseLearningPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AApBA;;;;;;;;;;;AA6BA,MAAM,qBAA+B;;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,WAAW,OAAO,QAAQ;IAChC,MAAM,EAAE,YAAY,iBAAiB,EAAE,oBAAoB,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4IAAA,CAAA,gBAAa,AAAD;IAE3F,qDAAqD;IACrD,MAAM,aAAa,cAAc,aAAa;IAE9C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAEpD,CAAC;IACJ,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAElD,CAAC;IACJ,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAEpD,CAAC;IACJ,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,MAAM,aAAa,GAAG,CAAC;YAC7B,IAAI,KAAK;gBACP,aAAa;YACf;YAEA,6DAA6D;YAC7D,MAAM,eAAe,aAAa,GAAG,CAAC,YAAY;YAClD,kBAAkB;QACpB;uCAAG;QAAC;KAAa;IAEjB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACjC;iEAAoB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,UAAU;oBAAC,CAAC;;QAC3E;wDAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YAChC;gEAAmB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS;oBAAC,CAAC;;QACxE;uDAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACjC;iEAAoB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,UAAU,EAAE,CAAC,IAAI,CAAC,UAAU;oBAAC,CAAC;;QAC3E;wDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACnC,MAAM,qBAAiD,CAAC;YACxD,WAAW,OAAO,CAAC,OAAO;oEAAC,CAAC;oBAC1B,IAAI,OAAO,UAAU,EAAE;wBACrB,kBAAkB,CAAC,OAAO,EAAE,CAAC,GAAG;oBAClC;gBACF;;YACA,mBAAmB;QACrB;2DAAG;QAAC,WAAW,OAAO;KAAC;IAEvB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACrC,mBAAmB,CAAC;QACtB;6DAAG,EAAE;IAEL,MAAM,4BAA4B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qEAC1C,CAAC;YACC,MAAM,eAAe,WAAW,OAAO,CAAC,IAAI;0FAAC,CAAC,IAAM,EAAE,EAAE,KAAK;;YAC7D,IAAI,CAAC,cAAc;YAEnB,MAAM,sBAAsB;gBAAE,GAAG,gBAAgB;YAAC;YAClD,aAAa,QAAQ,CAAC,OAAO;6EAAC,CAAC;oBAC7B,IAAI,QAAQ,UAAU,EAAE;wBACtB,mBAAmB,CAAC,QAAQ,EAAE,CAAC,GAAG;oBACpC;gBACF;;YACA,oBAAoB;QACtB;oEACA;QAAC,WAAW,OAAO;QAAE;KAAiB;IAGxC,MAAM,8BAA8B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uEAC5C,CAAC;YACC,MAAM,eAAe,WAAW,OAAO,CAAC,IAAI;4FAAC,CAAC,IAAM,EAAE,EAAE,KAAK;;YAC7D,IAAI,CAAC,cAAc;YAEnB;+EAAmB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,SAAS,EAAE;oBAAM,CAAC;;YAE5D,MAAM,sBAAsB;gBAAE,GAAG,gBAAgB;YAAC;YAClD,MAAM,sBAAsB;gBAAE,GAAG,gBAAgB;YAAC;YAClD,aAAa,QAAQ,CAAC,OAAO;+EAAC,CAAC;oBAC7B,OAAO,mBAAmB,CAAC,QAAQ,EAAE,CAAC;gBACxC;;YACA,oBAAoB;QACtB;sEACA;QAAC,WAAW,OAAO;QAAE;KAAiB;IAGxC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEACtC,CAAC;YACC,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;YAE5C,IAAI,eAAe;YACnB,KAAK,MAAM,gBAAgB,UAAU,OAAO,CAAE;gBAC5C,KAAK,MAAM,WAAW,aAAa,QAAQ,CAAE;oBAC3C,MAAM,UAAU,QAAQ,QAAQ,CAAC,IAAI;yFAAC,CAAC,IAAM,EAAE,EAAE,KAAK;;oBACtD,IAAI,SAAS;wBACX,QAAQ,WAAW,GAAG,CAAC,QAAQ,WAAW;wBAC1C,eAAe;wBAEf,MAAM,oBAAoB,QAAQ,QAAQ,CAAC,MAAM;qFAC/C,CAAC,IAAM,EAAE,WAAW;oFACpB,MAAM;wBACR,MAAM,mBAAmB,QAAQ,KAAK;wBACtC,MAAM,cAAc,aAAa,QAAQ,CAAC,IAAI;iGAC5C,CAAC,KAAO,GAAG,KAAK,KAAK,mBAAmB;;wBAG1C,IACE,eACA,sBAAsB,QAAQ,QAAQ,CAAC,MAAM,IAC7C,QAAQ,IAAI,CAAC,QAAQ,EACrB;4BACA,YAAY,UAAU,GAAG;wBAC3B;wBAEA,MAAM,uBAAuB,aAAa,QAAQ,CAAC,KAAK;0GACtD,CAAC,KACC,GAAG,QAAQ,CAAC,KAAK;kHAAC,CAAC,IAAM,EAAE,WAAW;oHAAK,GAAG,IAAI,CAAC,QAAQ;;wBAG/D,IAAI,wBAAwB,aAAa,UAAU,CAAC,QAAQ,EAAE;4BAC5D,MAAM,kBAAkB,aAAa,KAAK;4BAC1C,MAAM,aAAa,UAAU,OAAO,CAAC,IAAI;oGACvC,CAAC,IAAM,EAAE,KAAK,KAAK,kBAAkB;;4BAEvC,IAAI,YAAY;gCACd,WAAW,UAAU,GAAG;gCACxB,IAAI,WAAW,QAAQ,CAAC,MAAM,GAAG,GAAG;oCAClC,WAAW,QAAQ,CAAC,EAAE,CAAC,UAAU,GAAG;gCACtC;4BACF;wBACF;wBAEA;oBACF;gBACF;gBACA,IAAI,cAAc;YACpB;YAEA,qBAAqB;QACvB;gEACA;QAAC;QAAY;KAAqB;IAGpC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAC1B,CAAC;YACC,IAAI;YAEJ,+BAA+B;YAC/B,IAAI,WAAW,SAAS,CAAC,EAAE,KAAK,QAAQ;gBACtC,iDAAiD;gBACjD,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,SAAS,wBAAwB,EAAE,QAAQ;gBACtE;YACF;YAEA,mCAAmC;YACnC,KAAK,MAAM,gBAAgB,WAAW,OAAO,CAAE;gBAC7C,KAAK,MAAM,WAAW,aAAa,QAAQ,CAAE;oBAC3C,IAAI,QAAQ,IAAI,CAAC,EAAE,KAAK,QAAQ;wBAC9B,OAAO,QAAQ,IAAI;wBACnB,eAAe;4BAAE,GAAG,IAAI;wBAAC;wBACzB;oBACF;gBACF;gBACA,IAAI,aAAa,UAAU,CAAC,EAAE,KAAK,QAAQ;oBACzC,OAAO,aAAa,UAAU;oBAC9B,eAAe;wBAAE,GAAG,IAAI;oBAAC;oBACzB;gBACF;YACF;QACF;oDACA;QAAC;QAAY;QAAU;KAAO;IAGhC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DACnC,CAAC;YACC,IAAI,CAAC,aAAa;YAElB,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;YAE5C,MAAM;iFAAa,CAAC;oBAClB,KAAK,QAAQ,IAAI;oBACjB,KAAK,SAAS,GAAG;oBACjB,KAAK,QAAQ,GAAG,SAAS,KAAK,YAAY;gBAC5C;;YAEA,KAAK,MAAM,gBAAgB,UAAU,OAAO,CAAE;gBAC5C,KAAK,MAAM,WAAW,aAAa,QAAQ,CAAE;oBAC3C,IAAI,QAAQ,IAAI,CAAC,EAAE,KAAK,YAAY,EAAE,EAAE;wBACtC,WAAW,QAAQ,IAAI;wBAEvB,MAAM,uBAAuB,QAAQ,QAAQ,CAAC,KAAK;uGACjD,CAAC,IAAM,EAAE,WAAW;;wBAEtB,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,QAAQ,IAAI,CAAC,QAAQ,IAAI,QAAQ,IAAI,CAAC,WAAW,KAAK,sBAAsB;4BACxG,MAAM,cAAc,aAAa,QAAQ,CAAC,IAAI;kGAC5C,CAAC,KAAO,GAAG,KAAK,KAAK,QAAQ,KAAK,GAAG;;4BAEvC,IAAI,aAAa;gCACf,YAAY,UAAU,GAAG;4BAC3B;wBACF;wBACA;oBACF;gBACF;gBAEA,IAAI,aAAa,UAAU,CAAC,EAAE,KAAK,YAAY,EAAE,EAAE;oBACjD,WAAW,aAAa,UAAU;oBAElC,MAAM,uBAAuB,aAAa,QAAQ,CAAC,KAAK;mGACtD,CAAC,KAAO,GAAG,QAAQ,CAAC,KAAK;2GAAC,CAAC,IAAM,EAAE,WAAW;6GAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,GAAG,IAAI,CAAC,WAAW;;oBAEjH,IAAI,CAAC,aAAa,UAAU,CAAC,QAAQ,IAAI,aAAa,UAAU,CAAC,QAAQ,IAAI,aAAa,UAAU,CAAC,WAAW,KAAK,sBAAsB;wBACzI,MAAM,aAAa,UAAU,OAAO,CAAC,IAAI;6FACvC,CAAC,IAAM,EAAE,KAAK,KAAK,aAAa,KAAK,GAAG;;wBAE1C,IAAI,YAAY;4BACd,WAAW,UAAU,GAAG;4BACxB,IAAI,WAAW,QAAQ,CAAC,MAAM,GAAG,GAAG;gCAClC,WAAW,QAAQ,CAAC,EAAE,CAAC,UAAU,GAAG;4BACtC;wBACF;oBACF;gBACF;YACF;YAEA,IAAI,UAAU,SAAS,CAAC,EAAE,KAAK,YAAY,EAAE,EAAE;gBAC7C,WAAW,UAAU,SAAS;gBAE9B,MAAM,sBAAsB,UAAU,OAAO,CAAC,KAAK;8FACjD,CAAC,IACC,EAAE,QAAQ,CAAC,KAAK;sGACd,CAAC,KACC,GAAG,QAAQ,CAAC,KAAK;8GAAC,CAAC,IAAM,EAAE,WAAW;gHAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,GAAG,IAAI,CAAC,WAAW;wGACtG,CAAC,EAAE,UAAU,CAAC,QAAQ,IAAI,EAAE,UAAU,CAAC,QAAQ,IAAI,EAAE,UAAU,CAAC,WAAW;;gBAGpF,IAAI,UAAU,SAAS,CAAC,QAAQ,IAAI,qBAAqB;oBACvD,UAAU,WAAW,CAAC,UAAU,GAAG;oBACnC,UAAU,WAAW,CAAC,cAAc,GAAG,IAAI,OACxC,WAAW,GACX,KAAK,CAAC,IAAI,CAAC,EAAE;oBAChB,UAAU,MAAM,GAAG;gBACrB;YACF;YAEA,qBAAqB;YACrB,eAAe;QACjB;6DACA;QAAC;QAAa;QAAY;KAAqB;IAGjD,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mEACxC,CAAC,UAAkB,WAAoB;YACrC,aAAa;YAEb,8CAA8C;YAC9C,mBAAmB;YACnB,IAAI,WAAW;gBACb,oBAAoB;YACtB;YACA,IAAI,WAAW;gBACb,oBAAoB;YACtB;YAEA;2EAAmB,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,SAAS,EAAE;oBAAK,CAAC;;YAC3D,IAAI,WAAW;gBACb;+EAAoB,CAAC,OAAS,CAAC;4BAAE,GAAG,IAAI;4BAAE,CAAC,UAAU,EAAE;wBAAK,CAAC;;YAC/D;YAEA,IAAI,WAAW;gBACb;+EAAoB,CAAC,OAAS,CAAC;4BAAE,GAAG,IAAI;4BAAE,CAAC,UAAU,EAAE;wBAAK,CAAC;;YAC/D;QACF;kEACA,EAAE;IAGJ,MAAM,oBAAoB,WAAW,OAAO,CAAC,MAAM,CACjD,CAAC,OAAO,SACN,QACA,OAAO,QAAQ,CAAC,MAAM,CACpB,CAAC,KAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAM,EAAE,WAAW,KAAK,GAAG,IAAI,CAAC,QAAQ,EACnE,MAAM,EACV;IAGF,MAAM,gBAAgB,WAAW,OAAO,CAAC,MAAM,CAC7C,CAAC,OAAO,SAAW,QAAQ,OAAO,QAAQ,CAAC,MAAM,EACjD;IAEF,MAAM,kBACJ,gBAAgB,IAAI,AAAC,oBAAoB,gBAAiB,MAAM;IAElE,MAAM,kBAAkB;QACtB;YAAE,IAAI;YAAU,OAAO;YAAiB,MAAM,+NAAA,CAAA,iBAAY;QAAC;QAC3D;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,mNAAA,CAAA,YAAY;QAAC;QACxD;YAAE,IAAI;YAAQ,OAAO;YAAc,MAAM,wNAAA,CAAA,cAAU;QAAC;QACpD;YAAE,IAAI;YAAe,OAAO;YAAc,MAAM,wNAAA,CAAA,cAAS;QAAC;KAC3D;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAM,CAAC,YAAY,EAAE,UAAU;8DACnC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAK,WAAU;kEAC1C,cAAA,6LAAC,iOAAA,CAAA,kBAAa;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAG7B,6LAAC,8NAAA,CAAA,iBAAY;oDAAC,WAAU;;;;;;8DACxB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,WAAW,IAAI;;;;;;sEAElB,6LAAC;4DAAE,WAAU;sEAAyB,WAAW,IAAI;;;;;;;;;;;;;;;;;;sDAGzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAe,KAAK,KAAK,CAAC;gEAAiB;;;;;;;;;;;;;8DAE7D,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,OAAO;oDAAiB,WAAU;;;;;;8DAC5C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;sEAAM,WAAW,UAAU;;;;;;;;;;;;8DAE9B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;sEAAM,WAAW,OAAO;;;;;;;;;;;;8DAE3B,6LAAC,oIAAA,CAAA,QAAK;oDACJ,SACE,WAAW,MAAM,KAAK,cAClB,YACA;oDAEN,WAAU;8DAET,WAAW,MAAM,KAAK,cACnB,YACA;;;;;;;;;;;;;;;;;;gCAKT,+BACC,6LAAC,uJAAA,CAAA,kBAAe;oCACd,QAAQ;oCACR,YAAY;oCACZ,iBAAiB;oCACjB,kBAAkB;oCAClB,gBAAgB;oCAChB,iBAAiB;oCACjB,oBAAoB;;;;;yDAGtB;;sDACE,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,6LAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAC;gDACpB,MAAM,OAAO,KAAK,IAAI;gDACtB,qBACE,6LAAC;oDAEC,SAAS,IAAM,aAAa,KAAK,EAAE;oDACnC,WAAW,CAAC,oFAAoF,EAC9F,cAAc,KAAK,EAAE,GACjB,oDACA,kCACJ;;sEAEF,6LAAC;4DAAK,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAuB,KAAK,KAAK;;;;;;;mDAT5C,KAAK,EAAE;;;;;4CAYlB;;;;;;;;;;;;;;;;;;;kCAQV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;oCAAC,OAAO;oCAAW,eAAe;oCAAc,WAAU;;sDAC7D,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAS,WAAU;sDACpC,cAAA,6LAAC,qJAAA,CAAA,YAAS;gDACR,YAAY;gDACZ,iBAAiB;gDACjB,kBAAkB;gDAClB,kBAAkB;gDAClB,gBAAgB;gDAChB,iBAAiB;gDACjB,iBAAiB;gDACjB,yBAAyB;gDACzB,aAAa;gDACb,qBAAqB;gDACrB,oBAAoB;gDACpB,sBAAsB;gDACtB,6BAA6B;gDAC7B,+BAA+B;gDAC/B,uBAAuB,IAAM,aAAa;gDAC1C,gBAAgB;gDAChB,iBAAiB;gDACjB,kBAAkB;gDAClB,kBAAkB;;;;;;;;;;;sDAItB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAW,WAAU;sDACtC,cAAA,6LAAC,uJAAA,CAAA,cAAW;gDACV,YAAY;gDACZ,iBAAiB;;;;;;;;;;;sDAIrB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAO,WAAU;sDAClC,cAAA,6LAAC,mJAAA,CAAA,UAAO;gDAAC,YAAY;gDAAY,aAAa;;;;;;;;;;;sDAGhD,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAc,WAAU;sDACzC,cAAA,6LAAC,0JAAA,CAAA,iBAAc;gDACb,YAAY;gDACZ,aAAa;oDACX,IAAI;oDACJ,MAAM;oDACN,WAAW;oDACX,SAAS;oDACT,qBAAqB;wDACnB,cAAc;wDACd,gBAAgB;wDAChB,eAAe;wDACf,gBAAgB;oDAClB;gDACF;gDACA,iBAAiB;gDACjB,uBAAuB,IAAM,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU7D,6BACC,6LAAC,6IAAA,CAAA,YAAS;gBACR,MAAM;gBACN,QAAQ;gBACR,YAAY;gBACZ,SAAS,IAAM,eAAe;;;;;;;;;;;;AAKxC;GA7dM;;QACW,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QAE2C,4IAAA,CAAA,gBAAa;;;KALxF;uCA+dS", "debugId": null}}]}